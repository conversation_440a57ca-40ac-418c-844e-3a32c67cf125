/**
 * Interfaces for the Car Code Alarm Config
 */

/**
 * Car code alarm config data
 */
export interface CarCodeAlarmConfigDAO {
  car_code: string;
  name: string;
  description?: string;
  category?: string;
  device_type?: string;
  should_raise_alarm: boolean;
  updated_at?: string; // ISO 8601 format
  updated_by?: string;
}

/**
 * Request for creating car code alarm configs (gRPC)
 */
export interface CreateCarCodeAlarmConfigsRequest {
  requests: CarCodeAlarmConfigDAO[];
  user_id: string; // User ID for ACL check
}

/**
 * Request for finding car code alarm configs
 */
export interface FindCarCodeAlarmConfigsRequest {
  car_code?: string;
  limit?: number; // Maximum number of records to return
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  user_id: string; // User ID for ACL check
}

/**
 * Request for finding car code alarm config history
 */
export interface FindCarCodeAlarmConfigHistoryRequest {
  car_code?: string;
  start_date?: string; // ISO 8601 format
  end_date?: string; // ISO 8601 format
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  limit?: number; // Maximum number of records to return
  user_id: string; // User ID for ACL check
}

/**
 * Type for updating a car code alarm config
 * Requires car_code and updated_by, all other fields are optional
 */
export interface UpdateCarCodeAlarmConfigDTO {
  car_code: string;
  updated_by: string;
  name?: string;
  description?: string;
  category?: string;
  device_type?: string;
  should_raise_alarm?: boolean;
  updated_at?: string;
}

/**
 * Request for updating a car code alarm config
 */
export interface UpdateCarCodeAlarmConfigRequest {
  request: UpdateCarCodeAlarmConfigDTO;
  user_id: string; // User ID for ACL check
}

/**
 * Request for deleting a car code alarm config
 */
export interface DeleteCarCodeAlarmConfigRequest {
  car_code: string;
  updated_by: string;
  user_id: string; // User ID for ACL check
}

/**
 * Success response for car code alarm config operations
 */
export interface CarCodeAlarmConfigsSuccessResponse {
  status: string;
  code: number;
  message: CarCodeAlarmConfigDAO[];
}

/**
 * Error response for car code alarm config operations
 */
export interface CarCodeAlarmConfigsErrorResponse {
  status: string;
  code: number;
  message: string[];
  fields: string[];
  error: string;
}

/**
 * Response for car code alarm config operations
 */
export interface CarCodeAlarmConfigsResponse {
  success?: CarCodeAlarmConfigsSuccessResponse;
  error?: CarCodeAlarmConfigsErrorResponse;
}

/**
 * Ping response
 */
export interface PingResponse {
  message: string;
}

/**
 * Query parameters for filtering car code alarm configs
 */
export interface CarCodeAlarmConfigQueryParams {
  car_code?: string;
  limit?: number; // Maximum number of records to return
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
}
