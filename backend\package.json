{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build || ./node_modules/.bin/nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "nest start --watch", "start:hmr": "nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "start:docker:dev": "NODE_ENV=development DOCKER_ENVIRONMENT=true WEBPACK_WATCH=true CHOKIDAR_USEPOLLING=true nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:docker:prod": "NODE_ENV=production DOCKER_ENVIRONMENT=true node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix --format stylish", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js --config typeorm-cli.config.ts", "migration:generate": "npm run typeorm -- migration:generate -n", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@grpc/grpc-js": "^1.8.0", "@grpc/proto-loader": "^0.7.6", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/microservices": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/typeorm": "^11.0.0", "dotenv": "^16.4.7", "google-protobuf": "^3.21.2", "grpc-server-reflection": "^0.1.5", "pg": "^8.11.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.4.2", "run-script-webpack-plugin": "^0.2.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}