# Shared Test Data Configuration

This directory contains shared configuration files for test data generation across different modules.

## test_data_config.json

**IMPORTANT: This is the single source of truth for test data configuration.**

This file contains standardized test data values that must be used consistently across all test data generators, including:

- Car codes with their monitoring periods
- Site object IDs
- Site-to-car-code assignments

Rather than duplicating this information in READMEs or hardcoding values in multiple scripts, all test components should reference this file. This ensures consistency and simplifies maintenance.

### Usage in Python

```python
import json
import os
import random

# Get the path to the shared test data config
script_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(script_dir), 'shared')
config_path = os.path.join(shared_dir, 'test_data_config.json')

# Load the shared test data config
with open(config_path, 'r') as f:
    test_data_config = json.load(f)

# Get the car codes and site object IDs
car_codes = list(test_data_config['car_codes'].keys())
site_object_ids = list(test_data_config['site_object_ids'].keys())

# Use the values in your test data generation
random_car_code = random.choice(car_codes)
random_site_id = random.choice(site_object_ids)

# Or use the site-car code assignments
assignments = test_data_config['site_car_code_assignments']
random_assignment = random.choice(assignments)
site_id = random_assignment['site_id']
car_code = random_assignment['car_code']
```

### Usage in JavaScript/Node.js

```javascript
const fs = require('fs');
const path = require('path');

// Get the path to the shared test data config
const scriptDir = __dirname;
const sharedDir = path.join(path.dirname(scriptDir), 'shared');
const configPath = path.join(sharedDir, 'test_data_config.json');

// Load the shared test data config
const testDataConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// Get the car codes and site object IDs
const carCodes = Object.keys(testDataConfig.car_codes);
const siteObjectIds = Object.keys(testDataConfig.site_object_ids);

// Use the values in your test data generation
const randomCarCode = carCodes[Math.floor(Math.random() * carCodes.length)];
const randomSiteId = siteObjectIds[Math.floor(Math.random() * siteObjectIds.length)];

// Or use the site-car code assignments
const assignments = testDataConfig.site_car_code_assignments;
const randomAssignment = assignments[Math.floor(Math.random() * assignments.length)];
const siteId = randomAssignment.site_id;
const carCode = randomAssignment.car_code;
```

## Benefits of Shared Test Data

- **Consistency**: All test data generators use the same values
- **Maintainability**: Update values in one place
- **Reusability**: Share test data configuration across different modules
- **Compatibility**: Ensure test data works with seeded configurations
