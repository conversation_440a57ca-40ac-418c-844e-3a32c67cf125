import { Logger } from '@nestjs/common';
import { ServiceResponse } from './service-utils';

/**
 * Common controller utility functions for handling service responses and errors
 */

const logger = new Logger('ControllerUtils');

/**
 * Generic interface for controller error responses
 */
export interface ErrorResponse {
  status: string;
  code: number;
  message: any[];
  fields: string[];
  error: string;
}

/**
 * Generic interface for controller success responses
 */
export interface SuccessResponse<T> {
  status: string;
  code: number;
  message: T[];
}

/**
 * Generic interface for controller responses
 */
export interface ControllerResponse<T> {
  success?: SuccessResponse<T>;
  error?: ErrorResponse;
}

/**
 * Helper function to handle common error processing logic in controllers
 * For access control errors, use createAccessDeniedError(user_id) below.
 *
 * @param result The service response
 * @param operationType The type of operation (update, delete, etc.)
 * @param idFields Optional array of field names used for identification (e.g., ['car_code', 'site_object_id'])
 * @returns The formatted error response
 */
export function handleControllerError<T>(
  result: ServiceResponse<T>,
  operationType: string,
  idFields: string[] = ['car_code'],
): ControllerResponse<T> {
  // Create the error object based on the error type
  let errorObj: ErrorResponse;

  // Check for unexpected result structure
  if (!result.success && !result.error) {
    logger.error(
      `Unexpected result structure from service during ${operationType} operation`,
      result,
    );
    errorObj = {
      status: 'ERROR: Internal Server Error',
      code: 500,
      message: [],
      fields: [],
      error: 'Unexpected result structure from service',
    };
  }
  // Check if it's a not found error (404 Not Found)
  else if (result.error && result.error.includes('not found')) {
    errorObj = {
      status: 'ERROR: Not Found - no resources to process',
      code: 404,
      message: result.errors || [],
      fields: idFields,
      error: result.error,
    };
  }
  // Check if it's an audit logging failure (422 Unprocessable Entity)
  else if (result.error && result.error.includes('Audit logging failed')) {
    errorObj = {
      status: 'ERROR: Audit logging failure',
      code: 422,
      message: [],
      fields: [],
      error: result.error,
    };
  }
  // Handle other errors with 400 Bad Request
  else {
    errorObj = {
      status: 'ERROR: Bad Request - client-provided data caused the error',
      code: 400,
      message: result.errors || [],
      fields: [],
      error: result.error || `Unknown error during ${operationType}`,
    };
  }

  // Return the appropriate response format based on the responseType
  return {
    error: errorObj,
  };
}

/**
 * Helper function to handle validation of required fields
 *
 * @param missingFields Array of missing field names
 * @param operationType The type of operation (update, delete, create, etc.)
 * @param logger Logger instance to use for logging errors
 * @param context Optional context information (e.g., "item 1/5")
 * @returns The formatted error response for validation failures
 */
export function handleMissingFieldsError<T>(
  missingFields: string[],
  operationType: string,
  logger: Logger,
  context?: string,
): ControllerResponse<T> {
  if (missingFields.length > 0) {
    // Create a simple error message for the response
    const errorMessage = `The ${missingFields.join(', ')} fields are required.`;

    // Create a more detailed message for logging
    const logMessage = context
      ? `Missing required fields for ${operationType} ${context}: ${missingFields.join(', ')}`
      : `Missing required fields for ${operationType}: ${missingFields.join(', ')}`;

    logger.error(logMessage);

    return {
      error: {
        status: 'ERROR: Unprocessable Entity - semantic errors',
        code: 422,
        message: [],
        fields: missingFields,
        error: errorMessage,
      },
    };
  }

  // This should never be reached if the function is used correctly
  throw new Error(
    'handleValidationErrors called with empty missingFields array',
  );
}
