#!/usr/bin/env python3
"""
Generate dummy data for testing the car-code-config API.

This script generates realistic test data for the car-code-config API in gRPC format
for testing with grpcurl.

Usage:
    python generate_car_code_config_test_data.py [OPTIONS]

Options:
    --count     Number of car code configurations to generate (default: 10)
    --output    Output file name (default: car_code_configs_grpc.json)
    --output-dir Output directory (default: output)
    --port      gRPC server port (default: 8081)
"""

import argparse
import json
import random
import datetime
import os
import sys
from typing import List, Dict, Any


# Load the shared test data config
def load_test_data_config():
    """Load the shared test data configuration."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shared_dir = os.path.join(os.path.dirname(script_dir), 'shared')
    config_path = os.path.join(shared_dir, 'test_data_config.json')

    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Shared test data config not found at {config_path}")
        print("This file is required for test data generation.")
        print("Please ensure the file exists before running this script.")
        sys.exit(1)


# Global test data config
TEST_DATA_CONFIG = load_test_data_config()

# Probability of omitting thresholds and recommendation fields (0.0 to 1.0)
# 0.25 means 25% chance of being omitted
OMIT_FIELD_PROBABILITY = 0.0


def generate_car_code_config() -> Dict[str, Any]:
    """Generate a single car code configuration with realistic data matching the CarCodeConfigDAO interface.

    Note: This function selects a random site-car code assignment from the shared config,
    but the site_id and car_code may be overridden by the calling function to ensure
    strict adherence to the shared test data configuration.
    """
    # Use a random site-car code assignment from the shared config
    assignment = random.choice(TEST_DATA_CONFIG['site_car_code_assignments'])
    site_id = assignment['site_object_id'].lower()  # Ensure site_id is lowercase
    car_code = assignment['car_code'].lower()  # Ensure car_code is lowercase

    # Get the monitoring period from the config and use it as monitoring_window
    monitoring_window = TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period']

    # Generate thresholds as a list of objects (legacy format), always present
    threshold_types = ["temperature", "voltage", "current", "power", "efficiency"]
    units = ["%", "V", "A", "W", "kWh"]
    thresholds = []
    for _ in range(random.randint(1, 3)):
        threshold_type = random.choice(threshold_types)
        threshold_unit = random.choice(units)
        threshold_value = round(random.uniform(10, 100), 2)
        thresholds.append({
            "name": threshold_type,
            "unit": threshold_unit,
            "value": str(threshold_value)
        })

    # Generate recommendations (actions) as a list of objects (legacy format), always present
    possible_actions = [
        "reset missing inverter",
        "replace inverter",
        "check inverter at site",
        "inspect power distribution system",
        "review recent performance data",
        "clean panels and check for debris",
        "diagnose and repair sensor",
        "verify hardware configuration",
        "inspect for physical damage",
        "optimize system settings",
        "test inverter functionality",
        "monitor system for 24 hours"
    ]
    recommendation = []
    for _ in range(random.randint(1, 3)):
        action = random.choice(possible_actions)
        man_hour = str(random.randint(0, 8))
        num_of_man = str(random.randint(1, 4))
        equipment_cost = str(random.choice([0, 50, 100, 500, 1000, 3000]))
        recommendation.append({
            "name": action,
            "man_hour": man_hour,
            "num_of_man": num_of_man,
            "equipment_cost": equipment_cost
        })

    # Generate a timestamp for updated_at
    days_ago = random.randint(0, 30)
    updated_at = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

    # Convert car_code to lowercase
    car_code_lower = car_code.lower()

    # Create the car code config object matching the CarCodeConfigDAO interface
    config = {
        "site_object_id": site_id,
        "car_code": car_code_lower,
        "monitoring_window": monitoring_window,
        "enabled": random.choice([True, False]),  # Boolean value as required by the interface
        "updated_at": updated_at,
        "updated_by": "test-script"
    }

    # Always add thresholds and recommendation as lists
    import json
    config["thresholds"] = json.dumps(thresholds)
    config["recommendation"] = json.dumps(recommendation)

    return config


def generate_car_code_configs(count: int) -> List[Dict[str, Any]]:
    """Generate multiple car code configurations.

    If count is less than or equal to the number of available site-car code assignments,
    uses a random subset of the assignments. Otherwise, reuses assignments with different
    other attributes (thresholds, recommendation, etc.) to reach the desired count.
    """
    assignments = TEST_DATA_CONFIG['site_car_code_assignments']
    configs = []

    if count <= len(assignments):
        # Use a random subset of the assignments
        selected_assignments = random.sample(assignments, count)

        for assignment in selected_assignments:
            site_id = assignment['site_object_id']
            car_code = assignment['car_code']

            # Create a config based on this assignment
            config = generate_car_code_config()
            config['site_object_id'] = site_id.lower()
            config['car_code'] = car_code.lower()
            # Ensure monitoring_window is set from the monitoring_period in the config
            config['monitoring_window'] = TEST_DATA_CONFIG['car_codes'][car_code.lower()]['monitoring_period']

            configs.append(config)
    else:
        # Generate one config for each assignment first
        for assignment in assignments:
            site_id = assignment['site_object_id']
            car_code = assignment['car_code']

            # Create a config based on this assignment
            config = generate_car_code_config()
            config['site_object_id'] = site_id.lower()
            config['car_code'] = car_code.lower()
            # Ensure monitoring_window is set from the monitoring_period in the config
            config['monitoring_window'] = TEST_DATA_CONFIG['car_codes'][car_code.lower()]['monitoring_period']

            configs.append(config)

        # If we need more configs, cycle through the assignments again with different other attributes
        remaining = count - len(assignments)
        while remaining > 0:
            for assignment in assignments:
                if remaining <= 0:
                    break

                site_id = assignment['site_object_id']
                car_code = assignment['car_code']

                # Create a new config with the same site_id and car_code but different other attributes
                config = generate_car_code_config()
                config['site_object_id'] = site_id.lower()
                config['car_code'] = car_code.lower()
                # Ensure monitoring_window is set from the monitoring_period in the config
                config['monitoring_window'] = TEST_DATA_CONFIG['car_codes'][car_code.lower()]['monitoring_period']

                configs.append(config)
                remaining -= 1

    return configs


def save_as_json(configs: List[Dict[str, Any]], output_file: str) -> None:
    """Save car code configurations as a plain JSON array."""
    with open(output_file, 'w') as f:
        json.dump(configs, f, indent=2)


def main():
    parser = argparse.ArgumentParser(description='Generate dummy data for car-code-config API testing')
    parser.add_argument('--count', type=int, default=10, help='Number of car code configurations to generate')
    parser.add_argument('--output', type=str, default='car_code_configs_grpc.json', help='Output file name')
    parser.add_argument('--output-dir', type=str, default='output',
                       help='Output directory (default: output)')
    parser.add_argument('--port', type=int, default=8081, help='gRPC server port (default: 8081)')

    args = parser.parse_args()

    # Create output directory with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, timestamp)
    os.makedirs(output_path, exist_ok=True)

    print(f"Output directory: {output_path}")

    # Generate car code configs
    configs = generate_car_code_configs(args.count)

    # Save as plain JSON
    json_file = os.path.join(output_path, args.output)
    print(f"Generating test data ({args.count} records)...")
    save_as_json(configs, json_file)

    # Print summary
    print("\nDone! Test data file has been created in", output_path)
    print("")
    print("Files generated:")
    print(f"- {json_file}: Car code configurations")



if __name__ == "__main__":
    main()
