# Car Wrapper Service

A NestJS-based gRPC microservice for handling car wrapper operations.
This service processes CAR cases from synapse, with endpoints served via gRPC.
The protobuf definitions can be found in the `/backend/src/proto` folder.



<img src="./car-wrapper-service-icon.png" alt="car-wrapper-service-icon" width="200" />

## Table of Contents

- [Overview](#overview)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Environment Configuration](#environment-configuration)
- [Running the Application](#running-the-application)
  - [Development Mode](#development-mode)
  - [Auto-Rebuild Options](#auto-rebuild-options)
  - [Docker / Podman Development Mode](#docker--podman-development-mode)
  - [Production Mode](#production-mode)
  - [Docker / Podman Production Mode](#docker--podman-production-mode)
- [Testing](#testing)
  - [Unit Tests](#unit-tests)
  - [Test Data Generation](#test-data-generation)
- [gRPC API](#grpc-api)
  - [Available Services](#available-services)
  - [Using gRPC Tools](#using-grpc-tools)
- [Docker Configuration](#docker-configuration)
  - [Docker Compose Files](#docker-compose-files)
  - [Docker Environment Variables](#docker-environment-variables)
  - [Accessing Services](#accessing-services)
  - [Running Individual Services](#running-individual-services)
  - [Docker Troubleshooting](#docker-troubleshooting)
  - [Viewing Docker Logs](#viewing-docker-logs)
  - [Connecting to CloudBeaver](#connecting-to-cloudbeaver)
- [Deployment](#deployment)
  - [Environment Variables](#environment-variables)
  - [Docker Deployment](#docker-deployment)
  - [Kubernetes Deployment](#kubernetes-deployment)
- [Git Hooks](#git-hooks)
  - [Available Hooks](#available-git-hooks)
  - [Setup Instructions](#git-hooks-setup)
- [Database Schema Management](#database-schema-management)
- [Additional Resources](#additional-resources)
- [Documentation & Presentations](#documentation--presentations)

## Overview

This is a NestJS microservice that provides a gRPC API for handling car wrapper operations. It uses TypeORM with PostgreSQL for data storage and serves as a backend service for processing car-related data.

## Project Structure

```
/
├── backend/                # NestJS gRPC microservice
│   ├── src/                # Source code
│   │   ├── car-case/       # Car case module (main business logic)
│   │   │   ├── controller/         # Controllers for car case API endpoints
│   │   │   │   ├── car-case.controller.grpc.ts  # gRPC controller implementation
│   │   │   │   └── car-case.controller.rest.ts   # REST controller for read-only endpoints
│   │   │   ├── entities/   # Car case database entities
│   │   │   ├── interfaces/ # Car case data transfer objects and interfaces
│   │   │   └── services/   # Business logic services
│   │   │       └── car-case-conso.service.ts # Consolidated view service
│   │   ├── car-code-config/ # Car code configuration module
│   │   │   ├── entities/   # Car code config database entities
│   │   │   └── interfaces/ # Car code config data transfer objects and interfaces
│   │   ├── car-code-config-history/ # History tracking for car code configs
│   │   │   ├── entities/   # History database entities
│   │   │   └── interfaces/ # History data transfer objects and interfaces
│   │   ├── car-code-alarm-config/ # Car code alarms config module
│   │   │   ├── controller/ # Controllers for car code alarm config endpoints
│   │   │   ├── entities/   # Car code alarms config database entities
│   │   │   └── interfaces/ # Car code alarm config data transfer objects
│   │   ├── car-code-alarm-config-history/ # History tracking for car code alarms config
│   │   │   ├── entities/   # History database entities
│   │   │   └── interfaces/ # History data transfer objects and interfaces
│   │   ├── client/         # Help to communicate with external gRPC services
│   │   │   ├── acl/        # ACL microservice
│   │   │   └── interfaces/ # Shared interfaces for external systems
│   │   ├── common/         # Common utilities and helpers
│   │   │   └── utils/      # Utility functions (filters, type safety, etc.)
│   │   ├── config/         # Configuration files
│   │   ├── interfaces/     # Shared interfaces
│   │   ├── migrations/     # Database migrations
│   │   ├── proto/          # Protocol buffer definitions
│   │   ├── app.module.ts   # Main application module
│   │   └── main.ts         # Application entry point
│   ├── test/               # Test files
│   │   ├── car-case/       # Car case test scripts and data generators
│   │   ├── car-code-config/ # Car code config test data generators
│   │   ├── car-code-alarm-config/ # Car code alarm config test data generators
│   │   └── shared/         # Shared test utilities and configurations
│   ├── docker-compose.yml  # Production Docker Compose
│   └── docker-compose.dev.yml # Development Docker Compose
└── docs/                   # Documentation files
    ├── api/                # API documentation
    │   └── API_DOCS.yaml   # OpenAPI specification
    ├── planning/           # Project planning documents
    │   ├── DESIGN_DOCS.md  # Architecture and implementation details
    ├── guides/             # User and developer guides
    │   └── VSCODE_FIX_DOCS.md # VS Code troubleshooting guide
    ├── presentations/      # Presentation materials (Marp format)
    │   └── car-wrapper-features.md # Feature highlights presentation
    └── DOCS_GUIDE.md       # Documentation guide
```

## Getting Started

### Prerequisites

- Node.js (v22+)
- npm or yarn
- PostgreSQL database
- Docker or Podman; *Recommended: Podman for setup*
- Command Line & Git Bash
- gRPC tools for testing (optional)

### Installation

1. Clone the repository
2. Navigate to the backend directory:
   ```bash
   cd backend
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Set up your environment configuration (see below)

### Environment Configuration

Create a `.env` file in the backend directory based on the `.env.example`:

```bash
# gRPC Server Configuration
GRPC_HOST=0.0.0.0
GRPC_PORT=8081

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=root_password
DB_NAME=postgres
POSTGRES_CONNECTION_STRING=postgres://user:root_password@localhost:5432/postgres

# Node Environment
NODE_ENV=development
```

## Running the Application

### Development Mode

Standard development mode:

```bash
cd backend
npm run start
```

### Auto-Rebuild Options

#### Basic Auto-Rebuild (Watch Mode)

Uses Nest.js's built-in watch mode to automatically rebuild when files change:

```bash
cd backend
npm run start:dev
```

#### Advanced Auto-Rebuild (Hot Module Replacement)

Uses webpack with Hot Module Replacement for faster development:

```bash
cd backend
npm run start:hmr
```

### Docker / Podman Development Mode

For development with Docker and Hot Module Replacement (HMR):

```bash
cd backend
podman compose -f docker-compose.dev.yml up
# Or with build flag to ensure latest changes are included
podman compose -f docker-compose.dev.yml up --build
```

This will:
- Start a PostgreSQL database
- Start CloudBeaver for database management (accessible at http://localhost:8978)
- Start the NestJS application with HMR enabled
- Mount your local code into the container for live updates

#### Hot Module Replacement in Docker

The Docker development environment is configured with enhanced file watching to detect code changes even within the container:

- **Polling-based File Watching**: The webpack configuration uses aggressive polling to detect file changes in the Docker environment
- **Environment Variables**: `CHOKIDAR_USEPOLLING=true` and `WEBPACK_WATCH=true` are set to enable proper file watching
- **Automatic Rebuilding**: When you change a file in your local directory, the changes are detected and the application rebuilds automatically
- **Docker-specific Configuration**: The webpack configuration automatically detects Docker environments and adjusts polling settings accordingly

#### Verifying HMR is Working

To verify that HMR is working correctly:

1. Start the Docker development environment
2. Make a change to a controller or service file
3. Save the file
4. Check the logs for messages about modules being rebuilt:
   ```bash
   podman compose -f docker-compose.dev.yml logs -f car-wrapper-service-backend
   ```
5. You should see output indicating that webpack detected the change and is rebuilding, with messages like:
   ```
   🔥 HMR enabled with file polling for Docker environment
   🐳 Running in Docker environment
   👀 File watching with polling is enabled for Docker
   ⚡ Webpack watch mode is enabled
   ```

If HMR is not working, check the troubleshooting section below.

### Production Mode

For production deployment without Docker:

```bash
cd backend
npm run build
npm run start:prod
```

### Docker / Podman Production Mode

For production deployment with Docker:

```bash
cd backend
podman compose -f docker-compose.yml up
```

This starts:
- PostgreSQL database
- Car Wrapper Service backend

To stop the production environment:

```bash
cd backend
podman compose -f docker-compose.yml down
```

## Testing

### Unit Tests

Run unit tests:

```bash
cd backend
npm run test
```

Run specific tests:

```bash
cd backend
npm run test -- car-case.service
npm run test -- car-case.controller
```

### Test Data Generation

The project includes scripts to generate test data for the Car Case service. These scripts create sample car case data in various formats for testing the gRPC API.

```bash
cd backend/test/car-case
./generate_car_case_test_data.sh --format=all
```

For detailed instructions on using the test data generator, see the dedicated documentation at:
`backend/test/car-case/README_TEST_DATA.md`

## gRPC API

### Authentication

**Important**: All gRPC requests require authentication via the `user_id` field in the request payload. This can be either:

- A valid UUID of a Sembcorp user for regular access with permission checks
- The microservice secret key (`aCHW7ds8xn`) for full read and write access without permission checks

Failure to provide a valid `user_id` will result in authentication errors.

### Available Services

The microservice provides the following gRPC services:

- **CarCaseService**:
  - `pingCarCase`: Health check for the car case service
  - `createCarCase`: Create one or more car cases
  - `findCarCases`: Find car cases with filters (site_object_id, car_code, date range, status)
  - `findCarCasesByIds`: Find car cases by their IDs

- **CarCodeConfigService**:
  - `pingCarCodeConfig`: Health check for the car code config service
  - `createCarCodeConfig`: Create one or more car code configurations
  - `findCarCodeConfigs`: Find car code configurations with filters
  - `findCarCodeConfigsByIds`: Find car code configurations by their IDs

- **CarCodeAlarmConfigService**:
  - `pingCarCodeAlarmConfig`: Health check for the car code alarm config service
  - `createCarCodeAlarmConfig`: Create one or more car code alarm configurations
  - `findCarCodeAlarmConfigs`: Find car code alarm configurations with filters
  - `findCarCodeAlarmConfigsByIds`: Find car code alarm configurations by their IDs

### Sample Payloads

You can test these endpoints using gRPC tools, Postman (with gRPC support), or any other gRPC client. The service endpoint is `grpc://localhost:8081` when running locally.

#### CarWrapperService/FindCarCodeAlarmConfigs


grpc method: `FindCarCodeAlarmConfigs()`
```json
{
  "car_code": "hti",
  "user_id": "aCHW7ds8xn"
}
```

#### CarWrapperService/CreateCarCase

grpc method: `CreateCarCase()`
```json
{
  "car_cases": [
    {
      "site_object_id": "site123",
      "car_code": "hti",
      "source": "synapse",
      "title": "High Temperature Issue",
      "description": "Temperature exceeds threshold",
      "tags": "temperature,alert"
    }
  ],
  "user_id": "aCHW7ds8xn"
}
```

#### CarWrapperService/FindCarCases

grpc method: `FindCarCases()`
```json
{
  "site_object_id": "site123",
  "car_code": "hti",
  "status": "open",
  "limit": 10,
  "sort_order": "DESC",
  "user_id": "aCHW7ds8xn"
}
```

#### Testing with Postman

Postman supports gRPC requests. To test these endpoints in Postman:

1. Create a new gRPC request
2. Enter the server URL: `localhost:8081`
3. Select the service and method from the dropdown
4. Enter the request message in the "Message" tab
5. Click "Invoke" to send the request

Remember to include the `user_id` field in all requests for authentication.

### Using gRPC Tools

#### Command Line with grpc-tools.sh

You can use the provided `grpc-tools.sh` script to interact with the gRPC service:

```bash
# List all services
grpc-tools list

# List methods for a service
grpc-tools methods CarWrapperService

# Call a method with authentication
grpc-tools call CarWrapperService/FindCarCodeAlarmConfigs '{"car_code": "hti", "user_id": "aCHW7ds8xn"}'
grpc-tools call CarWrapperService/PingCarCase '{"user_id": "aCHW7ds8xn"}'
```

#### Using gRPC Web UI

You can also use gRPC web clients to interact with the service at endpoint `grpc://localhost:8081`:

```
Endpoint: grpc://localhost:8081
Service: CarWrapperService
Method: FindCarCodeAlarmConfigs
Payload: {"car_code": "hti", "user_id": "aCHW7ds8xn"}
```

## Docker Configuration

### Docker Compose Files

The project includes two Docker Compose files for different environments:

1. **docker-compose.yml**: Production build testing configuration
   - Located in the `backend/` directory
   - Includes only essential services: PostgreSQL and car-wrapper-service-backend
   - Optimized for performance and security
   - Uses the production Dockerfile
   - Used for testing the production build locally, not for actual production deployment

2. **docker-compose.dev.yml**: Development configuration
   - Located in the `backend/` directory
   - Includes additional development tools: CloudBeaver
   - Configures volume mounts for hot reloading
   - Uses the local.Dockerfile with HMR enabled

Note: For actual production deployment, only the Dockerfile is used with Kubernetes (see the Kubernetes Deployment section below).

### Docker Environment Variables

The Docker setup uses different environment files and scripts:

- `.env.docker`: Used for both development and production in Docker
- For development: Uses `start:docker:dev` script with HMR enabled
- For production: Uses `start:docker:prod` script optimized for production

### Accessing Services

The following table shows which services are available in development and production environments:

| Service                         | URL                         | Development | Production | Description                           |
|---------------------------------|-----------------------------|-------------|------------|---------------------------------------|
| **car-wrapper-service-backend** | grpc://localhost:8081      | ✅           | ✅          | The main gRPC service                 |
| **PostgreSQL**                  | postgresql://localhost:5432 | ✅           | ✅          | Database for storing application data |
| **CloudBeaver**                 | http://localhost:8978       | ✅           | ❌          | Database management UI                |

Development environment is started with: `podman compose -f docker-compose.dev.yml up`
Production environment is started with: `podman compose up`

The development-only tool (CloudBeaver) is intentionally excluded from the production environment to minimize resource usage and attack surface.

### Running Individual Services

You can run individual services from the Docker Compose files instead of starting the entire stack. This is useful for:
- Testing a specific service in isolation
- Running only the database for local development
- Running only CloudBeaver for database management
- Conserving system resources

#### Running Individual Services in Development Mode

```bash
cd backend

# Start all services (default)
podman compose -f docker-compose.dev.yml up

# Start only PostgreSQL
podman compose -f docker-compose.dev.yml up postgresdb

# Start only CloudBeaver (database management UI)
podman compose -f docker-compose.dev.yml up cloudbeaver

# Start only the NestJS backend service
podman compose -f docker-compose.dev.yml up car-wrapper-service-backend

# Start multiple specific services
podman compose -f docker-compose.dev.yml up postgresdb cloudbeaver
```

#### Running Individual Services in Production Mode

```bash
cd backend

# Start only PostgreSQL
podman compose -f docker-compose.yml up postgresdb

# Start only the NestJS backend service
podman compose -f docker-compose.yml up car-wrapper-service-backend-prod
```

#### Important Notes When Running Individual Services

1. **Dependencies**: Some services depend on others. For example:
   - CloudBeaver depends on PostgreSQL
   - The NestJS backend depends on PostgreSQL

   If you start a service without its dependencies, you may see connection errors.

2. **Starting Dependencies Automatically**: You can use the `--scale` option to start a service without its dependencies:
   ```bash
   # Start CloudBeaver without automatically starting PostgreSQL
   podman compose -f docker-compose.dev.yml up cloudbeaver --scale postgres=0
   ```

3. **Building Before Starting**: If you've made changes to the service code, build before starting:
   ```bash
   podman compose -f docker-compose.dev.yml build car-wrapper-service-backend
   podman compose -f docker-compose.dev.yml up car-wrapper-service-backend
   ```

4. **Detached Mode**: Run services in the background with the `-d` flag:
   ```bash
   podman compose -f docker-compose.dev.yml up -d postgres
   ```

5. **Stopping Individual Services**: To stop a specific service:
   ```bash
   podman compose -f docker-compose.dev.yml stop postgres
   ```

### Docker Troubleshooting

If you encounter issues with Docker:

#### Container Issues

1. **Container not starting**:
   - Check logs with `podman compose -f docker-compose.dev.yml logs`
   - Verify there are no port conflicts
   - Ensure Docker has sufficient resources allocated

#### Hot Module Replacement Issues

2. **HMR not working**:
   - Verify the volume mounts in docker-compose.dev.yml are correct
   - Check webpack-hmr.config.js for proper polling configuration
   - Ensure the following environment variables are set in the container:
     ```
     WEBPACK_WATCH=true
     CHOKIDAR_USEPOLLING=true
     ```
   - Try rebuilding the container: `podman compose -f docker-compose.dev.yml up --build`
   - Check if your editor is causing any file watching issues (some editors create temporary files)
   - Verify that the `start:docker:dev` script is being used in the container

#### Database Issues

3. **Database connection issues**:
   - Ensure the database container is healthy: `podman compose -f docker-compose.dev.yml ps`
   - Check database logs: `podman compose -f docker-compose.dev.yml logs postgresdb`
   - Verify connection parameters in the application match those in docker-compose.dev.yml
   - Try connecting to the database using CloudBeaver to verify it's accessible

#### Network Issues

4. **Network connectivity issues**:
   - Verify all containers are on the same network: `docker network inspect backend_network`
   - Check if containers can resolve each other by service name
   - Ensure no firewall rules are blocking container communication

### Viewing Docker Logs

To view logs for individual services in the development environment, you can use either the service name (from docker-compose.dev.yml) or the container name. Both methods are shown below.

#### Using Docker Compose with Service Names

```bash
# View logs for all services
podman compose -f docker-compose.dev.yml logs

# View logs for a specific service with follow option
podman compose -f docker-compose.dev.yml logs -f cloudbeaver
podman compose -f docker-compose.dev.yml logs -f postgres
podman compose -f docker-compose.dev.yml logs -f car-wrapper-service-backend

# View logs with timestamps
podman compose -f docker-compose.dev.yml logs -f --timestamps car-wrapper-service-backend
```

#### Using Docker/Podman with Container Names

```bash
# View logs using container names
docker logs -f nestjs_cloudbeaver
docker logs -f nestjs_postgres
docker logs -f nestjs_carwrapperservice

# If using Podman
podman logs -f nestjs_cloudbeaver
podman logs -f nestjs_postgres
podman logs -f nestjs_carwrapperservice
```

#### Service Names vs Container Names

Here's a reference table showing the relationship between service names and container names:

| Service Name (docker-compose) | Container Name           |
|-------------------------------|--------------------------|
| cloudbeaver                   | nestjs_cloudbeaver       |
| postgres                      | nestjs_postgres          |
| car-wrapper-service-backend   | nestjs_carwrapperservice |

### Connecting to CloudBeaver

CloudBeaver is a web-based database management tool included in the development environment. Follow these steps to connect to your PostgreSQL database:

#### 1. Access CloudBeaver

- Open http://localhost:8978 in your browser
- Wait for the CloudBeaver interface to load

#### 2. Initial Setup (First Time Only)

**Server Configuration:**
- On the "Initial Server Configuration" screen, configure the following:
  - **Server Name**: "CloudBeaver CE Server" (or any name you prefer)
  - **Server URL**: http://localhost:8978 (default)
  - **Session lifetime**: 30 minutes (default)
- Click **NEXT**

**Authentication Settings:**
- Enable **Local authentication** (toggle should be blue)
- Set **Login**: Choose a username (e.g., "postgres")
- Set **Password**: Choose a secure password (sample password: e.g. "Postgres1234")
- You can leave **Allow anonymous access** enabled for development
- Under **Disabled Drivers**, make sure PostgreSQL is NOT in the list (remove it if present)
- Click **NEXT** and then **FINISH** to complete the setup

#### 3. Log in to CloudBeaver

- After setup is complete, you'll be redirected to the login page
- Enter the username and password you created during setup
- Click **LOGIN**

#### 4. Create PostgreSQL Connection

- In the main CloudBeaver interface, click the **+** button in the top menu
- Select **PostgreSQL** from the list of database types
- Configure the connection with these exact settings:
  - **Connection Name**: "PostgreSQL - Car Wrapper Service"
  - **Host**: `postgresdb` (important: use the service name, not localhost)
  - **Port**: `5432`
  - **Database**: `postgres`
  - **Username**: `user`
  - **Password**: `root_password`
  - Leave other settings at their defaults
- Click **Test Connection** to verify it works
  - If the test fails, check the Docker logs for the postgres service
- Click **Create** to save the connection

#### 5. Using CloudBeaver

- **Browse Database Objects**: Expand the connection in the navigation tree to see tables, views, etc.
- **Execute SQL Queries**: Click on "SQL" in the top menu to open a new SQL script
- **View Table Data**: Right-click on a table and select "View Data"
- **Edit Table Data**: You can edit data directly in the table view
- **Export/Import Data**: Use the export/import features for data migration

#### Troubleshooting CloudBeaver Connection

If you encounter connection issues:

1. **Check PostgreSQL Container**: Ensure the postgres container is running
   ```bash
   podman compose -f docker-compose.dev.yml ps
   ```

2. **Verify Network Configuration**: Make sure both containers are on the same network
   ```bash
   docker network inspect backend_network
   ```

3. **Check PostgreSQL Logs**: Look for any errors in the PostgreSQL logs
   ```bash
   podman compose -f docker-compose.dev.yml logs postgres
   ```

4. **Restart CloudBeaver**: Sometimes restarting the CloudBeaver container can resolve connection issues
   ```bash
   podman compose -f docker-compose.dev.yml restart cloudbeaver
   ```

## Deployment

### Environment Variables

For deployment, configure the following environment variables:

- **GRPC_HOST**: Host address for the gRPC server (default: `0.0.0.0`)
- **GRPC_PORT**: Port for the gRPC server (default: `8081`)
- **DB_HOST**: Database host address
- **DB_PORT**: Database port
- **DB_USERNAME**: Database username
- **DB_PASSWORD**: Database password
- **DB_NAME**: Database name
- **NODE_ENV**: Set to `production` for production deployment
- **MICROSERVICE_SECRET_KEY**: Secret key for microservice authentication (default: `aCHW7ds8xn`)

### Docker Deployment

#### Production Docker Setup

The production Docker setup uses `docker-compose.yml` which includes only the essential services:

- **PostgreSQL**: Database for storing application data
- **car-wrapper-service-backend**: The main gRPC service

This setup is used for development and testing the production build locally, not for actual production deployment.

#### Kubernetes Deployment

For production deployment in Kubernetes, only the Dockerfile is used (not docker-compose files). The Kubernetes deployment configuration can be found in `backend/deployment/deployment.yaml`.

The Kubernetes deployment:
- Uses the same Docker image built from the Dockerfile
- Configures the gRPC service to run on port 8081
- Sets up appropriate health checks using TCP socket probes
- Creates necessary Kubernetes services for internal and external access
- Manages replicas and scaling for high availability

To deploy to Kubernetes:
1. Build the Docker image using the Dockerfile
2. Push the image to your container registry
3. Apply the Kubernetes deployment configuration:
   ```bash
   kubectl apply -f backend/deployment/deployment.yaml
   ```

Note: The deployment.yaml file is configured to deploy only the gRPC service. REST endpoints and cron jobs are not included in the Kubernetes deployment.

#### Custom Deployment

For custom deployments, you can use the provided Dockerfile:

```dockerfile
FROM node:22-alpine

WORKDIR /usr/src/app

# Set environment variables
ENV NODE_ENV=production
ENV DOCKER_ENVIRONMENT=true

# Copy configuration
COPY .env.docker ./

# Copy package files and install dependencies
COPY package*.json ./
RUN npm ci && npm install -g @nestjs/cli

# Copy the rest of the application
COPY . .

# Build the application
RUN npm run build

EXPOSE 50051

CMD ["npm", "run", "start:docker:prod"]
```

Then build and run:

```bash
docker build -t car-wrapper-service .
docker run -p 50051:50051 car-wrapper-service
```

## Database Schema Management

### TypeORM Synchronization Behavior

This project uses TypeORM for database interactions, with the following configuration in `app.module.ts`:

```typescript
synchronize: process.env.NODE_ENV !== 'production'
```

#### What This Means

- **In Development** (`NODE_ENV` is not 'production'):
  - New tables and columns are automatically created
  - Existing tables are updated with new columns
  - **Important**: Columns removed from entities but still present in the database will NOT be dropped
  - This allows for rapid development without migrations

- **In Production** (`NODE_ENV` is 'production'):
  - No automatic schema changes occur
  - All schema changes must be done through migrations
  - This protects production data from accidental schema changes

#### Handling Removed Columns

When you remove a column from an entity:

1. The column remains in the database even with `synchronize: true`
2. The application will ignore the column when querying
3. To actually remove the column from the database, you must:
   - Create and run a migration, or
   - Manually alter the table in the database

### Schema Migration Process

When schema changes are required (especially for production), follow these steps:

#### 1. Install TypeORM CLI (if not already installed)

```bash
npm install -g typeorm
# or locally
npm install --save-dev typeorm
```

#### 2. Configure TypeORM for Migrations

Since this project configures TypeORM in `app.module.ts` rather than using a separate `ormconfig.json` file, you'll need to create a `typeorm-cli.config.ts` file in the project root for migration commands:

```typescript
// typeorm-cli.config.ts
import { appConfig } from './src/config/app.config';

module.exports = {
  type: 'postgres',
  host: appConfig.database.host,
  port: appConfig.database.port,
  username: appConfig.database.username,
  password: appConfig.database.password,
  database: appConfig.database.database,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  cli: {
    migrationsDir: 'src/migrations'
  }
};
```

Then add the following to your `package.json` scripts section:

```json
"scripts": {
  // ... existing scripts
  "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js --config typeorm-cli.config.ts",
  "migration:generate": "npm run typeorm -- migration:generate -n",
  "migration:run": "npm run typeorm -- migration:run",
  "migration:revert": "npm run typeorm -- migration:revert"
}
```

#### 3. Generate a Migration

After making changes to your entities:

```bash
# Generate a migration by comparing current schema with entity definitions
npm run migration:generate MigrationName

# Or create an empty migration file manually
npm run typeorm -- migration:create -n MigrationName
```

#### 4. Review and Edit the Migration

The generated migration will be in `src/migrations/`. Review and edit if necessary:

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrationName1234567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Changes to apply when migrating up
    await queryRunner.query(`ALTER TABLE "car_cases" ADD "new_column" varchar`);

    // For removing columns:
    // await queryRunner.query(`ALTER TABLE "car_cases" DROP COLUMN "old_column"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Changes to apply when rolling back
    await queryRunner.query(`ALTER TABLE "car_cases" DROP COLUMN "new_column"`);

    // For restoring removed columns:
    // await queryRunner.query(`ALTER TABLE "car_cases" ADD "old_column" varchar`);
  }
}
```

#### 5. Build the Project

```bash
npm run build
```

#### 6. Run the Migration

```bash
# Apply pending migrations
npm run migration:run

# To revert the most recent migration
npm run migration:revert
```

#### 7. Verify the Changes

Connect to the database and verify that the schema changes were applied correctly.

### Migration in Docker Environment

For Docker deployments:

1. **Development**: Migrations can be run as part of the container startup script
2. **Production**: Consider running migrations as a separate step before deploying the new application version

Example Docker startup script with migrations:

```bash
#!/bin/sh

# Wait for database to be ready
echo "Waiting for database..."
sleep 5

# Run migrations
echo "Running migrations..."
npm run migration:run

# Start the application
echo "Starting application..."
npm run start:prod
```

#### Best Practices

- Use `synchronize: true` only in development
- Always use migrations for production schema changes
- Test migrations thoroughly before applying to production
- Keep track of all schema changes with version-controlled migrations
- Always include both `up` and `down` methods for reversibility
- Back up your database before applying migrations to production
- Consider using a migration staging environment that mirrors production

## gRPC Testing Tools Setup

This section provides instructions for setting up and using gRPC testing tools on Windows.

### Prerequisites

- Windows OS
- Command Prompt (`cmd.exe`)
- gRPC service running (e.g., `localhost:50051`)

### Tools Overview

- `grpcurl` is a command-line tool for interacting with gRPC services. It allows you to list available services, call methods, and inspect responses.
- `grpcui` provides a web-based UI for interacting with gRPC services, making it easier to explore and test your APIs.

### grpcurl Setup

#### Install grpcurl

1. Download the latest Windows binary from [grpcurl releases](https://github.com/fullstorydev/grpcurl/releases).
   (e.g., `grpcurl_1.8.9_windows_x86_64.zip`)
2. Create a tools directory:

   ```bash
   mkdir "C:\Users\<USER>\tools"
   ```

3. Extract the zip and move `grpcurl.exe` into `C:\Users\<USER>\tools`.

#### Add to PATH

1. Open **Start Menu** → search "*environment variables for your account*".
2. Under **User variables**, select `Path` → **Edit** → **New**.
3. Add:

   ```
   C:\Users\<USER>\tools
   ```

4. Click **OK** to close all dialogs.

#### Verify

```bash
grpcurl --version
```

### grpcui Setup

#### Install Go

- Download the Windows installer from [Go Downloads](https://go.dev/dl/)
- Install using the `.msi` and complete setup

#### Install grpcui

```bash
go install github.com/fullstorydev/grpcui/cmd/grpcui@latest
```

#### Add Go bin to PATH

Add the following to your PATH (same method as above):

```
C:\Users\<USER>\go\bin
```

#### Verify

```bash
grpcui --version
```

### Testing gRPC Services with Tools

#### Using grpcurl

```bash
# List services
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext localhost:8081 list

# Ping the service
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext -d "{}" localhost:8081 car_wrapper_service.CarWrapperService/Ping

# Find car cases
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext -d "{}" localhost:8081 car_wrapper_service.CarWrapperService/FindCarCases
```

#### Using grpcui

```bash
grpcui -proto src/proto/car-wrapper-service.proto -import-path . -plaintext localhost:8081
```

- Opens a browser-based UI (typically at http://127.0.0.1:8080)

#### Batch Script: test-grpc.bat

You can create a batch script to automate common gRPC testing tasks:

```batch
@echo off
echo Testing gRPC Service
echo ===================

echo.
echo Listing services...
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext localhost:8081 list

echo.
echo Pinging service...
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext -d "{}" localhost:8081 car_wrapper_service.CarWrapperService/Ping

echo.
echo Finding car cases...
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext -d "{}" localhost:8081 car_wrapper_service.CarWrapperService/FindCarCases
```

Run it with:

```batch
test-grpc.bat
```

### Troubleshooting gRPC Tools

#### Command Not Found

- Open **new** Command Prompt to reload environment
- Check:

  ```batch
  echo %PATH%
  where grpcurl
  ```

- Try full path:

  ```batch
  C:\Users\<USER>\tools\grpcurl.exe --version
  ```

#### Reflection API Errors

- Always specify `-proto` with correct path:

  ```batch
  dir src\proto\car-wrapper-service.proto
  ```

#### Connection Refused

- Ensure service is running:

  ```batch
  netstat -ano | findstr :8081
  ```

### Common Commands Reference

```batch
# Version check
grpcurl --version
grpcui --version

# List services
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext localhost:8081 list

# Launch grpcui
grpcui -proto src/proto/car-wrapper-service.proto -import-path . -plaintext localhost:8081

# FindCarCases example
grpcurl -proto src/proto/car-wrapper-service.proto -import-path . -plaintext -d "{}" localhost:8081 car_wrapper_service.CarWrapperService/FindCarCases

# Check if tools are in path
where grpcurl
where grpcui
```

### Tips

- Use **double quotes** (`"`) for JSON in cmd.exe
- `cd` into your project directory before running commands
- If issues arise, use the **full path** to tools
- Keep the **Command Prompt** open while using `grpcui`

## Additional Resources

- [NestJS Documentation](https://docs.nestjs.com)
- [gRPC Documentation](https://grpc.io/docs/)
- [TypeORM Documentation](https://typeorm.io/)
- [TypeORM Migrations Guide](https://typeorm.io/#/migrations)
- [grpcurl GitHub Repository](https://github.com/fullstorydev/grpcurl)
- [grpcui GitHub Repository](https://github.com/fullstorydev/grpcui)

## Git Hooks

This repository includes Git hooks that are shared across the team to ensure consistent code quality and configuration.

### Available Git Hooks

#### pre-commit

Automatically ensures that `DB_SSL=true` is set in the `.env.docker` file before committing. This is important because:

1. Production environments must use SSL for database connections for security
2. Local development environments may use `DB_SSL=false` for easier setup
3. The hook adds a comment to indicate that this value should be true for commits

### Git Hooks Setup

To use these hooks, run the following command in the root of the repository:

```bash
git config core.hooksPath .githooks
```

This will configure Git to use the hooks in the `.githooks` directory instead of the default `.git/hooks` directory.

#### Bypassing Hooks

If you need to bypass a hook for a specific commit, you can use:

```bash
git commit --no-verify -m "Your commit message"
```

However, please use this sparingly and only when absolutely necessary.

## Documentation & Presentations

The project includes comprehensive documentation and presentation materials:

### Documentation

The documentation is organized into the following categories:

- **Design Documentation**: `docs/design/DESIGN_DOCS.md` - Detailed design specifications and architecture
- **API Documentation**: `docs/api/API_DOCS.yaml` - OpenAPI specification for the service
- **Planning Documents**:
  - `docs/planning/SPRINT_PLANNING.md` - Sprint planning and task breakdown
  - `docs/planning/DAILY_PROGRESS.md` - Daily progress updates
- **User & Developer Guides**:
  - `docs/guides/VSCODE_FIX_DOCS.md` - Guide for fixing VS Code shell integration issues

### Presentations

The project includes presentation materials created with Marp, a Markdown-based presentation tool:

- **Feature Highlights**: `docs/presentations/car-wrapper-features.md` - Comprehensive overview of all features developed in the Car Wrapper Service

For instructions on viewing and exporting presentations, as well as general documentation guidelines, see `docs/DOCS_GUIDE.md`.