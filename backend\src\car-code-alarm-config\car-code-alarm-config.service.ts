import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, FindOptionsWhere, In, Repository } from 'typeorm';
import { CarCodeAlarmConfig } from './entities';
import {
  CarCodeAlarmConfigDAO,
  CarCodeAlarmConfigQueryParams,
  UpdateCarCodeAlarmConfigDTO,
} from './interfaces';
import { CarCodeAlarmConfigHistoryService } from '../car-code-alarm-config-history/car-code-alarm-config-history.service';
import { distinctBy } from '../common/utils';
import {
  DEFAULT_QUERY_LIMITS,
  DEFAULT_SORT_SETTINGS,
} from '../common/constants';
import { ServiceResponse } from '../common/utils/service-utils';

@Injectable()
export class CarCodeAlarmConfigService {
  private readonly logger = new Logger(CarCodeAlarmConfigService.name);

  constructor(
    @InjectRepository(CarCodeAlarmConfig)
    private carCodeAlarmConfigRepository: Repository<CarCodeAlarmConfig>,
    private carCodeAlarmConfigHistoryService: CarCodeAlarmConfigHistoryService,
    private dataSource: DataSource,
  ) {}

  /**
   * Find all car code alarm configs with optional filtering
   * @param filters Optional query parameters for filtering
   * @returns ServiceResponse with car code alarm configs or error details
   */
  async findCarCodeAlarmConfigs(
    filters: CarCodeAlarmConfigQueryParams,
  ): Promise<ServiceResponse<CarCodeAlarmConfigDAO>> {
    try {
      const { car_code, sort_order, limit } = filters;

      // Log query filters
      this.logger.log(
        `Finding car code alarm configurations with filters - limit: ${limit}, sort: ${sort_order}`,
      );

      this.logger.log(`Additional filters: ${JSON.stringify({ car_code })}`);

      // Build query conditions
      const where: FindOptionsWhere<CarCodeAlarmConfig> = {};

      // Apply basic filters
      if (car_code) where.car_code = car_code;

      // Find configurations matching the filters with sort order and limit
      const configs = await this.carCodeAlarmConfigRepository.find({
        where,
        order: {
          car_code: sort_order || DEFAULT_SORT_SETTINGS.CONFIG,
        },
        take: limit || DEFAULT_QUERY_LIMITS.CONFIG,
      });
      this.logger.log(`Found ${configs.length} car code alarm configurations`);
      const mappedConfigs = this.mapCarCodeAlarmConfigsToDAO(configs);

      return {
        success: true,
        data: mappedConfigs,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error finding car code alarm configurations: ${errorMessage}`,
      );
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Create new car code alarm configs
   * @param carCodeAlarmConfigDAOList Array of car code alarm configs to create
   * @returns The created car code alarm configs
   */
  async createCarCodeAlarmConfig(
    carCodeAlarmConfigDAOList: CarCodeAlarmConfigDAO[],
  ): Promise<ServiceResponse<CarCodeAlarmConfigDAO>> {
    try {
      // Enforce batch limit for car code alarm config creation
      if (
        carCodeAlarmConfigDAOList.length >
        DEFAULT_QUERY_LIMITS.CAR_CODE_ALARM_CONFIG_BATCH
      ) {
        return {
          success: false,
          error: `Batch limit exceeded. Maximum allowed is ${DEFAULT_QUERY_LIMITS.CAR_CODE_ALARM_CONFIG_BATCH}.`,
        };
      }

      // Check for existing config - if one of the config is present in db, reject entire batch entry
      const uniqueKeys: string[] = distinctBy(
        carCodeAlarmConfigDAOList,
        (r) => r.car_code,
      );
      const existingConfigs = await this.carCodeAlarmConfigRepository.find({
        where: { car_code: In(uniqueKeys) },
      });
      if (existingConfigs.length > 0) {
        const errors = existingConfigs.map(
          (cfg) => `Config with car_code=${cfg.car_code} already exists`,
        );
        return {
          success: false,
          error: `Some car codes already exist: ${existingConfigs
            .map((c) => c.car_code)
            .join(', ')}`,
          errors: errors,
        };
      }

      this.logger.log('Creating new car code alarm configs');
      // Transactional save - Rollback on failure
      return await this.dataSource.transaction(async (manager) => {
        // save CarCodeAlarmConfig
        const configEntities = carCodeAlarmConfigDAOList.map((req) => {
          const entity = new CarCodeAlarmConfig();
          entity.car_code = req.car_code;
          entity.name = req.name;
          entity.category = req.category;
          entity.device_type = req.device_type || '';
          entity.should_raise_alarm = req.should_raise_alarm;
          return entity;
        });
        const savedConfigs = await manager.save(
          CarCodeAlarmConfig,
          configEntities,
        );

        // save CarCodeAlarmConfigHistory
        const historyErrors =
          await this.carCodeAlarmConfigHistoryService.createNewHistory({
            oldRecords: {},
            configs: savedConfigs,
            transactionType: 'create',
            entityManager: manager,
          });
        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on create',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Map to DAO objects
        const carCodeAlarmConfigDAOs =
          this.mapCarCodeAlarmConfigsToDAO(savedConfigs);
        return {
          success: true,
          data: carCodeAlarmConfigDAOs,
        };
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error creating car code alarm configs: ${errorMessage}`,
      );
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Update a car code alarm config
   * @param updates DTO object with car_code and updated_by (required) and fields to update
   * @returns The updated car code alarm config
   */
  async updateCarCodeAlarmConfig(
    updates: UpdateCarCodeAlarmConfigDTO,
  ): Promise<ServiceResponse<CarCodeAlarmConfigDAO>> {
    try {
      const { car_code } = updates;
      this.logger.log(
        `Updating car code alarm config with car_code: ${car_code}`,
      );

      // Find existing config
      const existingConfig = await this.carCodeAlarmConfigRepository.findOne({
        where: { car_code: car_code },
      });

      if (!existingConfig) {
        return {
          success: false,
          error: `CAR config with the specified car_code not found.`,
          errors: [`Car code ${car_code} not found`],
        };
      }

      // Store old values for history
      const oldValue = JSON.stringify(existingConfig);
      // Return the result from the transaction
      return await this.dataSource.transaction(async (manager) => {
        // Apply updates
        const configToUpdate = {
          ...existingConfig,
          // Only update fields that are explicitly included in the updates object
          ...(updates.name !== undefined && { name: updates.name }),
          ...(updates.category !== undefined && { category: updates.category }),
          ...(updates.device_type !== undefined && {
            device_type: updates.device_type,
          }),
          ...(updates.should_raise_alarm !== undefined && {
            should_raise_alarm: updates.should_raise_alarm,
          }),
          // Always update the updated_by and updated_at fields
          updated_by: updates.updated_by,
          updated_at: new Date(),
        };

        // Save updated config within the transaction
        const updatedConfig = await manager.save(
          CarCodeAlarmConfig,
          configToUpdate,
        );

        // Create history record for the update
        const historyErrors =
          await this.carCodeAlarmConfigHistoryService.createNewHistory({
            oldRecords: { [updatedConfig.car_code]: oldValue },
            configs: [updatedConfig],
            transactionType: 'update',
            entityManager: manager,
          });

        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on update',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Map to DAO object and return success response
        this.logger.log(
          `Updated config before mapping: ${JSON.stringify(updatedConfig)}`,
        );

        // Ensure updatedConfig is properly formatted as an array
        const configsToMap = Array.isArray(updatedConfig)
          ? updatedConfig
          : [updatedConfig];

        const mappedConfigs = this.mapCarCodeAlarmConfigsToDAO(configsToMap);
        this.logger.log(`Mapped configs: ${JSON.stringify(mappedConfigs)}`);

        return {
          success: true,
          data: mappedConfigs,
        };
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.logger.error(
        `Error updating car code alarm config: ${errorMessage}`,
      );
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Delete a car code alarm config
   * @param car_code The unique identifier of the car code alarm config to delete
   * @param updated_by The user or system identifier that is performing the delete operation
   * @returns A service response containing a success message or error details
   */
  async deleteCarCodeAlarmConfig(
    car_code: string,
    updated_by: string,
  ): Promise<ServiceResponse<{ message: string }>> {
    try {
      this.logger.log(
        `Deleting car code alarm config with car_code: ${car_code}`,
      );

      // Find existing config
      const existingConfig = await this.carCodeAlarmConfigRepository.findOne({
        where: { car_code: car_code },
      });

      if (!existingConfig) {
        return {
          success: false,
          error: `CAR config with the specified car_code not found.`,
          errors: [`Car code ${car_code} not found`],
        };
      }

      // Convert to JSON string for storage
      const oldValue = JSON.stringify(existingConfig);
      // Return the result from the transaction
      return await this.dataSource.transaction(async (manager) => {
        // Delete config within the transaction
        await manager.remove(CarCodeAlarmConfig, existingConfig);

        // Create history record
        const configForHistory = {
          car_code: car_code,
          updated_by: updated_by,
        };

        // Create history record and handle any errors
        const historyErrors =
          await this.carCodeAlarmConfigHistoryService.createNewHistory({
            oldRecords: { [car_code]: oldValue },
            configs: [configForHistory],
            transactionType: 'delete',
            entityManager: manager,
          });

        // If history creation fails, return error response
        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on delete',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Return success response
        return {
          success: true,
          data: [{ message: `Car code alarm config deleted successfully` }],
        };
      });
    } catch (error: unknown) {
      // handle DB errors
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error deleting car code alarm config: ${errorMessage}`,
      );
      // Return standardized error response
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Converts CarCodeAlarmConfig entities to DAO objects (CarCodeAlarmConfigDAO[])
   * Ensures updated_at is always a string (ISO format)
   */
  private mapCarCodeAlarmConfigsToDAO(
    configs: CarCodeAlarmConfig[],
  ): CarCodeAlarmConfigDAO[] {
    this.logger.log(
      `mapCarCodeAlarmConfigsToDAO input: ${JSON.stringify(configs)}`,
    );

    return configs.map((config) => {
      // Ensure updated_at is a valid Date object or use current date
      // Assume updated_at is always a Date (by type contract)
      const updatedAt = config.updated_at ?? new Date();

      // Create the DAO object with all fields from the entity
      const daoObject: CarCodeAlarmConfigDAO = {
        car_code: config.car_code,
        name: config.name,
        description: config.description,
        category: config.category,
        device_type: config.device_type,
        should_raise_alarm: config.should_raise_alarm,
        updated_at: updatedAt.toISOString(),
        updated_by: config.updated_by,
      };

      this.logger.log(`Mapped DAO object: ${JSON.stringify(daoObject)}`);
      return daoObject;
    });
  }
}
