import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Raw } from 'typeorm';
import { CarCase } from './entities';
import { CarCodeConfig } from '../car-code-config/entities';
import { CarCaseDAO } from './interfaces';
import { ServiceResponse } from '../common/utils/service-utils';

@Injectable()
export class CarCaseService {
  private readonly logger = new Logger(CarCaseService.name);

  constructor(
    @InjectRepository(CarCase)
    private carCaseRepository: Repository<CarCase>,
    @InjectRepository(CarCodeConfig)
    private carCodeConfigRepository: Repository<CarCodeConfig>,
  ) {}

  /**
   * Process a batch of car cases, generating case IDs and tags
   * @param carCaseDAORequest Array of car cases to process
   * @returns Object with success status, processed cases data, or error message
   */
  async createCarCase(
    carCaseDAORequest: CarCaseDAO[],
  ): Promise<ServiceResponse<CarCaseDAO>> {
    /**
     * Saves a batch of car case records, ensuring correct assignment of case IDs and groupings before persistence.
     *
     * This function first maps car cases to their corresponding car code configurations and case IDs.
     * The mapping step is critical because multiple cases may belong to the same grouping but have not yet been assigned case IDs.
     * By checking and assigning groupings and IDs before saving, the function prevents duplicate or incorrect assignments in the database.
     *
     * @param carCaseDAOList - List of car case data objects to be saved.
     * @param carCodeConfigMap - Map linking car code identifiers to their configuration objects.
     * @param caseIdMap - Map for tracking and assigning unique case IDs to groupings.
     * @param totalCases - Total number of cases being processed in the current batch.
     * @param savedEntities - Accumulator for entities that have already been saved.
     * @returns Promise resolving to the updated list of saved car case entities.
     */
    const saveCarCase = async (
      carCaseDAOList: CarCaseDAO[],
      carCodeConfigMap: Map<string, CarCodeConfig>,
      caseIdMap: Map<string, string>,
      totalCases: number,
      savedEntities: CarCaseDAO[],
    ) => {
      for (const [index, carCase] of carCaseDAOList.entries()) {
        const currentIndex = index + 1;
        const configKey = `${carCase.car_code}-${carCase.site_object_id}`;
        const config = carCodeConfigMap.get(configKey);

        // Generate tags and assign case ID
        const caseTags = this.generateTags(carCase);
        carCase.tags = caseTags;

        // Either reuse existing case ID or generate a new one
        if (caseIdMap.has(caseTags)) {
          carCase.case_id = caseIdMap.get(caseTags)!;
          this.logger.log(
            `[${currentIndex}/${totalCases}] Reusing case ID: ${carCase.case_id}`,
          );
        } else {
          carCase.case_id = await this.determineCase(
            caseTags,
            config!.monitoring_window,
            caseIdMap,
          );
          caseIdMap.set(caseTags, carCase.case_id);
          this.logger.log(
            `[${currentIndex}/${totalCases}] New case ID: ${carCase.case_id}`,
          );
        }

        // Save to database
        const savedCase = await this.saveCarCaseDAO(
          carCase,
          currentIndex,
          totalCases,
        );
        savedEntities.push(savedCase);
      }
    };

    // logic starts here
    try {
      // Validate all car cases and get their configurations
      const { carCaseDAOList, carCodeConfigMap } =
        await this.checkCarCodeConfig(carCaseDAORequest);
      const totalCases = carCaseDAOList.length;

      // Track case IDs by tags within this batch
      const caseIdMap = new Map<string, string>();
      const savedEntities: CarCaseDAO[] = [];

      // Process and save each car case in a single loop
      await saveCarCase.call(
        this,
        carCaseDAOList,
        carCodeConfigMap,
        caseIdMap,
        totalCases,
        savedEntities,
      );

      this.logger.log(`Successfully saved ${savedEntities.length} car cases`);
      return { success: true, data: savedEntities };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing car cases: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Check if car code configurations exist and are enabled for each car case
   * @param carCaseDAOList List of car cases to check
   * @returns Object containing the validated car cases and the config map
   * @throws Error if any configuration is missing or disabled
   */
  private async checkCarCodeConfig(carCaseDAOList: CarCaseDAO[]): Promise<{
    carCaseDAOList: CarCaseDAO[];
    carCodeConfigMap: Map<string, CarCodeConfig>;
  }> {
    // Cache for car code configs
    const carCodeConfigMap = new Map<string, CarCodeConfig>();

    // Verify each incoming case
    for (const carCaseDAO of carCaseDAOList) {
      // Trim and normalize values to lowercase for consistency
      carCaseDAO.car_code = carCaseDAO.car_code.trim().toLowerCase();
      carCaseDAO.site_object_id = carCaseDAO.site_object_id
        .trim()
        .toLowerCase();

      // Log with string literals to see exact values including any whitespace
      this.logger.log(
        `Processing car case: car_code="${carCaseDAO.car_code}", site_object_id="${carCaseDAO.site_object_id}"`,
      );

      // We'll implement car code definition check later
      // For now, just log a warning message that we're proceeding without checking
      this.logger.warn(
        `Skipping car code definition check for car_code=${carCaseDAO.car_code} (to be implemented later)`,
      );

      const configKey = `${carCaseDAO.car_code}-${carCaseDAO.site_object_id}`;

      // Check if we already have this config in our cache
      if (!carCodeConfigMap.has(configKey)) {
        // Try to find the config with exact match
        const carCodeConfig = await this.carCodeConfigRepository.findOne({
          where: {
            car_code: Raw((alias) => `LOWER(${alias}) = LOWER(:car_code)`, {
              car_code: carCaseDAO.car_code,
            }),
            site_object_id: Raw(
              (alias) => `LOWER(${alias}) = LOWER(:site_object_id)`,
              {
                site_object_id: carCaseDAO.site_object_id,
              },
            ),
          },
        });

        // If config still doesn't exist, throw error
        if (!carCodeConfig) {
          this.logger.error(
            `No config found for car_code=${carCaseDAO.car_code} and site_object_id=${carCaseDAO.site_object_id}`,
          );
          throw new Error(
            `Car code config for ${carCaseDAO.car_code} and ${carCaseDAO.site_object_id} not found`,
          );
        }
        // Add to our cache
        carCodeConfigMap.set(configKey, carCodeConfig);
      }

      // Get the config from our cache (we know it exists since we just added it)
      const config = carCodeConfigMap.get(configKey)!;

      // Check if the config is disabled
      // Using explicit comparison to ensure we only throw when enabled is specifically false
      if (config && !config.enabled) {
        throw new Error(
          `Car code ${carCaseDAO.car_code} is disabled for ${carCaseDAO.site_object_id}`,
        );
      }
    }

    return {
      carCaseDAOList: carCaseDAOList,
      carCodeConfigMap: carCodeConfigMap,
    };
  }

  // findCarCases method has been removed as it's not used in the application.
  // The controller uses CarCaseConsoService.findCarCases instead.

  // findCarCasesByIds method has been removed as it's not used in the application.
  // The controller uses CarCaseConsoService.findCarCasesByIds instead.

  /**
   * Generate tags for a car case
   * @param data The car case data
   * @returns The generated tags
   */
  private generateTags(data: CarCaseDAO): string {
    // Format: car_code-site_object_id-sub_site_object_id-device_id
    // If sub_site_object_id or device_id are not provided, use "0"
    // Ensure all values are lowercase for consistency
    return [
      data.car_code.toLowerCase(),
      data.site_object_id.toLowerCase(),
      (data.sub_site_object_id || '0').toLowerCase(),
      (data.device_id || '0').toLowerCase(),
    ].join('-');
  }

  /**
   * Determine the case ID for a car case
   * @param tags The tags for the car case
   * @param monitoringWindow The monitoring window in days
   * @param inMemoryCache Optional in-memory cache of case IDs being processed in the current batch
   * @returns The case ID
   */
  private async determineCase(
    tags: string,
    monitoringWindow: number,
    inMemoryCache?: Map<string, string>,
  ): Promise<string> {
    // Check if we have a case in the in-memory cache
    const cachedCaseId = this.checkInMemoryCache(tags, inMemoryCache);
    if (cachedCaseId) {
      return cachedCaseId;
    }

    // Check the database for the most recent case with the same tags
    const recentCase = await this.findMostRecentCaseByTags(tags);

    // If no case exists, create a new case ID with counter 1
    if (!recentCase) {
      return `${tags}-1`;
    }

    // Check if the most recent case is within the monitoring window
    const isWithinMonitoringWindow = this.isWithinMonitoringWindow(
      recentCase.logging_date,
      monitoringWindow,
    );

    if (isWithinMonitoringWindow) {
      // Within monitoring window, use the same case ID
      return recentCase.case_id;
    } else {
      // Outside monitoring window, create a new case ID with incremented counter
      const highestCounter = await this.findHighestCounter(tags, inMemoryCache);
      return `${tags}-${highestCounter + 1}`;
    }
  }

  /**
   * Check if a case with the same tags exists in the in-memory cache
   * @param tags The tags to check
   * @param inMemoryCache The in-memory cache
   * @returns The case ID if found, null otherwise
   */
  private checkInMemoryCache(
    tags: string,
    inMemoryCache?: Map<string, string>,
  ): string | null {
    if (inMemoryCache && inMemoryCache.has(tags)) {
      const cachedCaseId = inMemoryCache.get(tags);
      if (cachedCaseId) {
        this.logger.log(
          `Found case with same tags in current batch: ${cachedCaseId}`,
        );
        return cachedCaseId;
      }
    }
    return null;
  }

  /**
   * Find the most recent case with the same tags in the database
   * @param tags The tags to search for
   * @returns The most recent case if found, null otherwise
   */
  private async findMostRecentCaseByTags(
    tags: string,
  ): Promise<{ case_id: string; logging_date: string } | null> {
    const dbCase = await this.carCaseRepository.findOne({
      where: { tags },
      order: { logging_date: 'DESC' },
    });

    if (dbCase) {
      return {
        case_id: dbCase.case_id,
        logging_date: dbCase.logging_date.toISOString(),
      };
    }

    return null;
  }

  /**
   * Check if a date is within the monitoring window
   * @param dateString The date to check (ISO string)
   * @param monitoringWindow The monitoring window in days
   * @returns True if the date is within the monitoring window, false otherwise
   */
  private isWithinMonitoringWindow(
    dateString: string,
    monitoringWindow: number,
  ): boolean {
    const now = new Date();
    const caseDate = new Date(dateString);
    const daysDifference = Math.floor(
      (now.getTime() - caseDate.getTime()) / (1000 * 60 * 60 * 24),
    );

    return daysDifference < monitoringWindow;
  }

  /**
   * Find the highest counter for a given tag from both database and in-memory cache
   * @param tags The tags to search for
   * @param inMemoryCache Optional in-memory cache
   * @returns The highest counter
   */
  private async findHighestCounter(
    tags: string,
    inMemoryCache?: Map<string, string>,
  ): Promise<number> {
    let highestCounter = 0;

    // Check the database
    const highestCounterCase = await this.carCaseRepository.findOne({
      where: { tags },
      order: { case_id: 'DESC' },
    });

    if (highestCounterCase) {
      const dbCounter = this.extractCounterFromCaseId(
        highestCounterCase.case_id,
      );
      if (dbCounter > highestCounter) {
        highestCounter = dbCounter;
      }
    }

    // Check the in-memory cache
    if (inMemoryCache) {
      for (const [cacheTag, caseId] of inMemoryCache.entries()) {
        if (cacheTag === tags) {
          const cacheCounter = this.extractCounterFromCaseId(caseId);
          if (cacheCounter > highestCounter) {
            highestCounter = cacheCounter;
          }
        }
      }
    }

    return highestCounter;
  }

  /**
   * Extract the counter from a case ID
   * @param caseId The case ID
   * @returns The counter as a number, or 0 if not found
   */
  private extractCounterFromCaseId(caseId: string): number {
    const parts = caseId.split('-');
    const counter = parseInt(parts[parts.length - 1], 10);
    return !isNaN(counter) ? counter : 0;
  }

  // Helper methods groupCasesByTags and filterCasesByStatus have been removed
  // as they were only used by the findCarCases method which is no longer needed.

  /**
   * Save a car case DAO to the database
   * @param carCaseDAO The car case DAO to save
   * @param index The index of the car case in the batch (for logging)
   * @param totalCount The total number of car cases in the batch (for logging)
   * @returns The saved car case DAO with ID
   */
  private async saveCarCaseDAO(
    carCaseDAO: CarCaseDAO,
    index: number,
    totalCount: number,
  ): Promise<CarCaseDAO> {
    // Map DAO to entity
    const carCaseEntity = new CarCase();
    carCaseEntity.site_object_id = carCaseDAO.site_object_id;
    carCaseEntity.sub_site_object_id =
      carCaseDAO.sub_site_object_id?.toLowerCase() || '';
    carCaseEntity.additional_device_info = carCaseDAO.additional_device_info
      ? String(carCaseDAO.additional_device_info)
      : null;
    carCaseEntity.tags = carCaseDAO.tags?.toLowerCase() || '';
    carCaseEntity.case_id = carCaseDAO.case_id || '';
    carCaseEntity.car_code = carCaseDAO.car_code || '';
    carCaseEntity.source = carCaseDAO.source || '';
    carCaseEntity.title = carCaseDAO.title || '';
    carCaseEntity.recommendation = carCaseDAO.recommendation
      ? String(carCaseDAO.recommendation)
      : null;
    carCaseEntity.metadata = carCaseDAO.metadata
      ? String(carCaseDAO.metadata)
      : null;
    carCaseEntity.logging_date = new Date(
      carCaseDAO.logging_date || new Date().toISOString(),
    );

    // Save to database and update DAO with generated ID
    const savedEntity = await this.carCaseRepository.save(carCaseEntity);
    this.logger.log(
      `[${index}/${totalCount}] Saved case with ID: ${savedEntity.id}`,
    );
    carCaseDAO.id = savedEntity.id;
    return carCaseDAO;
  }

  // Alarm functionality will be implemented later

  // mapToResponse method has been removed as it was only used by the findCarCasesByIds method
  // which is no longer needed.
}
