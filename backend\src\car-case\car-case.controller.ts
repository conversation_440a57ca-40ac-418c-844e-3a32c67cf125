import { Controller, Logger, UseFilters, UseGuards } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CONTROLLER_SOURCE } from '../common/constants';
import { normalizeSortOrder } from '../common/utils';
import { CarCaseService } from './car-case.service';
import { CarCaseConsoService } from './car-case-conso.service';
import {
  CarCasesResponse,
  CreateCarCaseDAORequest,
  FindCarCasesByIdsRequest,
  FindCarCasesRequest,
  PingResponse,
} from './interfaces';
import { CarCaseConsoDAO } from './interfaces/car-case-conso.interface';
import { AclGuard } from '../client/acl/acl.guard';
import { AclExceptionFilter } from '../client/acl/acl-exception.filter';

@Controller()
export class CarCaseController {
  protected readonly logger: Logger;
  protected readonly source: string;

  constructor(
    protected readonly carCaseService: CarCaseService,
    protected readonly carCaseConsoService: CarCaseConsoService,
  ) {
    this.logger = new Logger(CarCaseController.name);
    this.source = CONTROLLER_SOURCE.GRPC;
  }

  /**
   * Health check endpoint for the car case service
   * @returns A simple message indicating the service is running
   */
  @GrpcMethod('CarWrapperService', 'PingCarCase')
  pingCarCase(): PingResponse {
    return { message: 'Car Case Service is running' };
  }

  /**
   * Find car cases with optional filters
   *
   * @param request Query parameters for filtering car cases
   * @returns Filtered car cases or error response
   */
  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'FindCarCases')
  async findCarCases(request: FindCarCasesRequest): Promise<CarCasesResponse> {
    // Ensure request is an object even if null or undefined is passed
    request = request || {};
    this.logger.log(`[${this.source}] Processing find car cases request`);

    // Normalize the sort order if provided
    if (request.sort_order) {
      request.sort_order = normalizeSortOrder(request.sort_order as string) as
        | 'ASC'
        | 'DESC';
    }

    // Call the service method
    const result = await this.carCaseConsoService.findCarCases(request);

    // Case 1: Find failed
    if (!result.success) {
      return {
        error: {
          status: 'ERROR: Bad Request - error finding car cases',
          code: 400,
          message: [JSON.stringify(request || {})],
          fields: [],
          error: result.error || 'Unknown error during find operation',
        },
      };
    }

    // Case 2: Find succeeded
    this.logger.log(
      `[${this.source}] Returning ${result.data?.length || 0} car cases`,
    );
    return {
      success: {
        status: 'success',
        code: 200,
        message: result.data || [],
      },
    };
  }

  /**
   * Find car cases by their IDs
   *
   * @param request Request containing an array of car case IDs and optional sort order
   * @returns Car cases matching the provided IDs or error response
   */
  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'FindCarCasesByIds')
  async findCarCasesByIds(
    request: FindCarCasesByIdsRequest,
  ): Promise<CarCasesResponse> {
    this.logger.log(
      `[${this.source}] Finding car cases by IDs: ${request.ids.length} IDs requested`,
    );

    // Normalize the sort order if provided
    if (request.sort_order) {
      request.sort_order = normalizeSortOrder(request.sort_order as string) as
        | 'ASC'
        | 'DESC';
    }

    // Call the service method
    const result = await this.carCaseConsoService.findCarCasesByIds(request);

    // Case 1: Find failed
    if (!result.success) {
      return {
        error: {
          status: 'ERROR: Bad Request - error finding car cases by IDs',
          code: 400,
          message: [JSON.stringify({ ids: request.ids })],
          fields: [],
          error: result.error || 'Unknown error during find operation',
        },
      };
    }

    // If we're looking for a single ID, and it's not found, return a 404 error
    if (request.ids.length === 1 && (result.data?.length || 0) === 0) {
      const errorMessage = `Car case with ID ${request.ids[0]} not found`;
      this.logger.error(
        `[${this.source}] Error finding car cases by IDs: ${errorMessage}`,
      );
      return {
        error: {
          status: 'ERROR: Not Found - car case not found',
          code: 404,
          message: [JSON.stringify({ ids: request.ids })],
          fields: [],
          error: errorMessage,
        },
      };
    }

    // Case 2: Find succeeded
    this.logger.log(
      `[${this.source}] Returning ${result.data?.length || 0} car cases by IDs`,
    );
    return {
      success: {
        status: 'success',
        code: 200,
        message: result.data || [],
      },
    };
  }

  /**
   * Create one or more car cases
   *
   * @param request Request containing car case data
   * @returns Created car cases or error response
   */
  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'CreateCarCase')
  async createCarCase(
    request: CreateCarCaseDAORequest,
  ): Promise<CarCasesResponse> {
    // Log the raw incoming request for debugging
    this.logger.log(
      `[${this.source}] Incoming CreateCarCase payload: ${JSON.stringify(request, null, 2)}`,
    );
    // Handle both single and batch requests
    const requests = request.requests || [];

    if (requests.length === 0) {
      this.logger.error(
        `[${this.source}] No car case requests provided in the batch request`,
      );
      return {
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404,
          message: [],
          fields: [],
          error: 'No car case requests provided in the batch request',
        },
      };
    }

    // Basic validation for required fields
    for (let i = 0; i < requests.length; i++) {
      const req = requests[i];
      const missingFields: string[] = [];

      if (!req.site_object_id) missingFields.push('site_object_id');
      if (!req.car_code) missingFields.push('car_code');
      if (!req.source) missingFields.push('source');
      if (!req.logging_date) missingFields.push('logging_date');

      if (missingFields.length > 0) {
        this.logger.error(
          `[${this.source}] Validation failed for car case ${i + 1}/${requests.length}: Missing required fields: ${missingFields.join(', ')}`,
        );
        return {
          error: {
            status: 'ERROR: Unprocessable Entity - semantic errors',
            code: 422,
            message: [],
            fields: missingFields,
            error: `Validation failed for car case ${i + 1}/${requests.length}: Missing required fields: ${missingFields.join(', ')}`,
          },
        };
      }
    }

    this.logger.log(
      `[${this.source}] Processing ${requests.length} car cases in batch`,
    );
    this.logger.log(`------------------------------------------------`);
    const result = await this.carCaseService.createCarCase(requests);

    // Case 1: Creation failed
    if (!result.success) {
      return {
        error: {
          status: 'ERROR: Bad Request - error creating car cases',
          code: 400,
          message: [],
          fields: ['car_code', 'site_object_id'],
          error: result.error || 'Unknown error during create operation',
        },
      };
    }

    // Case 2: Creation succeeded, now fetch consolidated data
    let consoData: CarCaseConsoDAO[] = [];

    if (result.data && result.data.length > 0) {
      try {
        // Extract IDs and fetch consolidated data
        const caseIds = result.data.map((carCase) => carCase.id as string);
        const consoResult = await this.carCaseConsoService.findCarCasesByIds({
          ids: caseIds,
          user_id: request.user_id, // ensure user_id is available in request
          // Optionally pass sort_order, limit, offset if needed
        });

        // Check if the consolidated data fetch was successful
        if (!consoResult.success) {
          // Create a custom error response for consolidated data fetch failure
          return {
            error: {
              status: 'ERROR: Bad Request - error retrieving consolidated data',
              code: 400,
              message: [],
              fields: [],
              error:
                consoResult.error || 'Failed to retrieve consolidated view',
            },
          };
        }

        consoData = consoResult.data || [];
        this.logger.log(
          `[${this.source}] Successfully fetched ${consoData.length} consolidated car cases`,
        );
      } catch (error) {
        // Case 3: Failed to retrieve consolidated data
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        this.logger.error(
          `[${this.source}] Error retrieving consolidated car cases: ${errorMessage}`,
        );

        // Create a custom error response for consolidated data fetch failure
        return {
          error: {
            status: 'ERROR: Bad Request - error retrieving consolidated data',
            code: 400,
            message: [],
            fields: [],
            error: `Created car cases but failed to retrieve consolidated view: ${errorMessage}`,
          },
        };
      }
    }

    // Case 4: Return successful response
    this.logger.log(`[${this.source}] Returning ${consoData.length} car cases`);

    return {
      success: {
        status: 'success',
        code: 201, // Created
        message: consoData,
      },
    };
  }
}
