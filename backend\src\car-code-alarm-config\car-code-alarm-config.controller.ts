import { Controller, Logger, UseFilters, UseGuards } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AclGuard } from '../client/acl/acl.guard';
import { AclExceptionFilter } from '../client/acl/acl-exception.filter';
import { CarCodeAlarmConfigService } from './car-code-alarm-config.service';
import {
  handleControllerError,
  handleMissingFieldsError,
} from '../common/utils/controller-utils';
import {
  CarCodeAlarmConfigDAO,
  CarCodeAlarmConfigsResponse,
  CreateCarCodeAlarmConfigsRequest,
  DeleteCarCodeAlarmConfigRequest,
  FindCarCodeAlarmConfigsRequest,
  PingResponse,
  UpdateCarCodeAlarmConfigRequest,
} from './interfaces';

@Controller()
export class CarCodeAlarmConfigController {
  private readonly logger = new Logger(CarCodeAlarmConfigController.name);

  constructor(
    private readonly carCodeAlarmConfigService: CarCodeAlarmConfigService,
  ) {}

  @GrpcMethod('CarWrapperService', 'PingCarCodeAlarmConfig')
  pingCarCodeAlarmConfig(): PingResponse {
    return { message: 'Car Code Alarm Config Service is running' };
  }

  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'FindCarCodeAlarmConfigs')
  async findCarCodeAlarmConfigs(
    request: FindCarCodeAlarmConfigsRequest,
  ): Promise<CarCodeAlarmConfigsResponse> {
    this.logger.log(
      `Received request to find car code alarm configurations with filters: ${JSON.stringify(request)}`,
    );

    // Call the service method
    const result =
      await this.carCodeAlarmConfigService.findCarCodeAlarmConfigs(request);

    // Case 1: Find failed
    if (!result.success) {
      return {
        error: {
          status: 'ERROR: Bad Request - client-provided data caused the error',
          code: 400,
          message: result.errors || [],
          fields: [],
          error: result.error || 'Unknown error during find operation',
        },
      };
    }

    // Case 2: Find succeeded
    this.logger.log(
      `Returning ${result.data?.length || 0} car code alarm configurations`,
    );
    return {
      success: {
        status: 'success',
        code: 200,
        message: result.data || [],
      },
    };
  }

  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'CreateCarCodeAlarmConfig')
  async createCarCodeAlarmConfig(
    request: CreateCarCodeAlarmConfigsRequest,
  ): Promise<CarCodeAlarmConfigsResponse> {
    // Handle both single and batch requests
    const requests = request.requests || [];

    if (requests.length === 0) {
      this.logger.error(
        'No car code alarm config requests provided in the batch request',
      );
      return {
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404,
          message: [],
          fields: [],
          error:
            'No car code alarm config requests provided in the batch request',
        },
      };
    }

    // Basic validation for required fields
    for (let i = 0; i < requests.length; i++) {
      const req = requests[i];
      const missingFields: string[] = [];

      if (!req.car_code) missingFields.push('car_code');
      if (!req.name) missingFields.push('name');
      if (req.should_raise_alarm === undefined)
        missingFields.push('should_raise_alarm');
      if (!req.updated_by) missingFields.push('updated_by');

      if (missingFields.length > 0) {
        return handleMissingFieldsError<CarCodeAlarmConfigDAO>(
          missingFields,
          'create',
          this.logger,
          `item ${i + 1}/${requests.length}`,
        );
      }
    }

    this.logger.log(
      `Processing ${requests.length} car code alarm configs in batch`,
    );
    this.logger.log(`------------------------------------------------`);

    // Call the service to create the car code alarm configs
    const result =
      await this.carCodeAlarmConfigService.createCarCodeAlarmConfig(requests);

    // Case 1: Creation failed
    if (!result.success) {
      // Check if it's a duplicate entry error (409 Conflict)
      if (result.error && result.error.includes('already exists')) {
        // Convert problematic objects to JSON strings
        const messageArray: string[] = Array.isArray(result.errors)
          ? result.errors.map((obj) => JSON.stringify(obj))
          : [];

        return {
          error: {
            status: 'ERROR: Conflict - Duplicate entry',
            code: 409, // Conflict - duplicate entry
            message: messageArray,
            fields: ['car_code'],
            error: result.error,
          },
        };
      }

      // Use the common error handler for other error types
      return handleControllerError<CarCodeAlarmConfigDAO>(result, 'create');
    }

    // Case 2: Creation succeeded
    this.logger.log(
      `Successfully created ${result.data?.length || 0} car code alarm configs`,
    );
    return {
      success: {
        status: 'success',
        code: 201, // Created
        message: result.data || [],
      },
    };
  }

  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'UpdateCarCodeAlarmConfig')
  async updateCarCodeAlarmConfig(
    request: UpdateCarCodeAlarmConfigRequest,
  ): Promise<CarCodeAlarmConfigsResponse> {
    // Extract car_code from the request object
    const { car_code } = request.request;
    this.logger.log(
      `Received request to update car code alarm configuration for ${car_code}`,
    );

    // Validate required fields
    const missingFields: string[] = [];
    if (!request.request.updated_by) missingFields.push('updated_by');
    if (missingFields.length > 0) {
      return handleMissingFieldsError<CarCodeAlarmConfigDAO>(
        missingFields,
        'update',
        this.logger,
      );
    }

    // Update the car code alarm config
    const result =
      await this.carCodeAlarmConfigService.updateCarCodeAlarmConfig(
        request.request,
      );
    // Case 1: Update failed
    if (!result.success)
      return handleControllerError<CarCodeAlarmConfigDAO>(result, 'update');
    // Case 2: Update succeeded
    this.logger.log(
      `Successfully updated car code alarm config for ${car_code}`,
    );
    return {
      success: {
        status: 'success',
        code: 200,
        message: result.data || [],
      },
    };
  }

  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  @GrpcMethod('CarWrapperService', 'DeleteCarCodeAlarmConfig')
  async deleteCarCodeAlarmConfig(
    request: DeleteCarCodeAlarmConfigRequest,
  ): Promise<CarCodeAlarmConfigsResponse> {
    this.logger.log(
      `Received request to delete car code alarm configuration for ${request.car_code}`,
    );
    // Validate required fields
    const missingFields: string[] = [];
    if (!request.car_code) missingFields.push('car_code');
    if (!request.updated_by) missingFields.push('updated_by');
    if (missingFields.length > 0) {
      return handleMissingFieldsError<CarCodeAlarmConfigDAO>(
        missingFields,
        'delete',
        this.logger,
      );
    }

    // Delete the car code alarm config
    const result =
      await this.carCodeAlarmConfigService.deleteCarCodeAlarmConfig(
        request.car_code,
        request.updated_by,
      );
    // Case 1: Delete failed
    if (!result.success) {
      // Create a type-safe error response
      return {
        error: {
          status: result.error?.includes('not found')
            ? 'ERROR: Not Found - no resources to process'
            : 'ERROR: Bad Request - client-provided data caused the error',
          code: result.error?.includes('not found') ? 404 : 400,
          message: result.errors || [],
          fields: ['car_code'],
          error: result.error || 'Unknown error during delete',
        },
      };
    }
    // Case 2: Delete succeeded
    this.logger.log(
      `Successfully deleted car code alarm config for ${request.car_code}`,
    );
    return {
      success: {
        status: 'success',
        code: 200,
        message: [], // Empty array since we don't have any DAO objects to return after deletion
      },
    };
  }
}
