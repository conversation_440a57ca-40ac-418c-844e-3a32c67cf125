FROM node:22-alpine

WORKDIR /usr/src/app

# Set environment variables for development
ENV NODE_ENV=development
ENV DOCKER_ENVIRONMENT=true
ENV WEBPACK_WATCH=true
ENV CHOKIDAR_USEPOLLING=true
ENV MICROSERVICE_SECRET_KEY=aCHW7ds8xn

# Copy package files and install dependencies
COPY package*.json ./

# Install dependencies and NestJS CLI globally
# Include development dependencies for HMR and webpack
RUN npm ci && npm install -g @nestjs/cli

# Copy .env.docker file for configuration
COPY .env.docker ./

# Copy the rest of the application
# This is done last to optimize Docker layer caching
COPY . .

# Ensure dist directory exists and is empty before building
RUN rm -rf dist && mkdir -p dist

# Create proto directory in the expected location
RUN mkdir -p /usr/src/app/proto

# Copy proto files to both locations to ensure they're found
COPY src/proto/*.proto /usr/src/app/proto/
COPY src/proto/*.proto /usr/src/app/src/proto/

# Expose the gRPC port
EXPOSE 8081

# Command to run the application in development mode with HMR
CMD ["npm", "run", "start:docker:dev"]
