/**
 * Common service utility functions and interfaces
 */

import { FindOperator } from 'typeorm';
import { Logger } from '@nestjs/common';
import { buildDateFilter } from './date-utils';

/**
 * Generic service response interface for all service operations
 */
export interface ServiceResponse<T> {
  success: boolean;
  data?: T[];
  error?: string;
  errors?: string[];
}

/**
 * Applies date filtering to a where clause
 * @param startDate Optional start date string
 * @param endDate Optional end date string
 * @param logger Logger instance for error reporting
 * @returns A tuple with [success, dateFilter, errorResponse]
 *   - If success is true, dateFilter contains the FindOperator for the date filter
 *   - If success is false, errorResponse contains the error details
 */
export function applyDateFiltering<T>(
  startDate?: string,
  endDate?: string,
  logger?: Logger,
): [boolean, FindOperator<Date> | undefined, ServiceResponse<T> | undefined] {
  try {
    const updatedAtFilter = buildDateFilter(startDate, endDate) as
      | FindOperator<Date>
      | undefined;
    return [true, updatedAtFilter, undefined];
  } catch (error: unknown) {
    const errorMessage: string =
      error instanceof Error
        ? error.message
        : 'Unknown error occurred while filtering by date';

    if (logger) {
      logger.error(`Error in date filtering: ${errorMessage}`);
    }

    return [
      false,
      undefined,
      {
        success: false,
        error: `Error in date filtering: ${errorMessage}`,
      },
    ];
  }
}
