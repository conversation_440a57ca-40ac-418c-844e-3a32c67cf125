INSERT INTO carwrapper.car_code_alarm_config (
    car_code,
    name,
    description,
    category,
    device_type,
    should_raise_alarm,
    updated_at,
    updated_by
)
SELECT
    cd.car_code,
    cd.name,
    NULL AS description, -- description is not in CAR_DEFINITION, set to NULL
    cd.category,
    cd.dev_type AS device_type, -- map dev_type to device_type
    TRUE AS should_raise_alarm, -- based on new design, default to true for initial migration
    NOW() AS updated_at, -- Set updated_at to the current timestamp as current table does not have this column
    'migration-script' AS updated_by -- Indicate the update was done by the migration script as current table does not have this column
FROM
    carwrapper.car_definition cd;
