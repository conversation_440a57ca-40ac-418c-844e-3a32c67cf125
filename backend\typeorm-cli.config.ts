/**
 * TypeORM CLI Configuration
 * 
 * This file is used by TypeORM CLI tools for database migrations.
 * It's separate from the main application configuration to allow
 * running migration commands without starting the full application.
 */

import { appConfig } from './src/config/app.config';

module.exports = {
  type: 'postgres',
  host: appConfig.database.host,
  port: appConfig.database.port,
  username: appConfig.database.username,
  password: appConfig.database.password,
  database: appConfig.database.database,
  schema: appConfig.database.schema,
  synchronize: false,
  logging: true,
  entities: ['src/**/*.entity{.ts,.js}'],
  migrations: ['src/migrations/*{.ts,.js}'],
  cli: {
    entitiesDir: 'src/entities',
    migrationsDir: 'src/migrations'
  }
};
