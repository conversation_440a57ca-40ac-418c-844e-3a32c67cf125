# Car Case Test Data

This directory provides scripts to quickly generate sample data for car case testing. All scripts use shared config from `../shared/test_data_config.json` for consistency.

## Scripts

- **General Car Case Generator** (`generate_car_case_test_data.py`)
  - Outputs a plain JSON array of car cases.
  - Use for general testing, mocking, or seeding databases.
- **Open/Closed Case Detection Generator** (`generate_open_closed_test_data.py`)
  - Outputs car code configs and car cases, with logging dates for open/closed status testing.

## How to Use

### 1. Generate Car Cases (General)

```bash
python generate_car_case_test_data.py           # 10 cases (default)
python generate_car_case_test_data.py --count=20 # 20 cases
```

- Output: `output/<timestamp>/car_cases.json`

### 2. Generate Open/Closed Case Data

```bash
chmod +x generate_open_closed_test_data.py      # (first time only)
./generate_open_closed_test_data.py             # Mixed open/closed
./generate_open_closed_test_data.py --status=open   # Only open cases
./generate_open_closed_test_data.py --status=close  # Only closed cases
./generate_open_closed_test_data.py --count=20      # 20 cases
./generate_open_closed_test_data.py --date-offset=10 # Use a different reference date
./generate_open_closed_test_data.py --status=close --count=20 # pass multi flags
```

- Output: `output/<timestamp>_<status>/car_code_configs.json` and `car_cases.json`

## Output Example

```bash
output/
  ├── 20250415_123045_open/
  │   ├── car_code_configs.json
  │   └── car_cases.json
  └── 20250415_124230_close/
      └── ...
```

- Each run creates a new timestamped folder for easy organization and history.

## Prerequisites

- For `generate_car_case_test_data.py`: Make sure car code configs exist in your DB.
  - You can seed with:

    ```bash
    cd ../car-code-config
    node seed_car_code_config.js
    cd ../car-case
    ```

- For `generate_open_closed_test_data.py`: No manual seeding needed; it generates everything you need.

## Shared Test Data Config

- All scripts use `../shared/test_data_config.json` for car codes, site IDs, and assignments.
- To change test data structure, update this file (do not hardcode values elsewhere).

---
**Tip:** Delete old folders in `output/` to keep your workspace clean.
