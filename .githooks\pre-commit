#!/bin/bash

# Pre-commit hook to ensure DB_SSL is set to true in .env.docker before committing
# This hook automatically changes DB_SSL=false to DB_SSL=true in .env.docker
# and adds a comment to indicate that this value should be true for commits

ENV_DOCKER_FILE="backend/.env.docker"

# Check if .env.docker exists
if [ -f "$ENV_DOCKER_FILE" ]; then
    echo "Checking DB_SSL setting in $ENV_DOCKER_FILE..."
    
    # Check if DB_SSL is set to false
    if grep -q "DB_SSL=false" "$ENV_DOCKER_FILE"; then
        echo "Changing DB_SSL from false to true for commit..."
        
        # Replace DB_SSL=false with DB_SSL=true and add a comment
        sed -i 's/DB_SSL=false/DB_SSL=true # IMPORTANT: Must be true for commits, can be false for local development/' "$ENV_DOCKER_FILE"
        
        # Stage the modified file
        git add "$ENV_DOCKER_FILE"
        
        echo "Successfully updated DB_SSL to true in $ENV_DOCKER_FILE"
    else
        echo "DB_SSL is already set to true in $ENV_DOCKER_FILE"
    fi
else
    echo "Warning: $ENV_DOCKER_FILE not found"
    exit 1
fi

# Continue with the commit
exit 0
