// @ts-check
/**
 * ESLint Configuration File
 *
 * This file configures ESLint for the project, which is a static code analysis tool that
 * identifies and reports on patterns in JavaScript/TypeScript code. It helps maintain
 * code quality and consistency across the codebase.
 *
 * Key features of this configuration:
 * - Uses TypeScript ESLint for TypeScript-specific linting
 * - Integrates with <PERSON><PERSON><PERSON> for code formatting
 * - Configures specific rules for the project's coding standards
 * - Sets up global variables for Node.js and Jest environments
 */

import eslint from '@eslint/js';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import globals from 'globals';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  {
    ignores: ['eslint.config.mjs'],
  },
  eslint.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  eslintPluginPrettierRecommended,
  {
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
      ecmaVersion: 5,
      sourceType: 'module',
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'off', // Warn instead of off to encourage better typing
      '@typescript-eslint/no-floating-promises': 'warn', // Upgrade to error to prevent unhandled promises
      '@typescript-eslint/no-unsafe-argument': 'warn', // Upgrade to error for better type safety
      'no-console': ['warn', { allow: ['warn', 'error', 'info'] }], // Discourage console.log but allow others
      'no-unused-vars': 'off', // Use TypeScript's version instead
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }], // Error for unused vars except those starting with _
      'prefer-const': 'error', // Prefer const over let when possible
      'eqeqeq': ['error', 'always'], // Always use === instead of ==
    },
  },
);