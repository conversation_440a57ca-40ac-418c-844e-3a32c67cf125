---
marp: true
theme: default
paginate: true
header: "Car Wrapper Service"
footer: "© 2025 NZOS"
style: |
  section {
    background-color: #ffffff;
    font-family: Arial, sans-serif;
  }
  h1 {
    color: #2c3e50;
  }
  h2 {
    color: #3498db;
  }
  code {
    background-color: #f8f8f8;
    color: #e74c3c;
  }
---



<!-- _class: first-slide -->

# Car Wrapper Service
## Feature Highlights

![bg right:40% contain](./images/car-wrapper-service-icon.png)

---

# Table of Contents (1/3)

**1. Design Docs**
   Technical documentation for architecture and design decisions.

**2. ReadMe File**
   Installation, configuration, and troubleshooting guides.

**3. How to start the App**
   Quick-start guide for running the application.

---

# Table of Contents (2/3)

**4. Features of the App (Car Wrapper Service Overview)**
   Core functionality and capabilities:
   4.1. Introduction & Architecture
   4.2. Core Features
   4.3. Implementation Details
      4.3.1. Car Case API & Controller Flow
      4.3.2. Car Code Config API & Controller Flow
      4.3.3. Car Code Alarm Config API & Controller Flow
      4.3.4. Car Code Config History API & Controller Flow
      4.3.5. Car Code Alarm Config History API & Controller Flow
   4.4. API_DOCS.yaml file
   4.5. Unit Test

---

# Table of Contents (3/3)

**5. Generating Dummy Data for the API**
   Tools for creating test data for API validation.

**6. Future Roadmap**
   Planned enhancements and upcoming features.

---

# 1. Design Docs

- **Location**: `docs/planning/DESIGN_DOCS.md`
- **Description**: We use the design docs to describe the solutioning of this application.

- **Contents**:
  - Executive Summary
  - Architecture Overview
  - Data Models
  - API Design
  - Design Constraints
  - Business Impact Analysis

---

# 2. ReadMe File

- **Location**: `README.md`
- **Description**: We use this for Installation, configuration, and troubleshooting guides.

- **Contents**:
  - Project Overview
  - Installation Instructions
  - API Documentation
  - Testing Procedures
  - Deployment Guidelines
  - Troubleshooting Tips

---

# 3. How to Start the App

- **Development Environment**:
  ```bash
  # Clone the repository
  git clone https://<EMAIL>/sembcorp-eos/NetzeroOS/_git/NZOS-CarWrapperService

  # Install dependencies
  cd NZOS-CarWrapperService
  npm install

  # Start the development environment
  podman compose -f docker-compose.dev.yml up
  ```

- **Production Environment**:
  ```bash
  # Start the production environment
  podman compose up
  ```

---

# 4.1. Introduction & Architecture

- **Car Wrapper Service**: A NestJS-based gRPC microservice for handling car wrapper operations
- **Purpose**: Processes CAR cases from synapse with endpoints served via gRPC
- **Tech Stack**:
  - NestJS framework
  - gRPC for API communication
  - PostgreSQL with TypeORM
  - Docker/Podman for containerization

---

# 4.1.1. Tech Stack Architecture

---

<!-- Full slide dedicated to the Tech Stack Architecture -->
![bg contain](./images/tech-stack.png)

---

# 4.1.2. NestJS Architectural Pattern

- **Controller**: Handles gRPC requests and routes them to appropriate services
- **Service**: Implements business logic and orchestrates operations
- **Repository**: Abstracts database operations and data access
- **Entity**: Defines data structure and relationships

---

<!-- Full slide dedicated to the NestJS Architecture Diagram -->
![bg contain](./images/nestjs-architecture.png)

---

# 4.2.1. Core Feature: Car Case Management

- **Intelligent Case Grouping**:
  - Automatic case ID generation based on tags
  - Groups related incidents under single case IDs
  - Tracks occurrence counts and first/most recent occurrences

---

# *******. Case Grouping Process

- **Grouping Logic**:
  - Extract tags in format: <br> `car_code`-`site_object_id`-`sub_site_object_id`-`device_id`
  - Group incidents with identical tags (same combination of all four values)
  - Use monitoring window from CAR_CODE_CONFIG to determine case grouping
  - Reuse case_id if new incident occurs within monitoring window
  - Create new case_id if incident occurs after monitoring window expires

---

<!-- Full slide dedicated to the Case Grouping Flow Diagram -->
![bg contain](./images/case-grouping-flow.png)

---

# *******. Flow Diagram For Incoming Car Cases

---

<!-- Full slide dedicated to the Flow Diagram -->
![bg contain](./images/flow-diagram-car-cases.png)

---

# *******. Car Case Management: Consolidated Views

- **Consolidated Query Service**:
  - `CarCaseConsoService` provides optimized queries
  - Returns all related IDs belonging to the same case
  - Improves performance for complex case queries

- **Benefits**:
  - Single query for all related incidents
  - Automatic status calculation
  - Efficient data retrieval for reporting

---

# 4.2.1.2. CarCaseConsoDAO Example

```json
{
  "id": "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc",
  "conso_id": [
    "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc",
    "4c6a6333-0412-4e79-8726-5894457c6937",
    "23294c01-e167-4815-ad14-353e480fe0d8"
  ],
  "site_object_id": "site_1",
  "sub_site_object_id": "sub_002",
  "device_id": "dev_039",
  "tags": "hso-site_1-sub_002-dev_039",
  "case_id": "hso-site_1-sub_002-dev_039-1",
  "car_code": "hso",
  "source": "solar_os",
  "title": "Case: hso detected at site_1",
  "occurrence_count": 3,
  "first_occurrence": "2025-04-15T09:13:08.939Z",
  "most_recent_occurrence": "2025-04-20T09:13:08.939Z",
  "status": "open"
}
```

---

# 4.2.2. Core Feature: Open/Closed Case Detection

- **Dynamic Status Calculation**:
  - Status determined at query time based on monitoring window
  - No need to store or update status in database
  - Always reflects current state based on latest data



---

# 4.2.2.1. Open/Closed Case Detection: Implementation

- **Monitoring Window Logic**:
  ```sql
  CASE
    WHEN ccc.monitoring_window IS NULL THEN ''
    WHEN NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window THEN 'open'
    ELSE 'close'
  END as status
  ```

![](./images/case-detection.png)



---

# 4.2.3.1 Core Feature: Car Code Config

- **Car Code Configurations**:
  - Site-specific settings for each car code
  - Configurable monitoring windows
  - Custom thresholds and recommendations



---

# 4.2.3.2. Car Code Config: Site and Car Code Mapping



| Site ID | Code |     | Site ID | Code |
|---------|------|-----|---------|------|
| site_1  | HSO  |     | site_6  | DRS  |
| site_2  | AMD  |     | site_7  | HYM  |
| site_3  | HTI  |     | site_8  | CLD  |
| site_4  | APD  |     | site_9  | APR  |
| site_5  | HSH  |     | site_10 | HDG  |

---

# 4.2.4. Car Code Alarm Config

- **Alarm Configurations**:
  - Controls which car codes should raise alarms
  - Categorization of car codes by device type
  - Descriptive metadata for each car code

- **Benefits**:
  - Centralized alarm configuration
  - Consistent alarm behavior
  - Easy maintenance and updates

---

# *******. Car Code Alarm Config: Monitoring Windows

| Car Code | Days |     | Car Code | Days |
|----------|------|-----|----------|------|
| AMD      | 3    |     | HDG      | 2    |
| APD      | 7    |     | HSH      | 8    |
| APR      | 1    |     | HSO      | 4    |
| CLD      | 9    |     | HTI      | 6    |
| DRS      | 5    |     | HYM      | 10   |

---

# 4.2.5. Core Feature: History Tracking & Audit Logging

- **Complete Audit Trail**:
  - All create/update/delete operations tracked
  - Old and new values preserved for comparison
  - User attribution with updated_by field

---

<!-- Full slide dedicated to the Config Lifecycle Persistence Diagram -->
![bg contain](./images/config-lifecycle-persistence-simple.png)

---

# *******. History Tracking: Implementation

- **Transaction-Based History**:
  - History records created within same transaction as main operation
  - Automatic rollback if history creation fails
  - Ensures data integrity and audit compliance

- **Structured History Data**:
  - Standardized format for all history records
  - JSON storage of complex objects
  - Queryable history with filtering capabilities

---

<!-- Full slide dedicated to the Persistence Logic Flow Diagram -->
![bg contain](./images/persistence-logic-flow.png)

---

# 4.3.1. API Design

- **gRPC Service Implementation**:
  - Strongly typed interfaces with Protocol Buffers
  - Efficient binary communication
  - Service definitions in `car-wrapper-service.proto`

- **Consistent Response Pattern**:
  - Standardized success/error response structure
  - Detailed error information
  - Pagination and filtering support



---

# 4.3.1.1. API Endpoints: Car Case

- **Available Endpoints**:
  ```
  CarWrapperService.PingCarCase
  CarWrapperService.CreateCarCase
  CarWrapperService.FindCarCases
  CarWrapperService.FindCarCasesByIds
  ```

- **Key Features**:
  - Automatic case ID generation
  - Dynamic status calculation
  - Pagination and filtering support



---

# 4.3.1.2. Car Case Controller Flow

---

<!-- Full slide dedicated to the Car Case Controller Flow Diagram -->
![bg contain](./images/car-case-controller-flow.png)

---

# 4.3.2. API Endpoints: Car Code Config

- **Available Endpoints**:
  ```
  CarWrapperService.PingCarCodeConfig
  CarWrapperService.CreateCarCodeConfig
  CarWrapperService.UpdateCarCodeConfig
  CarWrapperService.FindCarCodeConfigs
  CarWrapperService.FindCarCodeConfigsByIds
  ```

- **Key Features**:
  - Site-specific configuration
  - Monitoring window settings
  - Threshold management
  - Recommendation templates

---

# *******. Car Code Config Controller Flow

---

<!-- Full slide dedicated to the Car Code Config Controller Flow Diagram -->
![bg contain](./images/car-code-config-controller-flow.png)



---

# 4.3.3. API Endpoints: Car Code Alarm Config

- **Available Endpoints**:
  ```
  CarWrapperService.PingCarCodeAlarmConfig
  CarWrapperService.CreateCarCodeAlarmConfig
  CarWrapperService.UpdateCarCodeAlarmConfig
  CarWrapperService.FindCarCodeAlarmConfigs
  CarWrapperService.FindCarCodeAlarmConfigsByIds
  ```

- **Key Features**:
  - Alarm enablement controls
  - Descriptive metadata management

---

# *******. Car Code Alarm Config Controller Flow

---

<!-- Full slide dedicated to the Car Code Alarm Config Controller Flow Diagram -->
![bg contain](./images/car-code-alarm-config-controller-flow.png)

---

# 4.3.4. API Endpoints: Car Code Config History

- **Available Endpoints**:
  ```
  CarWrapperService.FindCarCodeConfigHistories
  CarWrapperService.FindCarCodeConfigHistoriesByConfigId
  ```

- **Key Features**:
  - Complete audit trail
  - Change tracking
  - User attribution
  - Timestamp tracking

---

# *******. Car Code Config History Controller Flow

---

<!-- Full slide dedicated to the Car Code Config History Controller Flow Diagram -->
![bg contain](./images/car-code-config-history-controller-flow.png)

---

# 4.3.5. API Endpoints: Car Code Alarm Config History

- **Available Endpoints**:
  ```
  CarWrapperService.FindCarCodeAlarmConfigHistories
  CarWrapperService.FindCarCodeAlarmConfigHistoriesByConfigId
  ```

- **Key Features**:
  - Complete audit trail
  - Change tracking
  - User attribution
  - Timestamp tracking

---

# *******. Car Code Alarm Config History Controller Flow

---

<!-- Full slide dedicated to the Car Code Alarm Config History Controller Flow Diagram -->
![bg contain](./images/car-code-alarm-config-history-controller-flow.png)

---

# 4.3.6. API Security: ACL Guards & Filters

- **Access Control Implementation**:
  ```typescript
  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  ```

- **Key Benefits**:
  - **Authorization Control**: Verifies user permissions before accessing endpoints
  - **Granular Access Management**: Controls access based on user roles and permissions
  - **Consistent Error Handling**: Standardized responses for unauthorized access attempts

---

# *******. ACL Implementation Details

- **Separation of Concerns**: Decouples authorization logic from business logic

- **Implementation Pattern**:
  - Applied at controller or method level
  - Automatically validates authorization headers

- **Request Interception**:
  - Simply adding these decorators automatically intercepts requests
  - Security checks occur before execution reaches controller code
  - Provides "fail-fast" approach to unauthorized access attempts

---

# *******. ACL Implementation Example

```typescript
// Controller method with ACL protection
@UseGuards(AclGuard)
@UseFilters(AclExceptionFilter)
@GrpcMethod('CarWrapperService', 'FindCarCases')
async findCarCases(
  request: FindCarCasesRequest = {},
): Promise<CarCasesResponse> {
  // Method implementation is only executed
  // if ACL Guard allows the request
  
  // ... existing implementation
}
```

- **No manual security checks needed in controller code**
- **Clean separation between authorization and business logic**
- **Applied consistently across all protected endpoints**

---

# 4.3.2. Data Management

- **Entity Design**:
  - `CAR_CASES`: Stores individual car case records
  - `CAR_CODE_CONFIG`: Configuration settings by site and car code
  - `CAR_CODE_ALARM_CONFIG`: Alarm settings for car codes
  - History tables for audit tracking

---

# *******. Entity Relation Diagram (ER Diagram)

---

<!-- Full slide dedicated to the ER diagram -->
![bg contain](./images/er-diagram.png)

---

# *******. Data Retention & Management

- **Data Retention Policies**:
  - Current configurations: Indefinite retention
  - History records: 1 year retention
  - Car cases: 2 years from last logging date

- **Data Management Features**:
  - Automatic history tracking
  - Transaction-based operations
  - Optimized queries for reporting



---

# 4.5. Unit Test

- **Test Structure**:
  ```
  test/
  ├── car-case/
  │   ├── car-case.controller.spec.ts  # Tests controller logic
  │   └── car-case.service.spec.ts     # Tests business logic
  ├── car-code-config/
  │   ├── car-code-config.controller.spec.ts
  │   └── car-code-config.service.spec.ts
  ```

- **Test Data Generation**:
  - Custom scripts for generating realistic test data
  - Support for various test scenarios (open/closed cases)
  - Timestamped output for test organization



---

# 4.5.1. Testing Approach

- **What We Test**:
  - **Service Tests**: Business logic, data processing, error handling
  - **Controller Tests**: Request handling, response formatting, service integration

- **Testing Tools**:
  - Jest for unit testing
  - Mock repositories for database isolation
  - `grpc-tools.sh` script for command-line API testing



---

# 4.3.3. Deployment & Operations

- **Container-Based Deployment**:
  - Docker/Podman configuration for development and production
  - PostgreSQL database included in container setup
  - CloudBeaver for database management in development



---

# 4.3.4. Environment Configuration

- **Environment Variables**:
  - Environment-specific settings via .env files
  - Consistent port configuration (8081 for gRPC service)
  - Feature flags for optional functionality

---

# 4.3.7. Application Constants

- **Query Limits**:
  - CAR_CASES: 1000 records (default for car case queries)
  - HISTORY_LOGS: 2000 records (default for history logs)
  - CONFIG: 500 records (default for configuration queries)
  - CAR_CODE_ALARM_CONFIG_BATCH: 250 records (max batch size)

- **Sort Settings**:
  - DEFAULT_SORT_ORDER: "DESC" (newest first)
  - SORT_FIELD: "logging_date" (primary sort field)

> *constants.js* file is created to manage all these application configs.

---

# 4.4. API_DOCS.yaml file

- **OpenAPI Documentation**:
  - Located at `docs/api/API_DOCS.yaml`
  - Comprehensive API reference
  - Detailed request/response schemas
  - Example values for all parameters

- **Key Components**:
  - Car Case endpoints and data models
  - Car Code Config endpoints and data models
  - Car Code Alarm Config endpoints and data models
  - Error response formats

---

# 5. Generating Dummy Data for the API

- **Test Data Scripts**:
  - Located in `backend/test/` directory
  - Python scripts for generating realistic test data
  - Supports various test scenarios

- **Output**:
  - JSON files with sample requests
  - Ready-to-use gRPC command examples
  - Timestamped output directories

---

# 5.0.1. Test Data Folder Structure

Dummy data will be generated in the output folder.
```
backend/test/
├── car-case/
│   ├── generate_car_case_test_data.py
│   └── output/
│       └── [timestamped folders]
├── car-code-config/
│   ├── generate_car_code_config_test_data.py
│   └── output/
│       └── [timestamped folders]
└── car-code-alarm-config/
    ├── generate_car_code_alarm_config_test_data.py
    └── output/
        └── [timestamped folders]
```

---

# 5.1. Car Case Test Data Generation

- **Script**: `backend/test/car-case/generate_car_case_test_data.py --count 10`

- **Features**:
  - Generates realistic car case records
  - Creates proper case IDs based on tags
  - Includes random metadata and recommendations
  - Supports open/closed case testing

---

# 5.1.1. Car Case Example Output

```json
{
  "site_object_id": "site_1",
  "sub_site_object_id": "sub-004",
  "device_id": "dev-022",
  "tags": "hso-site_1-sub-004-dev-022",
  "case_id": "CASE-site_1-hso-0",
  "car_code": "hso",
  "source": "thermal_os",
  "title": "Case: hso detected at site_1",
  "description": "Test case for hso issue on site_1 with monitoring period of 4 days. This case is 1 days old.",
  "logging_date": "2025-04-28T18:00:23.148811",
  "recommendation": {
    "details": {
      "action": "Optimize system settings"
    }
  },
  "metadata": {
    "details": {
      "daily_energy_loss": "220",
      "damage_type": "crack"
    }
  }
}
```

---

# 5.2. Car Code Config Test Data Generation

- **Script**: `backend/test/car-code-config/generate_car_code_config_test_data.py --count 10`

- **Features**:
  - Creates site-specific configurations
  - Sets monitoring windows for open/closed detection
  - Generates thresholds and recommendations
  - Includes updated_by and updated_at fields

---

# 5.2.1. Car Code Config Example Output

```json
{
  "site_object_id": "site_1",
  "car_code": "hso",
  "monitoring_window": 4,
  "enabled": true,
  "thresholds": {
    "threshold1": "10",
    "threshold2": "20"
  },
  "recommendation": {
    "action": "Recommended action for hso",
    "priority": "medium"
  },
  "updated_at": "2025-04-28T18:00:23.148811",
  "updated_by": "test-script"
}
```

---

# 5.3. Car Code Alarm Config Test Data Generation

- **Script**: `backend/test/car-code-alarm-config/generate_car_code_alarm_config_test_data.py --count 10`

- **Features**:
  - Creates alarm settings for car codes
  - Assigns device types and categories
  - Sets alarm enablement flags
  - Includes descriptive metadata

---

# 5.3.1. Car Code Alarm Config Example Output

```json
{
  "car_code": "hso",
  "name": "HSO Alarm Configuration",
  "description": "Configuration for HSO alarms and notifications",
  "category": "Performance",
  "device_type": "Inverter",
  "should_raise_alarm": true,
  "updated_at": "2025-04-28T18:00:23.148811",
  "updated_by": "test-script"
}
```

---

# 6. Future Roadmap

- **Planned Enhancements**:
  - Add logic to push notification when car cases is added
  - Add logic to push alarm when car cases is added

- **Scalability Considerations**:
  - Horizontal scaling for increased load
  - Performance optimizations for large datasets
  - Caching strategies for frequent queries



---

# Thank You!

- **Questions?**
- **Repository**: https://dev.azure.com/sembcorp-eos/NetzeroOS/_git/NZOS-CarWrapperService


