import { Entity, Column, PrimaryColumn } from 'typeorm';
import { DB_TABLES } from '../../common/constants';

@Entity(DB_TABLES.CAR_CODE_CONFIG)
export class CarCodeConfig {
  @PrimaryColumn({ type: 'varchar' })
  site_object_id: string;

  @PrimaryColumn({ type: 'varchar' })
  car_code: string;

  @Column({ type: 'int', nullable: false })
  monitoring_window: number;

  @Column({ type: 'boolean', nullable: false, default: true })
  enabled: boolean;

  @Column({ type: 'jsonb', nullable: true })
  thresholds: Record<string, any>; // JSON object

  @Column({ type: 'jsonb', nullable: true })
  recommendation: Record<string, any>; // JSON object

  @Column({ type: 'timestamp', nullable: false })
  updated_at: Date;

  @Column({ nullable: false })
  updated_by: string;
}
