import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { appConfig } from './config/app.config';
import { CarCaseModule } from './car-case/car-case.module';
import { CarCodeConfigModule } from './car-code-config/car-code-config.module';
import { CarCodeAlarmConfigModule } from './car-code-alarm-config/car-code-alarm-config.module';
import { CarCodeAlarmConfigHistoryModule } from './car-code-alarm-config-history/car-code-alarm-config-history.module';
import { CarCodeConfigHistoryModule } from './car-code-config-history/car-code-config-history.module';
import { AclModule } from './client/acl/acl.module';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: appConfig.database.host,
      port: appConfig.database.port,
      username: appConfig.database.username,
      password: appConfig.database.password,
      database: appConfig.database.database,
      /**
       * Schema Configuration
       * - Defines the PostgreSQL schema to use for database operations
       * - Value is loaded from environment variable DB_SCHEMA or defaults to 'carwrapper'
       * - All tables should be created in this schema
       */
      schema: appConfig.database.schema,
      /**
       * NOTE: We're not using entityPrefix anymore to avoid schema name duplication.
       * The schema property above is sufficient for TypeORM to use the correct schema.
       * When using both schema and entityPrefix, it results in duplicate schema references
       * like 'carwrapper.carwrapper.table_name'
       */
      ssl: appConfig.database.ssl,
      entities: ['dist/**/*.entity{.ts,.js}'],
      /**
       * TypeORM synchronize option controls automatic schema synchronization
       * - When true: Creates new tables/columns but WILL NOT remove existing columns
       * - When false: Makes no schema changes; use migrations for schema updates
       *
       * IMPORTANT:
       * - Enable only in development to prevent accidental data loss
       * - Columns removed from entities but present in DB will remain in DB
       * - Use migrations for production schema changes
       *
       * Synchronization is false by default unless explicitly enabled via DB_SYNCHRONIZE=true
       */
      synchronize: process.env.DB_SYNCHRONIZE === 'true',
      autoLoadEntities: true,
    }),
    CarCaseModule,
    CarCodeConfigModule,
    CarCodeAlarmConfigModule,
    CarCodeAlarmConfigHistoryModule,
    CarCodeConfigHistoryModule,
    AclModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule implements OnModuleInit {
  private readonly logger = new Logger(AppModule.name);

  onModuleInit() {
    // Log database configuration details
    this.logger.log(`Database schema: ${appConfig.database.schema}`);
    this.logger.log(`Database host: ${appConfig.database.host}`);
    this.logger.log(`Database name: ${appConfig.database.database}`);
  }
}
