# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
*/dist
*/tmp
*/out-tsc
*/bazel-out

# Node
*/node_modules
*/npm-debug.log
*/yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.history/

# Visual Studio Code
/.vscode/*
!*/.vscode/settings.json
!*/.vscode/tasks.json
!*/.vscode/launch.json
!*/.vscode/extensions.json
*/.history/*

# Miscellaneous
*/.angular/cache
*/.sass-cache/
*/connect.lock
*/coverage
*/libpeerconnection.log
*/testem.log
*/typings
.codeiumignore

# System files
*/.DS_Store
*/Thumbs.db
*/logs/*
!/backend/logs/.gitkeep
*/mnt/*
!/backend/mnt/.gitkeep

# Test output directories
backend/test/*/output/

# Package files and build artifacts
# backend@* directories are typically created when packaging the NestJS application
# and contain compiled code, dependencies, and other build artifacts that should not be versioned
backend@*

# Jest coverage and test reports
# The jest/ directory contains test coverage reports, snapshots, and other test-related
# artifacts that are generated during test runs and can be recreated as needed
/jest/
jest/
**/jest/

# Presentation output files
# Generated presentation files (HTML, PDF) that should not be versioned
docs/presentations/output/
