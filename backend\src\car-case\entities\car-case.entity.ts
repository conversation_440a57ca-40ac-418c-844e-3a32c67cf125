import { Entity, Column, PrimaryGeneratedColumn, Generated } from 'typeorm';
import { DB_TABLES } from '../../common/constants';

@Entity(DB_TABLES.CAR_CASES)
export class CarCase {
  @PrimaryGeneratedColumn('uuid')
  @Generated('uuid')
  id: string;

  @Column({ type: 'varchar', nullable: false })
  site_object_id: string;

  @Column({ type: 'varchar', nullable: true })
  sub_site_object_id: string;

  @Column({ type: 'varchar', nullable: true })
  device_id: string;

  @Column({ type: 'json', nullable: true })
  additional_device_info: any;

  @Column({ type: 'varchar', nullable: false })
  tags: string;

  @Column({ type: 'varchar', nullable: false })
  case_id: string;

  @Column({ type: 'varchar', nullable: false })
  car_code: string;

  @Column({ type: 'varchar', nullable: false })
  source: string;

  @Column({ type: 'varchar', nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  remarks: string;

  @Column({ type: 'json', nullable: true })
  recommendation: any;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'timestamp', nullable: false })
  logging_date: Date;
}
