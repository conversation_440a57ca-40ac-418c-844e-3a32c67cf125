import { CarCaseConsoDAO } from './car-case-conso.interface';

/**
 * Individual car case data
 *
 * Note for gRPC/protobuf Value fields (e.g., metadata, recommendation, additional_device_info):
 * You must wrap arrays in { "listValue": { "values": [...] } } and objects in { "structValue": { "fields": ... } } for gRPC/protobuf Value.
 * This is a limitation of how protobuf handles dynamic types.
 */
export interface CarCaseDAO {
  id?: string; // Auto-generated by the backend, not required in request payload.
  site_object_id: string;
  sub_site_object_id?: string;
  device_id?: string;
  /**
   * Additional device information, now JSON object
   */
  additional_device_info?: any; // Optional. Omit or pass an empty object if no data. e.g. {}
  tags?: string; // Used to uniquely identify a car case grouping (e.g., car_code_1-site_object_id_1-sub_site_object_id_1-device_id_1)
  case_id?: string; // Running sequence number within a car case grouping (e.g., car_code_1-site_object_id_1-sub_site_object_id_1-device_id_1-1)
  car_code: string;
  source: string;
  title: string;
  description?: string;
  remarks?: string;
  /**
   * Recommendation details, now JSON object
   */
  recommendation?: any; // Optional. Omit or pass an empty object if no data. e.g. {}
  /**
   * Metadata, now JSON object
   */
  metadata?: any; // Optional. Omit or pass an empty object if no data. e.g. {}
  logging_date: string; // ISO 8601 format
}

/**
 * Request for batch creation of car cases
 */
export interface CreateCarCaseDAORequest {
  requests: CarCaseDAO[];
  user_id: string; // User ID for ACL check
}

export interface FindCarCasesRequest {
  site_object_id?: string;
  car_code?: string;
  start_date?: string; // ISO 8601 format
  end_date?: string; // ISO 8601 format
  status?: 'open' | 'close' | '';
  source?: string; // Filter by source (e.g., 'test', 'solar_os', 'wind_os')
  sort_order?: 'ASC' | 'DESC'; // Sort order for results (default: 'DESC')
  limit?: number; // Maximum number of records to return (default: 1000)
  offset?: number; // Offset for pagination (default: 0), recommended offset value should follow limit value
  user_id?: string; // User ID for ACL check
}

/**
 * Request to find car cases by their IDs
 */
export interface FindCarCasesByIdsRequest {
  ids: string[];
  limit?: number; // Maximum number of records to return (default: 1000)
  offset?: number; // Offset for pagination (default: 0)
  sort_order?: 'ASC' | 'DESC'; // Sort order for results (default: 'DESC')
  user_id: string; // User ID for ACL check
}

/**
 * Success response format for car cases
 */
export interface CarCasesSuccessResponse {
  status: string;
  code: number;
  message: CarCaseConsoDAO[]; // Array of car cases
}

/**
 * Error response format for car cases
 */
export interface CarCasesErrorResponse {
  status: string;
  code: number;
  message: string[]; // JSON strings of problematic objects
  fields?: string[]; // Optional array of field names for validation errors
  error?: string; // Error message for error responses
}

/**
 * Standard response format for multiple car cases
 * Follows the CarCasesRes schema from API_DOCS.yaml
 */
export interface CarCasesResponse {
  success?: CarCasesSuccessResponse;
  error?: CarCasesErrorResponse;
}

export interface PingResponse {
  message: string;
}
