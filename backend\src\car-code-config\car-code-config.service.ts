import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, FindOptionsWhere, Repository } from 'typeorm';
import { CarCodeConfig } from './entities';
import {
  CarCodeConfigDAO,
  FindCarCodeConfigsRequest,
  UpdateCarCodeConfigDTO,
} from './interfaces';
import { CarCodeConfigHistoryService } from '../car-code-config-history/car-code-config-history.service';
import { distinctBy } from '../common/utils';
import {
  DEFAULT_QUERY_LIMITS,
  DEFAULT_SORT_SETTINGS,
} from '../common/constants';
import { ServiceResponse } from '../common/utils/service-utils';

@Injectable()
export class CarCodeConfigService {
  private readonly logger = new Logger(CarCodeConfigService.name);

  constructor(
    @InjectRepository(CarCodeConfig)
    private carCodeConfigRepository: Repository<CarCodeConfig>,
    private carCodeConfigHistoryService: CarCodeConfigHistoryService,
    private dataSource: DataSource,
  ) {}

  /**
   * Find car code configurations with optional filters
   * @param filters Optional filters for car_code, site_object_id
   * @returns ServiceResponse with matching configurations or error details
   */
  async findCarCodeConfigs(
    filters: FindCarCodeConfigsRequest,
  ): Promise<ServiceResponse<CarCodeConfigDAO>> {
    try {
      const { car_code, site_object_id, sort_order, limit } = filters;

      // Log query filters
      this.logger.log(
        `Finding car code configurations with filters - limit: ${limit}, sort: ${sort_order}`,
      );

      this.logger.log(
        `Additional filters: ${JSON.stringify({ car_code, site_object_id })}`,
      );

      // Build where clause based on filters
      const where: FindOptionsWhere<CarCodeConfig> = {};

      // Apply basic filters
      if (car_code) where.car_code = car_code;
      if (site_object_id) where.site_object_id = site_object_id;

      // Find configurations matching the filters with sort order and limit
      const configs = await this.carCodeConfigRepository.find({
        where,
        order: {
          car_code: sort_order || DEFAULT_SORT_SETTINGS.CONFIG,
          site_object_id: sort_order || DEFAULT_SORT_SETTINGS.CONFIG,
        },
        take: limit || DEFAULT_QUERY_LIMITS.CONFIG,
      });
      this.logger.log(`Found ${configs.length} car code configurations`);
      const mappedConfigs = this.mapCarCodeConfigToDAO(configs);

      return {
        success: true,
        data: mappedConfigs,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error finding car code configurations: ${errorMessage}`,
      );
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Create one or more car code configurations
   * @param carCodeConfigDAOList List of car code configurations to create
   * @returns Object with success status, created configurations, and any errors
   */
  async createCarCodeConfig(
    carCodeConfigDAOList: CarCodeConfigDAO[],
  ): Promise<ServiceResponse<CarCodeConfigDAO>> {
    if (
      carCodeConfigDAOList.length >
      DEFAULT_QUERY_LIMITS.CAR_CODE_ALARM_CONFIG_BATCH
    ) {
      return {
        success: false,
        error: `Batch limit exceeded. Maximum allowed is ${DEFAULT_QUERY_LIMITS.CAR_CODE_ALARM_CONFIG_BATCH}.`,
      };
    }

    try {
      // Check for duplicates in a single DB call
      const uniqueKeys: { car_code: string; site_object_id: string }[] =
        distinctBy(
          carCodeConfigDAOList,
          (item) => `${item.car_code}::${item.site_object_id}`,
        ).map((key) => {
          const [car_code, site_object_id] = key.split('::');
          return { car_code, site_object_id };
        });
      const existingConfigs = await this.carCodeConfigRepository.find({
        where: uniqueKeys,
      });
      if (existingConfigs.length > 0) {
        const errors = existingConfigs.map(
          (cfg) =>
            `Config with car_code=${cfg.car_code} and site_object_id=${cfg.site_object_id} already exists`,
        );
        return {
          success: false,
          error: 'One or more configs already exists',
          errors,
        };
      }

      this.logger.log('Creating new car code configs');
      // Transactional save - Rollback on failures
      return await this.dataSource.transaction(async (manager) => {
        // save CarCodeConfig
        const configEntities = carCodeConfigDAOList.map((configDAO) => {
          // Parse JSON strings to objects for the entity
          const thresholds = configDAO.thresholds ? 
            (typeof configDAO.thresholds === 'string' ? JSON.parse(configDAO.thresholds) : configDAO.thresholds) : 
            null;
          const recommendation = configDAO.recommendation ? 
            (typeof configDAO.recommendation === 'string' ? JSON.parse(configDAO.recommendation) : configDAO.recommendation) : 
            null;

          const entityData = {
            ...configDAO,
            thresholds,
            recommendation,
            updated_at: configDAO.updated_at
              ? new Date(configDAO.updated_at)
              : new Date(),
          };
          return manager.create(CarCodeConfig, entityData as any);
        });
        const savedConfigs = await manager.save(CarCodeConfig, configEntities);

        // save CarCodeConfigHistory
        const historyErrors =
          await this.carCodeConfigHistoryService.createNewHistory({
            oldRecords: {},
            configs: savedConfigs,
            transactionType: 'create',
            entityManager: manager,
          });
        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on create',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Map to DAO objects
        const carCodeConfigDAOs = this.mapCarCodeConfigToDAO(savedConfigs);
        return {
          success: true,
          data: carCodeConfigDAOs,
        };
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Error creating car code configs: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Update a car code configuration
   * @param updates Updates to apply to the configuration, must include car_code and site_object_id
   * @returns ServiceResponse with the updated configuration or error details
   */
  async updateCarCodeConfig(
    updates: UpdateCarCodeConfigDTO,
  ): Promise<ServiceResponse<CarCodeConfigDAO>> {
    try {
      const { car_code, site_object_id } = updates;
      this.logger.log(
        `Updating car code configuration for ${car_code} at site ${site_object_id}`,
      );

      // Find existing config
      const existingConfig = await this.carCodeConfigRepository.findOne({
        where: {
          car_code: car_code,
          site_object_id: site_object_id,
        },
      });

      if (!existingConfig) {
        return {
          success: false,
          error: `Car code configuration not found.`,
          errors: [
            `Car code ${car_code} with site ${site_object_id} not found`,
          ],
        };
      }

      // Store old values for history
      const oldValue = JSON.stringify(existingConfig);
      // Return the result from the transaction
      return await this.dataSource.transaction(async (manager) => {
        // Apply updates
        const parseJsonField = (field: string | Record<string, any> | undefined): Record<string, any> | undefined => {
          if (field === undefined) return undefined;
          if (typeof field === 'string') {
            try {
              return field ? JSON.parse(field) : {};
            } catch (e) {
              this.logger.warn(`Failed to parse JSON field: ${e.message}`);
              return {};
            }
          }
          return field;
        };

        const configToUpdate = {
          ...existingConfig,
          ...(updates.monitoring_window !== undefined && {
            monitoring_window: updates.monitoring_window,
          }),
          ...(updates.enabled !== undefined && { 
            enabled: updates.enabled 
          }),
          ...(updates.thresholds !== undefined && {
            thresholds: parseJsonField(updates.thresholds) || {},
          }),
          ...(updates.recommendation !== undefined && {
            recommendation: parseJsonField(updates.recommendation) || {},
          }),
          // Always update the updated_by and updated_at fields
          updated_at: new Date(),
          updated_by: updates.updated_by || 'system',
        };

        // Save updated config within the transaction
        const updatedConfig = await manager.save(CarCodeConfig, configToUpdate);

        // Create history record for the update
        const historyErrors =
          await this.carCodeConfigHistoryService.createNewHistory({
            oldRecords: { [car_code]: oldValue },
            configs: [updatedConfig],
            transactionType: 'update',
            entityManager: manager,
          });

        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on update',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Map to DAO object and return success response
        const mappedConfig = this.mapCarCodeConfigToDAO([updatedConfig])[0];
        return {
          success: true,
          data: [mappedConfig],
        };
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.logger.error(
        `Error updating car code & site id configuration: ${errorMessage}`,
      );
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Delete a car code configuration
   * @param car_code Car code
   * @param site_object_id Site object ID
   * @param updated_by User or system identifier that is performing the delete operation
   * @returns A service response containing a success message or error details
   */
  async deleteCarCodeConfig(
    car_code: string,
    site_object_id: string,
    updated_by: string,
  ): Promise<ServiceResponse<{ message: string }>> {
    try {
      this.logger.log(
        `Deleting car code configuration for ${car_code} at site ${site_object_id}`,
      );

      // Find existing config
      const existingConfig = await this.carCodeConfigRepository.findOne({
        where: {
          car_code: car_code,
          site_object_id: site_object_id,
        },
      });

      if (!existingConfig) {
        return {
          success: false,
          error: `Car code configuration not found.`,
          errors: [
            `Car code ${car_code} with site ${site_object_id} not found`,
          ],
        };
      }

      // Convert to JSON string for storage
      const oldValue = JSON.stringify(existingConfig);

      // Use transaction to ensure atomicity
      return await this.dataSource.transaction(async (manager) => {
        // Delete config within the transaction
        await manager.remove(CarCodeConfig, existingConfig);

        // Create history record
        const configForHistory = {
          car_code: car_code,
          site_object_id: site_object_id,
          updated_by: updated_by,
        };

        // Create history record and handle any errors
        const historyErrors =
          await this.carCodeConfigHistoryService.createNewHistory({
            oldRecords: { [car_code]: oldValue },
            configs: [configForHistory],
            transactionType: 'delete',
            entityManager: manager,
          });

        // If history creation fails, return error response
        if (historyErrors.length > 0) {
          return {
            success: false,
            error: 'Audit logging failed on delete',
            errors: [`Audit logging failed: ${historyErrors.join('; ')}`],
          };
        }

        // Return success response
        return {
          success: true,
          data: [{ message: `Car code config deleted successfully` }],
        };
      });
    } catch (error: unknown) {
      // handle DB errors
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error deleting car code configuration: ${errorMessage}`,
      );
      // Return standardized error response
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Converts CarCodeConfig entities to DAO objects (CarCodeConfigDAO[])
   * Converts JSON objects to JSON strings for thresholds and recommendation fields
   * Ensures updated_at is always a string (ISO format)
   */
  private mapCarCodeConfigToDAO(configs: CarCodeConfig[]): CarCodeConfigDAO[] {
    return configs.map((config) => {
      // Convert JSON objects to strings for the DAO
      const thresholds = config.thresholds ? 
        (typeof config.thresholds === 'string' ? config.thresholds : JSON.stringify(config.thresholds)) : 
        undefined;
      const recommendation = config.recommendation ? 
        (typeof config.recommendation === 'string' ? config.recommendation : JSON.stringify(config.recommendation)) : 
        undefined;

      const dao: CarCodeConfigDAO = {
        site_object_id: config.site_object_id,
        car_code: config.car_code,
        monitoring_window: config.monitoring_window,
        enabled: config.enabled,
        thresholds,
        recommendation,
        updated_at: config.updated_at
          ? new Date(config.updated_at).toISOString()
          : new Date().toISOString(),
        updated_by: config.updated_by,
      };
      return dao;
    });
  }
}
