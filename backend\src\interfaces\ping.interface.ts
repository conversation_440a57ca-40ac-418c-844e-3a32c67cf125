/**
 * Interfaces for the Ping gRPC method
 */

/**
 * Request for ping method
 * Empty request with no parameters
 */
export interface PingRequest {
  // This is intentionally empty as the ping request doesn't need any parameters
  // Using Record<string, never> to indicate an empty object type
  readonly _dummy?: never;
}

/**
 * Response for ping method
 */
export interface PingResponse {
  message: string;
}
