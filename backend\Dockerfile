# Build stage
FROM node:22-alpine AS build

WORKDIR /usr/src/app

# Copy package files and install dependencies
COPY package*.json ./

# Install dependencies and NestJS CLI
RUN npm ci && npm install -g @nestjs/cli

# Copy the rest of the application
COPY . .

# Ensure dist directory exists and is empty before building
RUN rm -rf dist && mkdir -p dist

# Build the application
RUN npm run build

# Verify the build output and structure
RUN ls -la dist && ls -la dist/src && echo "Build completed successfully with expected directory structure"

# Production stage
FROM node:22-alpine

WORKDIR /usr/src/app

# Set environment variables
ENV NODE_ENV=production
ENV DOCKER_ENVIRONMENT=true
ENV MICROSERVICE_SECRET_KEY=aCHW7ds8xn

# Copy .env.docker file for configuration
COPY .env.docker ./

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy built application from build stage
COPY --from=build /usr/src/app/dist ./dist
COPY --from=build /usr/src/app/node_modules ./node_modules

# Verify the copied files
RUN ls -la dist && ls -la dist/src && echo "Files copied successfully with correct structure"

# Create proto directory in the expected location and copy proto files
RUN mkdir -p /usr/src/app/src/proto
COPY --from=build /usr/src/app/src/proto/*.proto /usr/src/app/src/proto/

# Expose the gRPC port
EXPOSE 8081

# Command to run the application in production mode
CMD ["npm", "run", "start:docker:prod"]
