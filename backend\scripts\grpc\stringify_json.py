#!/usr/bin/env python3
"""
stringify_json.py

This script reads a JSON file, stringifies it into a compact form by removing all unnecessary spaces,
and writes the result to the `output/` directory with the same base filename.

### Usage:
    python compact_json.py input.json

### Output:
    output/input.json

### Dependencies:
    None (uses Python standard library)

"""

import sys
import os
import json

def compact_json(json_obj):
    return json.dumps(json_obj, separators=(',', ':'))

def main():
    if len(sys.argv) != 2:
        print("Usage: python compact_json.py input.json")
        sys.exit(1)

    input_path = sys.argv[1]

    if not os.path.isfile(input_path):
        print(f"Error: File '{input_path}' not found.")
        sys.exit(1)

    # Load JSON data
    with open(input_path, 'r', encoding='utf-8') as infile:
        data = json.load(infile)

    # Compact the JSON
    compacted = compact_json(data)

    # Prepare output path
    os.makedirs("output", exist_ok=True)
    output_filename = os.path.basename(input_path)
    output_path = os.path.join("output", output_filename)

    # Write compact JSON
    with open(output_path, 'w', encoding='utf-8') as outfile:
        outfile.write(compacted)

    print(f"✅ Compact JSON saved to '{output_path}'")

if __name__ == "__main__":
    main()
