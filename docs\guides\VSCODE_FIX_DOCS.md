# VS Code Shell Integration Fix Guide

## Problem Description

When encountering the "Shell Integration Unavailable" error:

```code
Shell Integration Unavailable
Shell integration initialization sequence '\x1b]633;A' was not received within 5s. 
Shell integration has been disabled for this terminal instance.
```

---

## Step-by-Step Solution

### **1. Access Settings JSON**

**To Access Settings JSON:**

- Press Ctrl+Shift+P (Windows/Linux) or Cmd+Shift+P (Mac)
- Type "**Preferences: Open User Settings (JSON)**"
- Select this option

### **2. Configure Settings**

- Add these settings to your `settings.json`:

```json
{
    "terminal.integrated.shellIntegration.enabled": true,
    "terminal.integrated.enablePersistentSessions": true
}
```

### **3. Apply Changes**

- Save settings (Ctrl+S or Cmd+S)
- Close all VS Code terminals
- Open a new terminal (Ctrl+` or Terminal > New Terminal)

### **4. Optional: Shell Configuration**

If issues persist:

- Press Ctrl+Shift+P (Windows/Linux) or Cmd+Shift+P (Mac)
- Type "**Terminal: Select Default Profile**"
- Select supported shell:
  - bash
  - zsh
  - fish
  - PowerShell

### **5. Optional: VS Code Update**

Ensure latest version:

- Press Ctrl+Shift+P or Cmd+Shift+P
- Type "**Check for Updates**"
- Apply any available updates

### **Verification**

Shell integration is working when:

- No "Shell Integration Unavailable" message appears
- Terminal functions normally
- Shell integration features are accessible

> Note: Pick Git Bash for the default shell

Additional Links:

- <https://docs.roocode.com/troubleshooting/shell-integration/>
