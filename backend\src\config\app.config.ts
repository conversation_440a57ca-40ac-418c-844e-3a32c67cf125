// Import dotenv properly
import * as dotenv from 'dotenv';

// Try to load appropriate .env file
try {
  // Check if we're in Docker environment
  if (process.env.DOCKER_ENVIRONMENT) {
    // Use logger instead of console
    // eslint-disable-next-line no-console
    console.log('Loading Docker environment variables from .env.docker');
    dotenv.config({ path: '.env.docker' });
  } else {
    // Default to regular .env file
    // eslint-disable-next-line no-console
    console.log('Loading environment variables from .env');
    dotenv.config();
  }
} catch {
  // dotenv not available, using default values
  // eslint-disable-next-line no-console
  console.log('dotenv not available, using default values');
}

export const appConfig = {
  grpc: {
    host: process.env.GRPC_HOST || '0.0.0.0',
    port: parseInt(process.env.GRPC_PORT || '8081', 10),
  },
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'user',
    password: process.env.DB_PASSWORD || 'root_password',
    database: process.env.DB_NAME || 'postgres',
    schema: process.env.DB_SCHEMA || 'carwrapper',
    ssl: process.env.DB_SSL === 'true',
  },
  external_grpc_services: {
    aclHost: process.env.ACL_HOST || '127.0.0.1:8087',
    alertHost: process.env.ALERT_HOST || '127.0.0.1:8104',
    dataFetchingHost: process.env.DATA_FETCHING_HOST || '127.0.0.1:8089',
    notificationHost: process.env.NOTIFICATION_HOST || '127.0.0.1:8113',
    // Keep the ACL service name for the gRPC client
    aclServiceName: 'ACLRoutes',
    // Secret key for microservice authentication
    // This should be a strong, unique value in production
    microserviceSecretKey: process.env.MICROSERVICE_SECRET_KEY || 'aCHW7ds8xn',
  },
};
