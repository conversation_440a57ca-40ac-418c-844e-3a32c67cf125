sequenceDiagram
    title Car Code Alarm Config Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeAlarmConfigController
    participant CarCodeAlarmConfigService
    participant CarCodeAlarmConfigHistoryService
    participant EntityManager
    participant CarCodeAlarmConfigRepository
    participant CarCodeAlarmConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeAlarmConfigService,CarCodeAlarmConfigHistoryService: CarCodeAlarmConfigService handles CRUD operations while CarCodeAlarmConfigHistoryService maintains audit logs
    Note over CarCodeAlarmConfigHistoryService: Maintains audit trail of all configuration changes with old and new values
    
    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCodeAlarmConfigController: PingCarCodeAlarmConfig()
    CarCodeAlarmConfigController-->>Client: PingResponse

    %% 2. Find Car Code Alarm Configs Flow
    Client->>CarCodeAlarmConfigController: FindCarCodeAlarmConfigs(request)
    Note over CarCodeAlarmConfigController: Validate request parameters
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: findCarCodeAlarmConfigs(request)
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: find(where, order, take)
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG
    Database-->>CarCodeAlarmConfigRepository: Configurations
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Configurations
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO(configs)
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 3. Create Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: CreateCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, name, should_raise_alarm, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: createCarCodeAlarmConfig(requests)
    
    %% Check for duplicates
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: find(where: { car_code: In(uniqueKeys) })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code IN (...)
    Database-->>CarCodeAlarmConfigRepository: Existing Configurations
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configurations
    
    %% If no duplicates, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Save new configs
    loop For each config
        CarCodeAlarmConfigService->>EntityManager: save(CarCodeAlarmConfig)
        EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG
        Database-->>EntityManager: Saved Entity
    end
    
    %% Create history records
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    loop For each config
        CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
        CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
        EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
        Database-->>EntityManager: Saved History
    end
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO(configs)
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 4. Update Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: UpdateCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: updateCarCodeAlarmConfig(request)
    
    %% Find existing config
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: findOne(where: { car_code })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code = ?
    Database-->>CarCodeAlarmConfigRepository: Existing Configuration
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Save updated config
    CarCodeAlarmConfigService->>EntityManager: save(updatedConfig)
    EntityManager->>Database: UPDATE CAR_CODE_ALARM_CONFIG
    Database-->>EntityManager: Updated Entity
    
    %% Create history record
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
    CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO([updatedConfig])
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 5. Delete Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: DeleteCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: deleteCarCodeAlarmConfig(car_code, updated_by)
    
    %% Find existing config
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: findOne(where: { car_code })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code = ?
    Database-->>CarCodeAlarmConfigRepository: Existing Configuration
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Remove config
    CarCodeAlarmConfigService->>EntityManager: remove(CarCodeAlarmConfig, existingConfig)
    EntityManager->>Database: DELETE FROM CAR_CODE_ALARM_CONFIG
    Database-->>EntityManager: Deletion Result
    
    %% Create history record for audit
    Note over CarCodeAlarmConfigHistoryService: Store the deleted config details for audit trail
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
    CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(message: string)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse
