import { Column, Entity, PrimaryColumn } from 'typeorm';
import { DB_TABLES } from '../../common/constants';

@Entity(DB_TABLES.CAR_CODE_ALARM_CONFIG)
export class CarCodeAlarmConfig {
  @PrimaryColumn({ type: 'varchar' })
  car_code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', nullable: true })
  category?: string;

  @Column({ type: 'varchar', nullable: true })
  device_type?: string;

  @Column({ type: 'boolean', nullable: false, default: false })
  should_raise_alarm: boolean;

  @Column({ type: 'timestamp', nullable: true })
  updated_at: Date;

  @Column({ type: 'varchar', nullable: true })
  updated_by?: string;
}
