import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CarCodeAlarmConfigHistory } from './entities';
import { CarCodeAlarmConfigHistoryService } from './car-code-alarm-config-history.service';
import { CarCodeAlarmConfigHistoryController } from './car-code-alarm-config-history.controller';
import { AclGuard } from '../client/acl/acl.guard';
import { AclExceptionFilter } from '../client/acl/acl-exception.filter';
import { AclModule } from '../client/acl/acl.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CarCodeAlarmConfigHistory]),
    AclModule, // Import AclModule to make AclService available
  ],
  providers: [CarCodeAlarmConfigHistoryService, AclGuard, AclExceptionFilter],
  controllers: [CarCodeAlarmConfigHistoryController],
  exports: [TypeOrmModule, CarCodeAlarmConfigHistoryService],
})
export class CarCodeAlarmConfigHistoryModule {}
