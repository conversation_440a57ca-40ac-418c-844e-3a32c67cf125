/**
 * Webpack Hot Module Replacement (HMR) Configuration
 *
 * This file configures webpack for Hot Module Replacement, which allows the application
 * to update modules in the browser during development without a full reload. This
 * significantly speeds up the development process by preserving application state.
 *
 * Key features of this configuration:
 * - Configures webpack for HMR in a Node.js environment
 * - Provides special handling for Docker environments with file polling
 * - Sets up appropriate plugins for automatic restarting and watching
 * - Optimizes file watching based on the environment (local vs Docker)
 */

const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const { RunScriptWebpackPlugin } = require('run-script-webpack-plugin');

module.exports = function (options, webpack) {
  // Determine if we're running in Docker environment
  const isDocker = process.env.DOCKER_ENVIRONMENT === 'true';

  // Configure watch options based on environment
  const watchOptions = {
    // Use more aggressive polling in Docker environment
    poll: isDocker ? 1000 : 500,
    aggregateTimeout: 300,
    ignored: /node_modules/,
  };

  return {
    ...options,
    entry: ['webpack/hot/poll?100', options.entry],
    externals: [
      nodeExternals({
        allowlist: ['webpack/hot/poll?100'],
      }),
    ],
    watchOptions: watchOptions,
    plugins: [
      ...options.plugins,
      new webpack.HotModuleReplacementPlugin(),
      new webpack.WatchIgnorePlugin({
        paths: [/\.js$/, /\.d\.ts$/, /node_modules/],
      }),
      new RunScriptWebpackPlugin({
        name: options.output.filename,
        autoRestart: true,
        nodeArgs: ['--inspect'],
      }),
    ],
  };
};
