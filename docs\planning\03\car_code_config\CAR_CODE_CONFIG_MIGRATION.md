# CAR CODE CONFIG Migration

This document outlines the migration of the car code configuration tables and data for NZOS-CarWrapperService.

## Table Mapping: CAR_CONFIG -> CAR_CODE_CONFIG

| OLD                        | NEW            | Migrating |
|----------------------------|----------------|-----------|
| site_object_id             | site_object_id | yes       |
| car_code                   | car_code       | yes       |
| enabled                    | enabled        | yes       |
| enabled_email_notification | -              | no        |
| thresholds                 | thresholds     | yes       |
| actions                    | -              | no        |
| created_at                 | -              | no        |
| created_by                 | -              | no        |
| updated_at                 | updated_at     | yes       |
| updated_by                 | updated_by     | yes       |

## Table Mapping: CAR_MONITORING_CONFIG -> CAR_CODE_CONFIG

| OLD                      | NEW                      | Migrating |
|--------------------------|--------------------------|-----------|
| monitoring_period_days   | monitoring_period_days   | yes       |

## Migration Script for `CAR_CODE_CONFIG`

```sql
INSERT INTO car_code_config (
    site_object_id,
    car_code,
    monitoring_window,
    enabled,
    thresholds,
    recommendation,
    updated_at,
    updated_by
)
SELECT
    cc.site_object_id,
    cc.car_code,
    COALESCE(cmc.monitoring_period_days, 0) AS monitoring_window,
    cc.enabled,
    cc.thresholds,
    NULL AS recommendation,
    cc.updated_at,
    'migration-script' as updated_by
FROM
    carwrapper.car_config cc
        LEFT OUTER JOIN
    carwrapper.car_monitoring_config cmc ON cc.car_code = cmc.car_code;
```

## Identifying Configurations Without Monitoring Window

This section identifies configurations that lack a configured monitoring window. Currently, there are `9,208` such configurations. The following script can be used for identification:

```sql
SELECT car_code
FROM carwrapper.car_config
WHERE car_code NOT IN (
    SELECT car_code FROM carwrapper.car_monitoring_config
);
```

## Database Table Counts

| Table Name                                 | Total Count |
|--------------------------------------------|-------------|
| `carwrapper.car_config`                    | 67,519      |
| `carwrapper.car_monitoring_config`         | 31          |
| `Configurations without monitoring window` | 9,208       |
