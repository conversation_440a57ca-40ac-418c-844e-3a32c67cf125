/**
 * CarCaseConsoService
 *
 * This service is intentionally separated from the main CarCaseService to:
 * 1. Maintain separation of concerns - consolidation queries have distinct functionality
 * 2. Improve maintainability by keeping service files focused and smaller
 * 3. Reduce cognitive load when working with complex SQL queries
 * 4. Support better testability of the consolidation functionality
 *
 * The service provides optimized queries for consolidated car case views
 * with proper handling of monitoring windows and case grouping.
 */
import { Injectable, Logger } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import {
  DB_TABLES,
  DEFAULT_QUERY_LIMITS,
  DEFAULT_SORT_SETTINGS,
} from '../common/constants';
import {
  normalizeSortOrder,
  safelyHandleOptionalString,
  safelyHandleString,
} from '../common/utils';
import { FindCarCasesByIdsRequest, FindCarCasesRequest } from './interfaces';
import { CarCaseConsoDAO } from './interfaces/car-case-conso.interface';
import { ServiceResponse } from '../common/utils/service-utils';
import { appConfig } from '../config/app.config';

// Type is defined in the interface file

/**
 * Service for handling car case consolidation operations
 */
@Injectable()
export class CarCaseConsoService {
  private readonly logger = new Logger(CarCaseConsoService.name);

  // Define schema and table names as class properties for reuse
  private readonly schemaName = appConfig.database.schema;
  private readonly carCaseTable = `"${this.schemaName}"."${DB_TABLES.CAR_CASES}"`;
  private readonly carCodeConfigTable = `"${this.schemaName}"."${DB_TABLES.CAR_CODE_CONFIG}"`;

  constructor(private entityManager: EntityManager) {}

  /**
   * Find consolidated car cases by their IDs with pagination support.
   *
   * @param request - Object containing:
   *   - ids: Array of car case IDs to retrieve (required)
   *   - user_id: User ID for ACL check (required)
   *   - sort_order: Optional sort order ('ASC' | 'DESC')
   *   - offset: Optional offset for pagination (default: 0)
   *   - limit: Optional maximum number of records to return (default: 1000)
   * @returns Array of consolidated car case data objects or error response
   */
  async findCarCasesByIds(
    request: FindCarCasesByIdsRequest,
  ): Promise<ServiceResponse<CarCaseConsoDAO>> {
    const ids = request.ids || [];
    const sortOrder = request.sort_order || DEFAULT_SORT_SETTINGS.CAR_CASES;
    const limit = request.limit || DEFAULT_QUERY_LIMITS.CAR_CASES;
    const offset = request.offset || 0;

    if (!ids || ids.length === 0) {
      this.logger.warn('No IDs provided to findCarCasesByIds');
      return {
        success: true,
        data: [],
      };
    }

    // Get the sort order from the parameter or use the default from constants
    const querySortOrder = normalizeSortOrder(
      sortOrder as string,
      DEFAULT_SORT_SETTINGS.CAR_CASES,
    );

    this.logger.log(`Finding car cases with ${ids.length} IDs`);

    try {
      // Generate placeholders for the SQL query
      const placeholders = ids.map((_, i) => `$${i + 1}`).join(',');

      // Build a query to find specific car cases by IDs
      const query = `
        WITH case_data AS (
          SELECT
            COALESCE(cc.case_id, cc.id::text) as case_id,
            COALESCE(cc.tags, '') as tags
          FROM
            ${this.carCaseTable} cc
          WHERE
            cc.id IN (${placeholders})
        ),
        latest_group AS (
          SELECT
            case_id,
            tags,
            MAX(logging_date) AS most_recent_occurrence,
            MIN(logging_date) as first_occurrence,
            COUNT(*) as occurrence_count,
            array_agg(id) as related_ids
          FROM
            ${this.carCaseTable}
          WHERE
            (case_id, tags) IN (SELECT case_id, tags FROM case_data)
          GROUP BY
            case_id, tags
        )
        SELECT
          cc.id,
          lg.related_ids as conso_id,
          cc.site_object_id,
          cc.sub_site_object_id,
          cc.device_id,
          cc.additional_device_info,
          cc.tags,
          cc.case_id,
          cc.car_code,
          cc.source,
          cc.title,
          cc.description,
          cc.remarks,
          cc.recommendation,
          cc.metadata,
          cc.logging_date,
          lg.occurrence_count,
          lg.first_occurrence,
          lg.most_recent_occurrence,
          CASE
            WHEN ccc.monitoring_window IS NULL THEN ''
            WHEN NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window THEN 'open'
            ELSE 'close'
          END as status
        FROM
          ${this.carCaseTable} cc
        LEFT JOIN
          ${this.carCodeConfigTable} ccc ON cc.car_code = ccc.car_code
                             AND cc.site_object_id = ccc.site_object_id
        JOIN
          latest_group lg ON COALESCE(cc.case_id, cc.id::text) = lg.case_id
                          AND COALESCE(cc.tags, '') = lg.tags
        WHERE
          cc.id IN (${placeholders})
        ORDER BY
          CASE
            WHEN ccc.monitoring_window IS NULL THEN 2
            WHEN NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window THEN 0
            ELSE 1
          END,
          cc.logging_date ${querySortOrder}
        LIMIT ${limit} OFFSET ${offset || 0}
      `;

      this.logger.debug(`Executing query: ${query}`);
      this.logger.debug(`Query parameters: ${JSON.stringify(ids)}`);

      // Execute the query
      const results: unknown = await this.entityManager.query(query, ids);

      // Ensure results is an array before processing
      if (!Array.isArray(results)) {
        this.logger.warn('Query result is not an array');
        return {
          success: true,
          data: [],
        };
      }

      this.logger.log(`Found ${results.length} car cases by IDs`);

      // Map the results to CarCaseConsoDAO objects
      const mappedResults = this.mapResultsToDTO(results);
      this.logger.log(
        `Mapped ${mappedResults.length} car cases to DTO objects`,
      );

      return {
        success: true,
        data: mappedResults,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error executing findCarCasesByIds query: ${errorMessage}`,
      );
      return {
        success: false,
        error: `Error finding car cases by IDs: ${errorMessage}`,
        data: [],
      };
    }
  }

  /**
   * Find consolidated car cases based on provided filters
   *
   * @param filters - Filter criteria for car cases
   * @returns Array of consolidated car case data objects
   */
  async findCarCases(
    filters: FindCarCasesRequest = {},
  ): Promise<ServiceResponse<CarCaseConsoDAO>> {
    // Ensure filters is an object even if null or undefined is passed
    filters = filters || {};

    try {
      const limit = filters.limit || DEFAULT_QUERY_LIMITS.CAR_CASES;
      const effectiveOffset = filters.offset || 0; // Default to 0 if not provided

      this.logger.log(
        `Finding car cases with filters - limit: ${limit}, offset: ${effectiveOffset}, sort: ${filters.sort_order || DEFAULT_SORT_SETTINGS.CAR_CASES}`,
      );

      // Build query and execute (normalizeSortOrder now inside buildFindCarCasesQuery)
      const { query, params } = this.buildFindCarCasesQuery(filters, limit);
      const results: unknown = await this.entityManager.query(query, params);

      // If results is not an array or is falsy, treat as no data
      if (!Array.isArray(results) || !results) {
        this.logger.warn(
          'findCarCases: DB query returned non-array or empty result, returning success: true, data: []',
        );
        return {
          success: true,
          data: [],
        };
      }

      // Map results to DTO objects
      const mappedResults = this.mapResultsToDTO(results);

      return {
        success: true,
        data: mappedResults,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Error executing findCarCases query: ${errorMessage}`);
      return {
        success: false,
        error: `Error finding car cases: ${errorMessage}`,
        data: [],
      };
    }
  }

  /**
   * Builds the SQL query and parameters for finding car cases
   *
   * @param filters - Filter criteria for car cases
   * @param limit - Maximum number of results to return
   * @returns Object containing the query string and parameters array
   */
  private buildFindCarCasesQuery(
    filters: FindCarCasesRequest,
    limit: number,
  ): { query: string; params: any[] } {
    // Initialize query building
    const baseQuery = this.initializeBaseQuery();

    // Apply filters
    const { query: filteredQuery, params: selectedParams } =
      this.applyFiltersToQuery(baseQuery, filters);

    // Add sorting and limit (normalizeSortOrder now handled inside finalizeSortingAndLimit)
    const finalQuery = this.finalizeSortingAndLimit(
      filteredQuery,
      limit,
      filters,
    );

    return { query: finalQuery, params: selectedParams };
  }

  /**
   * Initializes the base query with the appropriate table references
   *
   * @returns Base SQL query string
   */
  private initializeBaseQuery(): string {
    // Always use the consolidated view with proper status calculation
    return `
      WITH case_data AS (
        SELECT
          COALESCE(cc.case_id, cc.id::text) as case_id,
          COALESCE(cc.tags, '') as tags
        FROM
          ${this.carCaseTable} cc
        WHERE 1=1
      ),
      latest_group AS (
        SELECT
          case_id,
          tags,
          MAX(logging_date) AS most_recent_occurrence,
          MIN(logging_date) as first_occurrence,
          COUNT(*) as occurrence_count,
          array_agg(id) as related_ids
        FROM
        ${this.carCaseTable}
        WHERE
          (case_id, tags) IN (SELECT case_id, tags FROM case_data)
        GROUP BY
          case_id, tags
      )
      SELECT
        cc.id,
        lg.related_ids as conso_id,
        cc.site_object_id,
        cc.sub_site_object_id,
        cc.device_id,
        cc.additional_device_info,
        cc.tags,
        cc.case_id,
        cc.car_code,
        cc.source,
        cc.title,
        cc.description,
        cc.remarks,
        cc.recommendation,
        cc.metadata,
        cc.logging_date,
        lg.occurrence_count,
        lg.first_occurrence,
        lg.most_recent_occurrence,
        CASE
          WHEN ccc.monitoring_window IS NULL THEN ''
          WHEN NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window THEN 'open'
          ELSE 'close'
        END as status
      FROM
        ${this.carCaseTable} cc
      LEFT JOIN
        ${this.carCodeConfigTable} ccc ON cc.car_code = ccc.car_code
                           AND cc.site_object_id = ccc.site_object_id
      JOIN
        latest_group lg ON COALESCE(cc.case_id, cc.id::text) = lg.case_id
                        AND COALESCE(cc.tags, '') = lg.tags
      WHERE 1=1
    `;
  }

  /**
   * Applies all filter conditions to the query
   *
   * @param query - The base SQL query
   * @param filters - The filter criteria
   * @returns Updated query and parameters
   */
  private applyFiltersToQuery(
    query: string,
    filters: FindCarCasesRequest,
  ): { query: string; params: any[] } {
    // Apply standard filters
    const standardFiltersResult = this.applyStandardFilters(query, filters);

    // Apply date filters
    const dateFiltersResult = this.applyDateFilters(
      standardFiltersResult.query,
      standardFiltersResult.params,
      filters,
    );

    let finalQuery = dateFiltersResult.query;

    // Apply status filter if present
    if (filters.status) {
      finalQuery = this.applyStatusFilter(finalQuery, filters.status);
    }

    return { query: finalQuery, params: dateFiltersResult.params };
  }

  /**
   * Applies standard filter conditions (site_object_id, car_code, source)
   */
  private applyStandardFilters(
    query: string,
    filters: FindCarCasesRequest,
  ): { query: string; params: string[] } {
    // We always use the cc. prefix for all table columns to avoid ambiguity
    const params: string[] = [];
    let currentParamCount = 1; // Calculate current parameter count for placeholders

    // Site object ID filter
    if (filters.site_object_id) {
      // Always use the cc table prefix to avoid ambiguity
      query += ` AND cc.site_object_id = $${currentParamCount}`;
      params.push(filters.site_object_id);
      this.logger.debug(
        `Applied site_object_id filter: ${filters.site_object_id}`,
      );
      currentParamCount++;
    }

    // Car code filter
    if (filters.car_code) {
      // Always use the cc table prefix to avoid ambiguity with CAR_CODE_CONFIG table
      query += ` AND LOWER(cc.car_code) = LOWER($${currentParamCount})`;
      params.push(filters.car_code);
      this.logger.debug(`Applied car_code filter: ${filters.car_code}`);
      currentParamCount++;
    }

    // Source filter
    if (filters.source) {
      // Always use the cc table prefix to avoid ambiguity
      query += ` AND cc.source = $${currentParamCount}`;
      params.push(filters.source);
      this.logger.debug(`Applied source filter: ${filters.source}`);
    }

    return { query, params };
  }

  /**
   * Applies date filters (start_date, end_date)
   */
  private applyDateFilters(
    query: string,
    params: any[],
    filters: FindCarCasesRequest,
  ): { query: string; params: any[] } {
    const updatedParams = Array.from(params);

    // Always use the cc. prefix for all table columns to avoid ambiguity
    const dateField = 'cc.logging_date';

    // Calculate current parameter count for placeholders
    const currentParamCount = updatedParams.length + 1;

    // Date range filtering
    if (filters.start_date && filters.end_date) {
      query += ` AND ${dateField} BETWEEN $${currentParamCount} AND $${currentParamCount + 1}`;
      updatedParams.push(filters.start_date, filters.end_date);
    } else if (filters.start_date) {
      query += ` AND ${dateField} >= $${currentParamCount}`;
      updatedParams.push(filters.start_date);
    } else if (filters.end_date) {
      query += ` AND ${dateField} <= $${currentParamCount}`;
      updatedParams.push(filters.end_date);
    }

    return { query, params: updatedParams };
  }

  /**
   * Applies status filter conditions
   *
   * @param query - The current SQL query
   * @param status - The status filter value (open, close, or empty string)
   * @returns Updated query with status conditions added
   */
  private applyStatusFilter(query: string, status: string | undefined): string {
    let updatedQuery = query;

    if (!status) return updatedQuery;

    // Add the status condition based on most_recent_occurrence
    if (status === 'open') {
      updatedQuery += ` AND ccc.monitoring_window IS NOT NULL AND NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window`;
    } else if (status === 'close') {
      updatedQuery += ` AND ccc.monitoring_window IS NOT NULL AND NOW()::DATE - lg.most_recent_occurrence::date >= ccc.monitoring_window`;
    } else if (status === '') {
      updatedQuery += ` AND ccc.monitoring_window IS NULL`;
    }

    return updatedQuery;
  }

  /**
   * Adds sorting and limit clauses to finalize the query
   *
   * @param query - The current SQL query
   * @param limit - Maximum number of results
   * @param filters - Filter criteria for car cases
   * @returns Finalized query with sorting and limit
   */
  private finalizeSortingAndLimit(
    query: string,
    limit: number,
    filters: FindCarCasesRequest,
  ): string {
    // Normalize sort order here
    const sortOrder = normalizeSortOrder(
      filters.sort_order as string,
      DEFAULT_SORT_SETTINGS.CAR_CASES,
    );

    // Add status-based sorting first, then sort by most_recent_occurrence
    const effectiveOffset = filters.offset || 0; // Default to 0 if undefined

    return `${query}
    ORDER BY
      CASE
        WHEN ccc.monitoring_window IS NULL THEN 2
        WHEN NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window THEN 0
        ELSE 1
      END,
      lg.most_recent_occurrence ${sortOrder}
    LIMIT ${limit} OFFSET ${effectiveOffset}`; // Always include OFFSET
  }

  /**
   * Maps the raw query results to DTO objects
   * Consolidates mapping logic for both single and multiple results
   *
   * @param results - The raw query results (can be single object or array)
   * @returns Array of mapped DTO objects
   */
  private mapResultsToDTO(results: unknown): CarCaseConsoDAO[] {
    // Handle both single objects and arrays
    const resultsArray = Array.isArray(results) ? results : [results];

    if (resultsArray.length === 0) {
      return [];
    }

    // Process each result and map to CarCaseConsoDAO
    const mappedResults = resultsArray.map((raw) => {
      return this.mapSingleResultToDTO(raw);
    });

    this.logger.log(`Mapped ${mappedResults.length} car cases to DTO objects`);
    return mappedResults;
  }

  /**
   * Maps a single raw query result to CarCaseConsoDAO
   * Includes validation and proper error handling
   *
   * @param raw - The raw database query result
   * @returns A properly typed CarCaseConsoDAO object
   */
  private mapSingleResultToDTO(raw: unknown): CarCaseConsoDAO {
    // Type guard to ensure raw is an object
    if (!raw || typeof raw !== 'object') {
      throw new Error('Invalid raw data: expected an object');
    }

    // Cast to a record with string keys after basic validation
    const data = raw as Record<string, unknown>;

    // Validate required fields
    this.validateRequiredFields(data);

    // Return the mapped object with appropriate type assertions and fallback logic
    return {
      id: safelyHandleString(data.id),
      conso_id: Array.isArray(data.conso_id)
        ? data.conso_id.map((id) => safelyHandleString(id))
        : [safelyHandleString(data.id)], // Fallback to single ID if conso_id is not an array
      site_object_id: safelyHandleString(data.site_object_id),
      sub_site_object_id: safelyHandleOptionalString(data.sub_site_object_id),
      device_id: safelyHandleOptionalString(data.device_id),
      additional_device_info: data.additional_device_info ?? '',
      tags: safelyHandleOptionalString(data.tags),
      case_id: safelyHandleString(data.case_id),
      car_code: safelyHandleString(data.car_code),
      source: safelyHandleString(data.source),
      title: safelyHandleString(data.title),
      description: safelyHandleOptionalString(data.description),
      remarks: safelyHandleOptionalString(data.remarks),
      recommendation: data.recommendation ?? '',
      metadata: data.metadata ?? '',
      logging_date: safelyHandleString(data.logging_date),
      occurrence_count: data.occurrence_count
        ? Number(data.occurrence_count)
        : 1,
      first_occurrence:
        safelyHandleString(data.first_occurrence) ||
        safelyHandleString(data.logging_date), // Fallback to logging_date
      most_recent_occurrence:
        safelyHandleString(data.most_recent_occurrence) ||
        safelyHandleString(data.logging_date), // Fallback to logging_date
      status: this.validateStatus(data.status),
    };
  }

  /**
   * Validates that all required fields are present in the raw data
   *
   * @param data The raw data object to validate
   * @throws Error if any required field is missing
   */
  private validateRequiredFields(data: Record<string, unknown>): void {
    const requiredFields = [
      'id',
      'site_object_id',
      'case_id',
      'car_code',
      'source',
      'title',
      'logging_date',
      'occurrence_count',
      'first_occurrence',
      'most_recent_occurrence',
      'status',
    ];

    const missingFields = requiredFields.filter(
      (field) => data[field] === undefined,
    );

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Validates that the status value is either 'open' or 'close'
   *
   * @param status The status value to validate
   * @returns The validated status value
   * @throws Error if the status is invalid
   */
  private validateStatus(status: unknown): 'open' | 'close' | '' {
    if (status === 'open' || status === 'close' || status === '') {
      return status;
    }
    throw new Error(
      `Invalid status value: ${String(status)}. Expected 'open', 'close', or empty string.`,
    );
  }
}
