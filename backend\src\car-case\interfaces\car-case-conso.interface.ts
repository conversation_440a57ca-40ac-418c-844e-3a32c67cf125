
/**
 * Data Access Object for consolidated car case views.
 * This interface represents the consolidated view of car cases
 * with additional fields for occurrence counting and status tracking.
 */
export interface CarCaseConsoDAO {
  id: string;
  conso_id: string[]; // Array of related car case IDs
  site_object_id: string;
  sub_site_object_id?: string;
  device_id?: string;
  additional_device_info?: any; // Optional. Omit or pass an empty string if no data. e.g. ""
  tags?: string;
  case_id: string;
  car_code: string;
  source: string;
  title: string;
  description?: string;
  remarks?: string;
  recommendation?: any; // Optional. Omit or pass an empty string if no data. e.g. ""
  metadata?: any; // Optional. Omit or pass an empty string if no data. e.g. ""
  logging_date: string | Date;
  occurrence_count: number;
  first_occurrence: string | Date;
  most_recent_occurrence: string | Date;
  status: 'open' | 'close' | '';
}
