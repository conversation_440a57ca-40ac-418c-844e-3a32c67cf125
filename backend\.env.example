# Environment Identifier
ENV=%%env_var%%
DOCKER_ENVIRONMENT=false

# gRPC Server Configuration
GRPC_HOST=0.0.0.0
GRPC_PORT=8081

# REST Server Configuration (for legacy support)
REST_PORT=8080

# Database Configuration
DB_HOST=postgresdb
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=root_password
DB_NAME=postgres
DB_SCHEMA=CARWRAPPER
DB_SSL=false
DB_SYNCHRONIZE=false

# Logging Configuration
LOGPATH_API=/mnt/carwrapper/logs/car-wrapper-api_%DATE%.log
LOGPATH_GRPC=/mnt/carwrapper/logs/car-wrapper-grpc_%DATE%.log
LOGPATH_DEFAULT=/mnt/carwrapper/logs/car-wrapper-default_%DATE%.log
VOLUME_MOUNT_PATH=/mnt

# External Service Endpoints
ACL_HOST=%%acl_host%%
ALERT_HOST=%%alert_host%%
DATA_FETCHING_HOST=%%data_fetching_host%%
NOTIFICATION_HOST=%%notification_host%%
DATAFETCH_SERVICE_USER_CODE=9aU05w3

# Feature Flags
ALERT_NOTIFICATION_STREAM_ENABLED=true
AUTO_CLOSE_CAR_CASE_ENABLED=false
NORMALIZE_CAR_CASE_STATUS_ENABLED=true
WEEKLY_OPEN_CAR_CASE_REPORT_ENABLED=true

# Microservice Authentication
MICROSERVICE_SECRET_KEY=aCHW7ds8xn

# Node Environment
NODE_ENV=development
