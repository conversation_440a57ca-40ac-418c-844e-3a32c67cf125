import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { appConfig } from './config/app.config';
import { resolveProtoPaths } from './common/utils';

// Define the module type for Hot Module Replacement
interface HotModule {
  hot?: {
    accept: () => void;
    dispose: (callback: () => void) => void;
  };
}

// Cast module as HotModule
declare const module: HotModule;

async function bootstrap() {
  try {
    const logger = new Logger('Bootstrap');

    // Create the gRPC microservice directly
    const { host, port } = appConfig.grpc;
    const url = `${host}:${port}`;

    // Create the NestJS application as a gRPC microservice
    const app = await NestFactory.createMicroservice<MicroserviceOptions>(
      AppModule,
      {
        transport: Transport.GRPC,
        options: {
          package: [
            'car_wrapper_service',
            'external_grpc_services',
            'aclService',
          ],
          protoPath: resolveProtoPaths([
            'car-wrapper-service.proto',
            'external-grpc-services.proto', // Contains endpoints for external services (for testing & not for end users)
            'acl-service.proto',
            // Add more proto files here as needed
          ]),
          url,
          loader: {
            keepCase: true,
            longs: String,
            enums: String,
            defaults: true,
            oneofs: true,
          },
          maxReceiveMessageLength: 1024 * 1024 * 10, // 10MB
          maxSendMessageLength: 1024 * 1024 * 10, // 10MB
        },
      },
    );

    // Start the gRPC microservice
    await app.listen();
    logger.log(`gRPC Microservice is listening on: ${url}`);
    logger.log(
      'Note: Reflection API is not enabled. Use the grpc-tools.sh script to interact with the service.',
    );

    // For hot module replacement
    if (process.env.NODE_ENV !== 'production' && module.hot) {
      module.hot.accept();
      module.hot.dispose(() => {
        const hmrLogger = new Logger('HMR');
        hmrLogger.log('Module disposed. Closing application...');
        void app.close();
      });

      // Log HMR status
      const hmrLogger = new Logger('HMR');
      hmrLogger.log('Hot Module Replacement is enabled');
      if (process.env.CHOKIDAR_USEPOLLING) {
        hmrLogger.log('File watching with polling is enabled');
      }
    }
  } catch (error: unknown) {
    const errorLogger = new Logger('Bootstrap');
    let errorMessage: string;

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = 'Unknown error';
    }

    errorLogger.error(`Failed to start the application: ${errorMessage}`);
    process.exit(1);
  }
}

void bootstrap();
