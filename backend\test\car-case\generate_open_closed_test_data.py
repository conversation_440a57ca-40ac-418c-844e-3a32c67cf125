#!/usr/bin/env python3
"""
Generate test data for open/closed case detection testing.

This script generates car code configurations, car code alarm configurations, and car cases
with specific logging dates to test the open/closed case detection feature.

Usage:
    python generate_open_closed_test_data.py [OPTIONS]

    # Make the script executable first:
    chmod +x generate_open_closed_test_data.py

    # Then run it directly:
    ./generate_open_closed_test_data.py [OPTIONS]

Options:
    --status        Status of cases to generate: 'open', 'close', or 'mixed' (default: mixed)
    --count         Number of car cases to generate (default: 10)
    --output-dir    Output directory (default: output)
    --date-offset   Days to offset from today for the reference date (default: 0)
    --port          gRPC server port (default: 8081)
"""

import argparse
import json
import random
import datetime
import os
import sys
from typing import List, Dict, Any


# Load the shared test data config
def load_test_data_config():
    """Load the shared test data configuration."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shared_dir = os.path.join(os.path.dirname(script_dir), 'shared')
    config_path = os.path.join(shared_dir, 'test_data_config.json')

    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Shared test data config not found at {config_path}")
        print("This file is required for test data generation.")
        print("Please ensure the file exists before running this script.")
        sys.exit(1)


# Global test data config
TEST_DATA_CONFIG = load_test_data_config()

# Probability of omitting recommendation and metadata fields (0.0 to 1.0)
# 0.25 means 25% chance of being omitted
OMIT_FIELD_PROBABILITY = 0.25


def generate_car_code_configs() -> List[Dict[str, Any]]:
    """Generate car code configurations from the shared test data."""
    configs = []

    # Create configurations for each site-car code assignment in the shared config
    for assignment in TEST_DATA_CONFIG['site_car_code_assignments']:
        site_object_id = assignment['site_object_id']
        car_code = assignment['car_code'].lower()  # Ensure car_code is lowercase
        monitoring_period = TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period']

        configs.append({
            "car_code": car_code,
            "site_object_id": site_object_id.lower(),  # Ensure site_object_id is lowercase
            "monitoring_window": monitoring_period,
            "enabled": True,
            "thresholds": {"threshold1": "10", "threshold2": "20"},
            "recommendation": {
                "action": f"Recommended action for {car_code}",
                "priority": random.choice(["low", "medium", "high"])
            },
            "updated_by": "test-script",
        })

    return configs


def generate_car_code_alarm_configs() -> List[Dict[str, Any]]:
    """Generate car code alarm configurations from the shared test data."""
    configs = []

    # Create alarm configurations for each car code in the shared config
    for car_code in TEST_DATA_CONFIG['car_codes'].keys():
        car_code = car_code.lower()  # Ensure car_code is lowercase

        # Generate a name based on the car code
        name = f"{car_code.upper()} Alarm Configuration"

        # Generate a description
        if 'description' in TEST_DATA_CONFIG['car_codes'][car_code]:
            base_description = TEST_DATA_CONFIG['car_codes'][car_code]['description']
            description = f"Alarm configuration for {base_description}"
        else:
            description = f"Alarm configuration for {car_code.upper()}"

        # Generate a category
        categories = [
            "Performance",
            "Safety",
            "Maintenance",
            "Efficiency",
            "Operational",
            "Critical",
            "Warning",
            "Informational"
        ]
        category = random.choice(categories)

        # Generate a device type
        device_types = [
            "Inverter",
            "Panel",
            "Sensor",
            "Controller",
            "Battery",
            "Tracker",
            "Meter",
            "Gateway"
        ]
        device_type = random.choice(device_types)

        # Generate a timestamp for updated_at
        days_ago = random.randint(0, 30)
        updated_at = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

        # Create the car code alarm config object
        config = {
            "car_code": car_code,
            "name": name,
            "description": description,
            "category": category,
            "device_type": device_type,
            "should_raise_alarm": random.choice([True, False]),
            "updated_at": updated_at,
            "updated_by": "test-script"
        }

        configs.append(config)

    return configs


# Function removed - we'll generate cases directly in the generate_car_cases function


def generate_car_cases(
    count: int,
    status: str,
    reference_date: datetime.datetime
) -> List[Dict[str, Any]]:
    """Generate multiple car cases with specific status.

    Args:
        count: Number of car cases to generate
        status: 'open', 'close', or 'mixed'
        reference_date: The reference date to calculate logging_date from

    Returns:
        A list of car case dictionaries
    """
    result = []

    # Get all available site-car code combinations
    assignments = TEST_DATA_CONFIG['site_car_code_assignments']

    # Determine how many cases to generate for each status if mixed
    open_count = count
    close_count = 0

    if status == 'mixed':
        open_count = count // 2
        close_count = count - open_count
    elif status == 'close':
        open_count = 0
        close_count = count

    # Generate cases
    case_index = 0

    # Generate open cases
    for _ in range(open_count):
        # Cycle through assignments
        assignment = assignments[case_index % len(assignments)]
        site_object_id = assignment['site_object_id']
        car_code = assignment['car_code']
        car_code_lower = car_code.lower()

        # Get monitoring period
        monitoring_period = TEST_DATA_CONFIG['car_codes'][car_code_lower]['monitoring_period']

        # Generate sub_site_id and device_id
        sub_site_id = f"sub_{random.randint(1, 10):03d}"
        device_id = f"dev_{random.randint(1, 50):03d}"

        # Tags are no longer needed

        # Calculate logging_date for open cases
        days_ago = random.randint(0, monitoring_period - 1)
        logging_date = (reference_date - datetime.timedelta(days=days_ago)).isoformat()

        # Create the car case
        case = {
            "site_object_id": site_object_id,
            "sub_site_object_id": sub_site_id,
            "device_id": device_id,
            "additional_device_info": json.dumps({
                "device_type": random.choice(["sensor", "controller", "gateway"]),
                "firmware": f"v{random.randint(1,2)}.{random.randint(0,9)}.{random.randint(0,9)}",
                "manufacturer": random.choice(["AcmeCorp", "DeviceMakers", "IoTInc"]),
                "serial_number": f"SN{random.randint(100000,999999)}"
            }),
            "car_code": car_code_lower,
            "source": random.choice(["solar_os", "wind_os", "thermal_os"]),
            "title": f"Case: {car_code_lower} detected at {site_object_id}",
            "description": f"Test case for {car_code_lower} issue on {site_object_id} with monitoring period of {monitoring_period} days. This case is {days_ago} days old.",
            "logging_date": logging_date,
            "remarks": f"Test remarks for {car_code_lower} at {site_object_id}"
        }

        # Generate metadata
        if random.random() >= OMIT_FIELD_PROBABILITY:
            metadata_details = {
                "daily_energy_loss": str(random.randint(10, 500)),
                "monitoring_period": str(TEST_DATA_CONFIG['car_codes'][car_code_lower]['monitoring_period'])
            }

            # Add some additional metadata based on car code
            if car_code_lower in ["amd", "apd"]:
                metadata_details["error_code"] = f"E-{random.randint(100, 999)}"
            elif car_code_lower in ["hsh", "hso"]:
                metadata_details["damage_type"] = random.choice(["crack", "discoloration", "delamination"])
            elif car_code_lower in ["hti", "hym"]:
                metadata_details["severity"] = random.choice(["low", "medium", "high"])
            elif car_code_lower in ["cld", "drs"]:
                metadata_details["affected_area"] = f"{random.randint(1, 100)} sq.m"

            case["metadata"] = json.dumps(metadata_details)

        # Generate recommendation
        if random.random() >= OMIT_FIELD_PROBABILITY:
            action_values = {
                "amd": "Check inverter connections and reset",
                "apd": "Inspect power distribution system",
                "apr": "Review recent performance data",
                "cld": "Clean panels and check for debris",
                "drs": "Diagnose and repair sensor",
                "hdg": "Verify hardware configuration",
                "hsh": "Inspect for physical damage",
                "hso": "Optimize system settings",
                "hti": "Test inverter functionality",
                "hym": "Monitor system for 24 hours"
            }

            action_value = action_values.get(car_code_lower, "Perform site inspection")

            case["recommendation"] = json.dumps({
                "action": action_value
            })

        result.append(case)
        case_index += 1

    # Generate closed cases
    for _ in range(close_count):
        # Cycle through assignments
        assignment = assignments[case_index % len(assignments)]
        site_object_id = assignment['site_object_id']
        car_code = assignment['car_code']
        car_code_lower = car_code.lower()

        # Get monitoring period
        monitoring_period = TEST_DATA_CONFIG['car_codes'][car_code_lower]['monitoring_period']

        # Generate sub_site_id and device_id
        sub_site_id = f"sub_{random.randint(1, 10):03d}"
        device_id = f"dev_{random.randint(1, 50):03d}"

        # Tags are no longer needed

        # Calculate logging_date for closed cases
        days_ago = random.randint(monitoring_period, monitoring_period + 10)
        logging_date = (reference_date - datetime.timedelta(days=days_ago)).isoformat()

        # Create the car case
        case = {
            "site_object_id": site_object_id,
            "sub_site_object_id": sub_site_id,
            "device_id": device_id,
            "additional_device_info": json.dumps({
                "device_type": random.choice(["sensor", "controller", "gateway"]),
                "firmware": f"v{random.randint(1,2)}.{random.randint(0,9)}.{random.randint(0,9)}",
                "manufacturer": random.choice(["AcmeCorp", "DeviceMakers", "IoTInc"]),
                "serial_number": f"SN{random.randint(100000,999999)}"
            }),
            "car_code": car_code_lower,
            "source": random.choice(["solar_os", "wind_os", "thermal_os"]),
            "title": f"Case: {car_code_lower} detected at {site_object_id}",
            "description": f"Test case for {car_code_lower} issue on {site_object_id} with monitoring period of {monitoring_period} days. This case is {days_ago} days old.",
            "logging_date": logging_date,
            "remarks": f"Test remarks for {car_code_lower} at {site_object_id}"
        }

        # Generate metadata
        if random.random() >= OMIT_FIELD_PROBABILITY:
            metadata_details = {
                "daily_energy_loss": str(random.randint(10, 500)),
                "monitoring_period": str(TEST_DATA_CONFIG['car_codes'][car_code_lower]['monitoring_period'])
            }

            # Add some additional metadata based on car code
            if car_code_lower in ["amd", "apd"]:
                metadata_details["error_code"] = f"E-{random.randint(100, 999)}"
            elif car_code_lower in ["hsh", "hso"]:
                metadata_details["damage_type"] = random.choice(["crack", "discoloration", "delamination"])
            elif car_code_lower in ["hti", "hym"]:
                metadata_details["severity"] = random.choice(["low", "medium", "high"])
            elif car_code_lower in ["cld", "drs"]:
                metadata_details["affected_area"] = f"{random.randint(1, 100)} sq.m"

            case["metadata"] = json.dumps(metadata_details)

        # Generate recommendation
        if random.random() >= OMIT_FIELD_PROBABILITY:
            action_values = {
                "amd": "Check inverter connections and reset",
                "apd": "Inspect power distribution system",
                "apr": "Review recent performance data",
                "cld": "Clean panels and check for debris",
                "drs": "Diagnose and repair sensor",
                "hdg": "Verify hardware configuration",
                "hsh": "Inspect for physical damage",
                "hso": "Optimize system settings",
                "hti": "Test inverter functionality",
                "hym": "Monitor system for 24 hours"
            }

            action_value = action_values.get(car_code_lower, "Perform site inspection")

            case["recommendation"] = json.dumps({
                "action": action_value
            })

        result.append(case)
        case_index += 1

    return result


def save_car_code_configs(configs: List[Dict[str, Any]], output_file: str, port: int) -> None:
    """Save car code configurations to a JSON file."""
    with open(output_file, 'w') as f:
        json.dump(configs, f, indent=2)
    print(f"Generated {len(configs)} car code configurations: {output_file}")


def save_car_code_alarm_configs(configs: List[Dict[str, Any]], output_file: str, port: int) -> None:
    """Save car code alarm configurations to a JSON file."""
    with open(output_file, 'w') as f:
        json.dump(configs, f, indent=2)
    print(f"Generated {len(configs)} car code alarm configurations: {output_file}")


def save_car_cases(car_cases: List[Dict[str, Any]], output_file: str, port: int) -> None:
    """Save car cases to a JSON file."""
    with open(output_file, 'w') as f:
        json.dump(car_cases, f, indent=2)
    print(f"Generated {len(car_cases)} car cases: {output_file}")


# Removed save_find_car_cases_command as gRPC output is no longer needed.


def main():
    # Change to the script directory (like the shell script does)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"Working directory: {os.getcwd()}")

    parser = argparse.ArgumentParser(description='Generate test data for open/closed case detection testing')
    parser.add_argument('--status', type=str, choices=['open', 'close', 'mixed'], default='mixed',
                        help='Status of cases to generate: open, close, or mixed (default: mixed)')
    parser.add_argument('--count', type=int, default=10,
                        help='Number of car cases to generate (default: 10)')
    parser.add_argument('--output-dir', type=str, default='output',
                        help='Output directory (default: output)')
    parser.add_argument('--date-offset', type=int, default=0,
                        help='Days to offset from today for the reference date (default: 0)')
    parser.add_argument('--port', type=int, default=8081,
                        help='gRPC server port (default: 8081)')

    args = parser.parse_args()

    # Create output directory with timestamp and status
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output_dir, f"{timestamp}_{args.status}")
    os.makedirs(output_dir, exist_ok=True)

    print(f"Output directory: {output_dir}")

    # Calculate reference date
    reference_date = datetime.datetime.now() - datetime.timedelta(days=args.date_offset)
    print(f"Reference date: {reference_date.strftime('%Y-%m-%d')}")

    # Generate car code configurations
    configs = generate_car_code_configs()
    config_file = os.path.join(output_dir, "car_code_configs.json")
    save_car_code_configs(configs, config_file, args.port)

    # Generate car code alarm configurations
    alarm_configs = generate_car_code_alarm_configs()
    alarm_config_file = os.path.join(output_dir, "car_code_alarm_configs.json")
    save_car_code_alarm_configs(alarm_configs, alarm_config_file, args.port)

    # Generate car cases
    car_cases = generate_car_cases(args.count, args.status, reference_date)
    cases_file = os.path.join(output_dir, "car_cases.json")
    save_car_cases(car_cases, cases_file, args.port)

    # Print summary
    print("\nDone! Test data files have been created in", output_dir)
    print("")
    print("Files generated:")
    print(f"- {config_file}: Car code configurations")
    print(f"- {alarm_config_file}: Car code alarm configurations")
    print(f"- {cases_file}: Car cases with {args.status} status")


if __name__ == "__main__":
    main()
