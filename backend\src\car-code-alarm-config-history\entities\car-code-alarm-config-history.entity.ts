import { Entity, Column, PrimaryColumn } from 'typeorm';
import { DB_TABLES } from '../../common/constants';

/**
 * Entity representing the history of changes to car code alarm configurations
 * Tracks all create, update, and delete operations
 */
@Entity(DB_TABLES.CAR_CODE_ALARM_CONFIG_HISTORY)
export class CarCodeAlarmConfigHistory {
  @PrimaryColumn({ type: 'varchar' })
  car_code: string;

  @Column({ type: 'text', nullable: false })
  old_value: string;

  @Column({ type: 'text', nullable: false })
  new_value: string;

  @Column({
    type: 'varchar',
    nullable: false,
    enum: ['create', 'update', 'delete'],
  })
  transaction_type: 'create' | 'update' | 'delete';

  @Column({ type: 'timestamp', nullable: false })
  updated_at: Date;

  @Column({ type: 'varchar', nullable: false })
  updated_by: string;
}
