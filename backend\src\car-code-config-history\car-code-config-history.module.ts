import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CarCodeConfigHistory } from './entities';
import { CarCodeConfigHistoryService } from './car-code-config-history.service';
import { CarCodeConfigHistoryController } from './car-code-config-history.controller';
import { AclGuard } from '../client/acl/acl.guard';
import { AclExceptionFilter } from '../client/acl/acl-exception.filter';
import { AclModule } from '../client/acl/acl.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CarCodeConfigHistory]),
    AclModule, // Import AclModule to make AclService available
  ],
  providers: [CarCodeConfigHistoryService, AclGuard, AclExceptionFilter],
  controllers: [CarCodeConfigHistoryController],
  exports: [TypeOrmModule, CarCodeConfigHistoryService],
})
export class CarCodeConfigHistoryModule {}
