/**
 * Unit tests for the CarCodeAlarmConfigService
 *
 * These tests verify the business logic of the CarCodeAlarmConfigService in isolation
 * by mocking the database repositories.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { CarCodeAlarmConfigService } from './car-code-alarm-config.service';
import { CarCodeAlarmConfig } from './entities';
import { CarCodeAlarmConfigHistoryService } from '../car-code-alarm-config-history/car-code-alarm-config-history.service';
import {
  CarCodeAlarmConfigDAO,
  UpdateCarCodeAlarmConfigDTO,
} from './interfaces';
import { DEFAULT_SORT_SETTINGS } from '../common/constants';

describe('CarCodeAlarmConfigService', () => {
  let service: CarCodeAlarmConfigService;
  let carCodeAlarmConfigRepository: Repository<CarCodeAlarmConfig>;
  let carCodeAlarmConfigHistoryService: CarCodeAlarmConfigHistoryService;
  let dataSource: DataSource;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CarCodeAlarmConfigService,
        {
          provide: getRepositoryToken(CarCodeAlarmConfig),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            remove: jest.fn(),
          },
        },
        {
          provide: CarCodeAlarmConfigHistoryService,
          useValue: {
            createNewHistory: jest.fn().mockResolvedValue([]),
          },
        },
        {
          provide: DataSource,
          useValue: {
            transaction: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CarCodeAlarmConfigService>(CarCodeAlarmConfigService);
    carCodeAlarmConfigRepository = module.get<Repository<CarCodeAlarmConfig>>(
      getRepositoryToken(CarCodeAlarmConfig),
    );
    carCodeAlarmConfigHistoryService =
      module.get<CarCodeAlarmConfigHistoryService>(
        CarCodeAlarmConfigHistoryService,
      );
    dataSource = module.get<DataSource>(DataSource);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * Test Data
   *
   * These mock objects are used throughout the tests to simulate
   * database entities and request objects.
   */
  function getMockCarCodeAlarmConfig(): CarCodeAlarmConfig {
    return {
      car_code: 'TEST-001',
      name: 'Test Alarm Config',
      description: 'Test description',
      category: 'Test Category',
      device_type: 'Test Device',
      should_raise_alarm: true,
      updated_at: new Date(),
      updated_by: 'test-user',
    };
  }

  const mockCarCodeAlarmConfigDAO: CarCodeAlarmConfigDAO = {
    car_code: 'TEST-001',
    name: 'Test Alarm Config',
    description: 'Test description',
    category: 'Test Category',
    device_type: 'Test Device',
    should_raise_alarm: true,
    updated_at: new Date().toISOString(),
    updated_by: 'test-user',
  };
  getMockCarCodeAlarmConfig();
  // Helper for result verification (type-safe, avoids 'any')
  interface ServiceResult {
    success?: boolean;
    data?: { car_code?: string }[];
    error?: string;
  }

  // Helper for repeated find-by-car-code success check
  async function expectFindByCarCodeSuccess(
    service: CarCodeAlarmConfigService,
    repo: Repository<CarCodeAlarmConfig>,
    car_code = 'TEST-001',
  ): Promise<ServiceResult> {
    jest.spyOn(repo, 'find').mockResolvedValue([getMockCarCodeAlarmConfig()]);
    const result = await service.findCarCodeAlarmConfigs({ car_code });
    expectSingleResult(result, car_code);
    return result;
  }

  // Helper for result verification
  function expectSingleResult(result: ServiceResult, car_code = 'TEST-001') {
    expect(typeof result).toBe('object');
    expect(result.success).toBe(true);
    expect(Array.isArray(result.data)).toBe(true);
    expect(result.data && result.data.length).toBe(1);
    expect(result.data && result.data[0]?.car_code).toBe(car_code);
  }

  // Helper for mocking transaction
  function mockTransaction(
    dataSource: DataSource,
    entityManagerOverrides: Partial<any> = {},
  ) {
    jest
      .spyOn(dataSource, 'transaction')
      .mockImplementation(async (...args: unknown[]) => {
        const callback = args[args.length - 1] as (
          manager: any,
        ) => Promise<unknown>;

        // Create a complete mock config with all required fields
        const mockConfig = {
          car_code: 'TEST-001',
          name: 'Test Alarm Config',
          description: 'Test description',
          category: 'Test Category',
          device_type: 'Test Device',
          should_raise_alarm: true,
          updated_at: new Date(), // Always provide a valid Date object
          updated_by: 'test-user',
          ...entityManagerOverrides, // Allow overriding any fields
        };

        const mockEntityManager = {
          save: jest.fn().mockResolvedValue([mockConfig]),
          remove: jest.fn().mockResolvedValue(mockConfig),
          ...entityManagerOverrides,
        };
        return await callback(mockEntityManager);
      });
  }

  describe('findCarCodeAlarmConfigs', () => {
    it('should find car code alarm configurations with filters', async () => {
      await expectFindByCarCodeSuccess(service, carCodeAlarmConfigRepository);
    });

    it('should apply specified sort order when provided', async () => {
      // Mock repository methods
      jest
        .spyOn(carCodeAlarmConfigRepository, 'find')
        .mockResolvedValue([getMockCarCodeAlarmConfig()]);

      // Call the service method with sort order
      const result = await service.findCarCodeAlarmConfigs({
        sort_order: 'ASC',
      });

      // Verify the result
      expect(result.success).toBe(true);

      // Use a spy to avoid unbound method warning
      const findSpy = jest.spyOn(carCodeAlarmConfigRepository, 'find');

      // Verify sort order was applied correctly
      expect(findSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          order: {
            car_code: 'ASC',
          },
        }),
      );
    });

    it('should apply DESC sort order when provided', async () => {
      // Mock repository methods
      jest
        .spyOn(carCodeAlarmConfigRepository, 'find')
        .mockResolvedValue([getMockCarCodeAlarmConfig()]);

      // Call the service method with DESC sort order
      const result = await service.findCarCodeAlarmConfigs({
        sort_order: 'DESC',
      });

      // Verify the result
      expect(result.success).toBe(true);

      // Use a spy to avoid unbound method warning
      const findSpy = jest.spyOn(carCodeAlarmConfigRepository, 'find');

      // Verify sort order was applied correctly to car_code field
      expect(findSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          order: {
            car_code: 'DESC',
          },
        }),
      );
    });

    it('should use default sort order when not specified', async () => {
      // Mock repository methods
      jest
        .spyOn(carCodeAlarmConfigRepository, 'find')
        .mockResolvedValue([getMockCarCodeAlarmConfig()]);

      // Call the service method without specifying sort order
      const result = await service.findCarCodeAlarmConfigs({});

      // Verify the result
      expect(result.success).toBe(true);

      // Use a spy to avoid unbound method warning
      const findSpy = jest.spyOn(carCodeAlarmConfigRepository, 'find');

      // Verify default sort order was applied correctly to car_code field
      expect(findSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          order: {
            car_code: DEFAULT_SORT_SETTINGS.CONFIG,
          },
        }),
      );
    });
  });

  describe('findCarCodeAlarmConfigs with specific car_code', () => {
    it('should find a car code alarm configuration by car_code', async () => {
      await expectFindByCarCodeSuccess(service, carCodeAlarmConfigRepository);
    });

    it('should return empty array when car code alarm configuration is not found', async () => {
      // Mock repository methods to return empty array (not found)
      jest.spyOn(carCodeAlarmConfigRepository, 'find').mockResolvedValue([]);

      // Call the service method
      const result = await service.findCarCodeAlarmConfigs({
        car_code: 'NONEXISTENT',
      });

      // Verify the success response with empty data array
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.length).toBe(0);
    });
  });

  describe('createCarCodeAlarmConfig', () => {
    it('should create car code alarm configurations successfully', async () => {
      jest.spyOn(carCodeAlarmConfigRepository, 'find').mockResolvedValue([]);
      mockTransaction(dataSource);
      const result = await service.createCarCodeAlarmConfig([
        mockCarCodeAlarmConfigDAO,
      ]);
      expectSingleResult(result);
    });

    it('should handle audit logging failures during create', async () => {
      jest.spyOn(carCodeAlarmConfigRepository, 'find').mockResolvedValue([]);
      jest
        .spyOn(carCodeAlarmConfigHistoryService, 'createNewHistory')
        .mockResolvedValue(['Error creating history']);
      mockTransaction(dataSource);
      const result = await service.createCarCodeAlarmConfig([
        mockCarCodeAlarmConfigDAO,
      ]);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Audit logging failed');
    });

    it('should handle general errors during create', async () => {
      jest.spyOn(carCodeAlarmConfigRepository, 'find').mockResolvedValue([]);
      jest
        .spyOn(dataSource, 'transaction')
        .mockRejectedValue(new Error('Database error'));
      const result = await service.createCarCodeAlarmConfig([
        mockCarCodeAlarmConfigDAO,
      ]);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Database error');
    });
  });

  describe('updateCarCodeAlarmConfig', () => {
    it('should update a car code alarm configuration successfully', async () => {
      // Mock the findOne method to return a valid config
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(getMockCarCodeAlarmConfig());

      // No need to spy on mapCarCodeAlarmConfigsToDAO anymore

      // Mock the transaction
      mockTransaction(dataSource);

      // Create the update DTO
      const updateDTO: UpdateCarCodeAlarmConfigDTO = {
        car_code: 'TEST-001',
        name: 'Updated Name',
        updated_by: 'test-user',
      };

      // Create a spy on the mapCarCodeAlarmConfigsToDAO method
      const mapSpy = jest.spyOn(service as any, 'mapCarCodeAlarmConfigsToDAO');

      // Call the service method
      const result = await service.updateCarCodeAlarmConfig(updateDTO);

      // Log the result and the arguments passed to mapCarCodeAlarmConfigsToDAO for debugging
      console.info('Update test result:', JSON.stringify(result));
      console.info(
        'Map method called with:',
        mapSpy.mock.calls.length > 0
          ? JSON.stringify(mapSpy.mock.calls[0][0])
          : 'not called',
      );

      // Verify the result
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data && result.data.length).toBe(1);
      expect(result.data && result.data[0]?.car_code).toBe('TEST-001');
    });

    it('should return error when car code alarm configuration is not found', async () => {
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(null);
      const updateDTO: UpdateCarCodeAlarmConfigDTO = {
        car_code: 'NONEXISTENT',
        name: 'Updated Name',
        updated_by: 'test-user',
      };
      const result = await service.updateCarCodeAlarmConfig(updateDTO);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('not found');
    });

    it('should handle audit logging failures during update', async () => {
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(getMockCarCodeAlarmConfig());
      jest
        .spyOn(carCodeAlarmConfigHistoryService, 'createNewHistory')
        .mockResolvedValue(['Error creating history']);
      mockTransaction(dataSource);
      const updateDTO: UpdateCarCodeAlarmConfigDTO = {
        car_code: 'TEST-001',
        name: 'Updated Name',
        updated_by: 'test-user',
      };
      const result = await service.updateCarCodeAlarmConfig(updateDTO);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Audit logging failed');
    });
  });

  describe('deleteCarCodeAlarmConfig', () => {
    it('should delete a car code alarm configuration successfully', async () => {
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(getMockCarCodeAlarmConfig());
      mockTransaction(dataSource);
      const result = await service.deleteCarCodeAlarmConfig(
        'TEST-001',
        'test-user',
      );
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.[0].message).toBe(
        'Car code alarm config deleted successfully',
      );
    });

    it('should return error when car code alarm configuration is not found', async () => {
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(null);
      const result = await service.deleteCarCodeAlarmConfig(
        'NONEXISTENT',
        'test-user',
      );
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('not found');
    });

    it('should handle audit logging failures during delete', async () => {
      jest
        .spyOn(carCodeAlarmConfigRepository, 'findOne')
        .mockResolvedValue(getMockCarCodeAlarmConfig());
      jest
        .spyOn(carCodeAlarmConfigHistoryService, 'createNewHistory')
        .mockResolvedValue(['Error creating history']);
      mockTransaction(dataSource);
      const result = await service.deleteCarCodeAlarmConfig(
        'TEST-001',
        'test-user',
      );
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Audit logging failed');
    });
  });
});
