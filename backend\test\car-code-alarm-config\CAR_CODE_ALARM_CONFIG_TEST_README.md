# Car Code Alarm Config Test Data

This directory provides scripts to quickly generate sample data for car code alarm config testing. All scripts use shared config from `../shared/test_data_config.json` for consistency.

## Scripts

- **Car Code Alarm Config Generator** (`generate_car_code_alarm_config_test_data.py`)
  - Outputs a plain JSON array of car code alarm configs.

## How to Use

```bash
python generate_car_code_alarm_config_test_data.py           # 10 configs (default)
python generate_car_code_alarm_config_test_data.py --count=20 # 20 configs
```

- Output: `output/<timestamp>/car_code_alarm_configs.json`

## Output Example

```bash
output/
  ├── 20250429_174248/
  │   └── car_code_alarm_configs.json
  └── ...
```

- Each run creates a new timestamped folder for easy organization and history.

## Prerequisites

- Make sure `../shared/test_data_config.json` exists.

## Shared Test Data Config

- All scripts use `../shared/test_data_config.json` for car codes, site IDs, and assignments.
- To change test data structure, update this file (do not hardcode values elsewhere).

---
**Tip:** Delete old folders in `output/` to keep your workspace clean.

- **description** (optional): A detailed description of the alarm
- **category** (optional): The category of the alarm (e.g., "Performance", "Safety")
- **device_type** (optional): The type of device this alarm applies to
- **should_raise_alarm**: Boolean indicating whether this configuration should trigger an alarm
- **updated_at**: Timestamp of the last update
- **updated_by**: User who last updated the configuration

## Integration with Other Test Data

The car code alarm configurations can be used in conjunction with car code configurations and car cases for comprehensive testing:

```bash
# First, generate and insert car code alarm configurations
cd backend/test/car-code-alarm-config
python generate_car_code_alarm_config_test_data.py

# Then, seed car code configurations
cd ../car-code-config
node seed_car_code_config.js

# Finally, generate and insert car case test data
cd ../car-case
python generate_car_case_test_data.py
```

This allows you to test the complete workflow from alarm configurations to car cases.
