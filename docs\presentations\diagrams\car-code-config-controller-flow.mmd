sequenceDiagram
    title Car Code Config Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeConfigController
    participant CarCodeConfigService
    participant CarCodeConfigHistoryService
    participant EntityManager
    participant CarCodeConfigRepository
    participant CarCodeConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeConfigService,CarCodeConfigHistoryService: CarCodeConfigService handles CRUD operations while CarCodeConfigHistoryService maintains audit logs
    Note over CarCodeConfigHistoryService: Maintains audit trail of all configuration changes
    
    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCodeConfigController: PingCarCodeConfig()
    CarCodeConfigController-->>Client: PingResponse

    %% 2. Find Car Code Configs Flow
    Client->>CarCodeConfigController: FindCarCodeConfigs(request)
    Note over CarCodeConfigController: Validate request parameters
    CarCodeConfigController->>CarCodeConfigService: findCarCodeConfigs(request)
    CarCodeConfigService->>CarCodeConfigRepository: find(where, order, take)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG
    Database-->>CarCodeConfigRepository: Configurations
    CarCodeConfigRepository-->>CarCodeConfigService: Configurations
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO(configs)
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 3. Create Car Code Config Flow
    Client->>CarCodeConfigController: CreateCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: createCarCodeConfig(requests)
    
    %% Check for duplicates
    CarCodeConfigService->>CarCodeConfigRepository: find(uniqueKeys)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE (car_code, site_object_id) IN (...)
    Database-->>CarCodeConfigRepository: Existing Configurations
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configurations
    
    %% If no duplicates, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Save new configs
    loop For each config
        CarCodeConfigService->>EntityManager: save(CarCodeConfig)
        EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG
        Database-->>EntityManager: Saved Entity
    end
    
    %% Create history records
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    loop For each config
        CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
        CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
        EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
        Database-->>EntityManager: Saved History
    end
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO(configs)
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 4. Update Car Code Config Flow
    Client->>CarCodeConfigController: UpdateCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: updateCarCodeConfig(request)
    
    %% Find existing config
    CarCodeConfigService->>CarCodeConfigRepository: findOne(where)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE car_code = ? AND site_object_id = ?
    Database-->>CarCodeConfigRepository: Existing Configuration
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Save updated config
    CarCodeConfigService->>EntityManager: save(updatedConfig)
    EntityManager->>Database: UPDATE CAR_CODE_CONFIG
    Database-->>EntityManager: Updated Entity
    
    %% Create history record
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
    CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO([updatedConfig])
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 5. Delete Car Code Config Flow
    Client->>CarCodeConfigController: DeleteCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: deleteCarCodeConfig(car_code, site_object_id, updated_by)
    
    %% Find existing config
    CarCodeConfigService->>CarCodeConfigRepository: findOne(where)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE car_code = ? AND site_object_id = ?
    Database-->>CarCodeConfigRepository: Existing Configuration
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Remove config
    CarCodeConfigService->>EntityManager: remove(CarCodeConfig, existingConfig)
    EntityManager->>Database: DELETE FROM CAR_CODE_CONFIG
    Database-->>EntityManager: Deletion Result
    
    %% Create history record for audit
    Note over CarCodeConfigHistoryService: Store the deleted config details for audit trail
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
    CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(message: string)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse
