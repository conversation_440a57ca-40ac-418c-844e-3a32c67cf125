# Development
.git
.github
.vscode
.idea

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules

# Build output
dist
build
coverage
.nyc_output

# Environment and config
.env*
!.env.docker
.dockerignore

# Docker files (not needed inside container)
Dockerfile
local.Dockerfile
docker-compose*.yml

# Documentation and other files not needed in container
README.md
LICENSE
docs
swagger.yaml

# Test files
test
*.spec.ts
*.test.ts
jest.config.js

# Misc
.DS_Store
Thumbs.db