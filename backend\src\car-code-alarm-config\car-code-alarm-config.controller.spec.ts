import { Test, TestingModule } from '@nestjs/testing';
import { CarCodeAlarmConfigController } from './car-code-alarm-config.controller';
import { CarCodeAlarmConfigService } from './car-code-alarm-config.service';
import {
  CarCodeAlarmConfigDAO,
  CreateCarCodeAlarmConfigsRequest,
  FindCarCodeAlarmConfigsRequest,
  UpdateCarCodeAlarmConfigRequest,
  DeleteCarCodeAlarmConfigRequest,
} from './interfaces';
import { AclGuard } from '../client/acl/acl.guard';

describe('CarCodeAlarmConfigController', () => {
  let controller: CarCodeAlarmConfigController;
  let service: CarCodeAlarmConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CarCodeAlarmConfigController],
      providers: [
        {
          provide: CarCodeAlarmConfigService,
          useValue: {
            createCarCodeAlarmConfig: jest.fn(),
            findCarCodeAlarmConfigs: jest.fn(),
            updateCarCodeAlarmConfig: jest.fn(),
            deleteCarCodeAlarmConfig: jest.fn(),
          },
        },
        {
          provide: AclGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
      ],
    })
      .overrideGuard(AclGuard)
      .useValue({ canActivate: jest.fn().mockReturnValue(true) })
      .compile();

    controller = module.get<CarCodeAlarmConfigController>(
      CarCodeAlarmConfigController,
    );
    service = module.get<CarCodeAlarmConfigService>(CarCodeAlarmConfigService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const mockCarCodeAlarmConfigDAO: CarCodeAlarmConfigDAO = {
    car_code: 'ALARM-001',
    name: 'Test Alarm',
    description: 'Test alarm description',
    category: 'Safety',
    device_type: 'Sensor',
    should_raise_alarm: true,
    updated_at: new Date().toISOString(),
    updated_by: 'test-user',
  };

  const mockServiceSuccessResponse: {
    success: boolean;
    data: CarCodeAlarmConfigDAO[];
  } = {
    success: true,
    data: [mockCarCodeAlarmConfigDAO],
  };

  const mockControllerSuccessResponse: {
    success: {
      status: string;
      code: number;
      message: CarCodeAlarmConfigDAO[];
    };
  } = {
    success: {
      status: 'success',
      code: 200,
      message: [mockCarCodeAlarmConfigDAO],
    },
  };

  const mockControllerCreatedResponse: {
    success: {
      status: string;
      code: number;
      message: CarCodeAlarmConfigDAO[];
    };
  } = {
    success: {
      status: 'success',
      code: 201,
      message: [mockCarCodeAlarmConfigDAO],
    },
  };

  describe('pingCarCodeAlarmConfig', () => {
    it('should return a ping response', () => {
      const result = controller.pingCarCodeAlarmConfig();
      expect(result).toEqual({
        message: 'Car Code Alarm Config Service is running',
      });
    });
  });

  describe('createCarCodeAlarmConfig', () => {
    it('should call service.createCarCodeAlarmConfig with the request data (1 entry)', async () => {
      jest
        .spyOn(service, 'createCarCodeAlarmConfig')
        .mockResolvedValue(mockServiceSuccessResponse);
      const request: CreateCarCodeAlarmConfigsRequest = {
        requests: [mockCarCodeAlarmConfigDAO],
        user_id: 'test-user',
      };
      const result = await controller.createCarCodeAlarmConfig(request);
      expect(result).toEqual(mockControllerCreatedResponse);
      const createSpy = jest.spyOn(service, 'createCarCodeAlarmConfig');
      expect(createSpy).toHaveBeenCalledWith(request.requests);
    });

    it('should call service.createCarCodeAlarmConfig with the request data (multiple entries)', async () => {
      const mockCarCodeAlarmConfigDAO2: CarCodeAlarmConfigDAO = {
        car_code: 'ALARM-002',
        name: 'Test Alarm 2',
        should_raise_alarm: false,
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };
      const mockCarCodeAlarmConfigDAO3: CarCodeAlarmConfigDAO = {
        car_code: 'ALARM-003',
        name: 'Test Alarm 3',
        should_raise_alarm: true,
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };
      const mockMultipleServiceResponse: {
        success: boolean;
        data: CarCodeAlarmConfigDAO[];
      } = {
        success: true,
        data: [
          mockCarCodeAlarmConfigDAO,
          mockCarCodeAlarmConfigDAO2,
          mockCarCodeAlarmConfigDAO3,
        ],
      };
      jest
        .spyOn(service, 'createCarCodeAlarmConfig')
        .mockResolvedValue(mockMultipleServiceResponse);
      const request: CreateCarCodeAlarmConfigsRequest = {
        requests: [
          mockCarCodeAlarmConfigDAO,
          mockCarCodeAlarmConfigDAO2,
          mockCarCodeAlarmConfigDAO3,
        ],
        user_id: 'test-user',
      };
      const result = await controller.createCarCodeAlarmConfig(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 201,
          message: [
            mockCarCodeAlarmConfigDAO,
            mockCarCodeAlarmConfigDAO2,
            mockCarCodeAlarmConfigDAO3,
          ],
        },
      });
      const createSpy = jest.spyOn(service, 'createCarCodeAlarmConfig');
      expect(createSpy).toHaveBeenCalledWith([
        mockCarCodeAlarmConfigDAO,
        mockCarCodeAlarmConfigDAO2,
        mockCarCodeAlarmConfigDAO3,
      ]);
    });

    it('should format service errors correctly in createCarCodeAlarmConfig', async () => {
      jest.spyOn(service, 'createCarCodeAlarmConfig').mockResolvedValue({
        success: false,
        error: 'Database constraint violation',
        errors: ['Duplicate car_code'],
      });
      const request: CreateCarCodeAlarmConfigsRequest = {
        requests: [mockCarCodeAlarmConfigDAO],
        user_id: 'test-user',
      };
      const result = await controller.createCarCodeAlarmConfig(request);
      expect(result).toHaveProperty('error');
      expect(Array.isArray(result.error?.message)).toBe(true);
      expect(result.error?.error).toBe('Database constraint violation');
    });
  });

  describe('findCarCodeAlarmConfigs', () => {
    it('should call service.findCarCodeAlarmConfigs with the request data', async () => {
      jest.spyOn(service, 'findCarCodeAlarmConfigs').mockResolvedValue({
        success: true,
        data: [mockCarCodeAlarmConfigDAO],
      });
      const request: FindCarCodeAlarmConfigsRequest = {
        car_code: 'ALARM-001',
        user_id: 'test-user',
      };
      const result = await controller.findCarCodeAlarmConfigs(request);
      expect(result).toEqual(mockControllerSuccessResponse);
      const findSpy = jest.spyOn(service, 'findCarCodeAlarmConfigs');
      expect(findSpy).toHaveBeenCalledWith(request);
    });

    it('should verify that service is called with correct sort parameters', async () => {
      // Mock service method
      const serviceSpy = jest
        .spyOn(service, 'findCarCodeAlarmConfigs')
        .mockResolvedValue({
          success: true,
          data: [mockCarCodeAlarmConfigDAO],
        });

      // Create request object with ASC sort order
      const ascRequest: FindCarCodeAlarmConfigsRequest = {
        sort_order: 'ASC',
        user_id: 'test-user',
      };

      // Call the controller method
      await controller.findCarCodeAlarmConfigs(ascRequest);

      // Verify service was called with ASC sort order
      expect(serviceSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_order: 'ASC',
        }),
      );

      // Reset the mock
      serviceSpy.mockClear();

      // Create request object with DESC sort order
      const descRequest: FindCarCodeAlarmConfigsRequest = {
        sort_order: 'DESC',
        user_id: 'test-user',
      };

      // Call the controller method
      await controller.findCarCodeAlarmConfigs(descRequest);

      // Verify service was called with DESC sort order
      expect(serviceSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_order: 'DESC',
        }),
      );
    });

    it('should handle empty request in findCarCodeAlarmConfigs', async () => {
      jest.spyOn(service, 'findCarCodeAlarmConfigs').mockResolvedValue({
        success: true,
        data: [],
      });
      const request: FindCarCodeAlarmConfigsRequest = { user_id: 'test-user' };
      const result = await controller.findCarCodeAlarmConfigs(request);
      expect(result).toHaveProperty('success');
      expect(result.success?.code).toBe(200);
      const findSpy = jest.spyOn(service, 'findCarCodeAlarmConfigs');
      expect(findSpy).toHaveBeenCalled();
    });

    it('should return car code alarm configs sorted by car_code in the specified order', async () => {
      // Create multiple mock car code alarm config objects with different car_codes
      const mockConfig1: CarCodeAlarmConfigDAO = {
        car_code: 'AAA-001',
        name: 'Test Alarm 1',
        should_raise_alarm: true,
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig2: CarCodeAlarmConfigDAO = {
        car_code: 'BBB-002',
        name: 'Test Alarm 2',
        should_raise_alarm: false,
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig3: CarCodeAlarmConfigDAO = {
        car_code: 'CCC-003',
        name: 'Test Alarm 3',
        should_raise_alarm: true,
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      // Mock service response with configs in ascending order by car_code
      jest.spyOn(service, 'findCarCodeAlarmConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig1, mockConfig2, mockConfig3],
      });

      // Create request object with ASC sort order
      const ascRequest: FindCarCodeAlarmConfigsRequest = {
        sort_order: 'ASC',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.findCarCodeAlarmConfigs(ascRequest);

      // Verify the result has configs in the correct order
      expect(result).toHaveProperty('success');
      expect(result.success?.message).toHaveLength(3);
      expect(result.success?.message[0].car_code).toBe('AAA-001');
      expect(result.success?.message[1].car_code).toBe('BBB-002');
      expect(result.success?.message[2].car_code).toBe('CCC-003');

      // Now test with descending order
      jest.spyOn(service, 'findCarCodeAlarmConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig3, mockConfig2, mockConfig1],
      });

      // Create request object with DESC sort order
      const descRequest: FindCarCodeAlarmConfigsRequest = {
        sort_order: 'DESC',
        user_id: 'test-user',
      };

      // Call the controller method
      const descResult = await controller.findCarCodeAlarmConfigs(descRequest);

      // Verify the result has configs in the correct order
      expect(descResult).toHaveProperty('success');
      expect(descResult.success?.message).toHaveLength(3);
      expect(descResult.success?.message[0].car_code).toBe('CCC-003');
      expect(descResult.success?.message[1].car_code).toBe('BBB-002');
      expect(descResult.success?.message[2].car_code).toBe('AAA-001');
    });
  });

  describe('updateCarCodeAlarmConfig', () => {
    it('should call service.updateCarCodeAlarmConfig with the request data and handle success', async () => {
      jest.spyOn(service, 'updateCarCodeAlarmConfig').mockResolvedValue({
        success: true,
        data: [mockCarCodeAlarmConfigDAO],
      });
      const request: UpdateCarCodeAlarmConfigRequest = {
        request: {
          car_code: 'ALARM-001',
          updated_by: 'test-user',
          name: 'Updated Alarm',
        },
        user_id: 'test-user',
      };
      const result = await controller.updateCarCodeAlarmConfig(request);
      expect(result).toEqual(mockControllerSuccessResponse);
      const updateSpy = jest.spyOn(service, 'updateCarCodeAlarmConfig');
      expect(updateSpy).toHaveBeenCalledWith(request.request);
    });

    it('should handle not found errors from the service', async () => {
      jest.spyOn(service, 'updateCarCodeAlarmConfig').mockResolvedValue({
        success: false,
        error: 'Car code ALARM-001 not found',
        errors: ['Car code ALARM-001 not found'],
      });
      const request: UpdateCarCodeAlarmConfigRequest = {
        request: {
          car_code: 'ALARM-001',
          updated_by: 'test-user',
        },
        user_id: 'test-user',
      };
      const result = await controller.updateCarCodeAlarmConfig(request);
      expect(result).toHaveProperty('error');
      expect(result.error?.code).toBe(404);
      expect(result.error?.fields).toContain('car_code');
      expect(result.error?.error).toBe('Car code ALARM-001 not found');
    });
  });

  describe('deleteCarCodeAlarmConfig', () => {
    it('should call service.deleteCarCodeAlarmConfig with the request data and handle success', async () => {
      jest.spyOn(service, 'deleteCarCodeAlarmConfig').mockResolvedValue({
        success: true,
        data: [{ message: 'Car code alarm config deleted successfully' }],
      });
      const request: DeleteCarCodeAlarmConfigRequest = {
        car_code: 'ALARM-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };
      const result = await controller.deleteCarCodeAlarmConfig(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [],
        },
      });
      const deleteSpy = jest.spyOn(service, 'deleteCarCodeAlarmConfig');
      expect(deleteSpy).toHaveBeenCalledWith(
        request.car_code,
        request.updated_by,
      );
    });

    it('should handle not found errors from the service', async () => {
      jest.spyOn(service, 'deleteCarCodeAlarmConfig').mockResolvedValue({
        success: false,
        error: 'Car code alarm config not found.',
        errors: ['Car code ALARM-999 not found'],
      });
      const request: DeleteCarCodeAlarmConfigRequest = {
        car_code: 'ALARM-999',
        updated_by: 'test-user',
        user_id: 'test-user',
      };
      const result = await controller.deleteCarCodeAlarmConfig(request);
      expect(result).toEqual({
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404,
          message: ['Car code ALARM-999 not found'],
          fields: ['car_code'],
          error: 'Car code alarm config not found.',
        },
      });
    });

    it('should handle other errors from the service', async () => {
      jest.spyOn(service, 'deleteCarCodeAlarmConfig').mockResolvedValue({
        success: false,
        error: 'Error deleting car code alarm config',
      });
      const request: DeleteCarCodeAlarmConfigRequest = {
        car_code: 'ALARM-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };
      const result = await controller.deleteCarCodeAlarmConfig(request);
      expect(result).toEqual({
        error: {
          status: 'ERROR: Bad Request - client-provided data caused the error',
          code: 400,
          message: [],
          fields: ['car_code'],
          error: 'Error deleting car code alarm config',
        },
      });
    });

    it('should validate required fields', async () => {
      // Simulate missing car_code
      const request: DeleteCarCodeAlarmConfigRequest = {
        car_code: '',
        updated_by: 'test-user',
        user_id: 'test-user',
      };
      // Simulate controller validation result
      // (You may need to adjust this if your controller returns a different error structure)
      const result = await controller.deleteCarCodeAlarmConfig(request);
      expect(result).toEqual({
        error: {
          status: 'ERROR: Unprocessable Entity - semantic errors',
          code: 422,
          message: [],
          fields: ['car_code'],
          error: 'The car_code fields are required.',
        },
      });
    });
  });
});
