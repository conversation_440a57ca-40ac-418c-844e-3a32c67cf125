# CAR_CASE Migration Plan

> **Migration Statistics:**
> 
> As of 30/05/2025, the total number of car cases to be migrated is **602,000 records**.


## 1. Introduction and Purpose

This document outlines the plan for migrating the `car_case` and `new_car_case` tables, addressing the current data model and its implications for future scalability, maintainability, and reporting. The primary goal of this migration is to streamline the incident logging process and consolidate related data effectively.

## 2. Current State Analysis

### 2.1. Existing Schema Definitions

#### `car_case` Table

```sql
Table car_case {
  case_id varchar [primary key]
  site_object_id varchar
  sub_site_object_id varchar
  dev_object_id varchar
  additional_dev_info jsonb
  car_code varchar
  logging_date date
  source varchar
  trigger varchar
  total_energy_loss float
  remarks varchar
  status varchar
  capacity float
  created_at timestamp
  updated_at timestamp
  updated_by varchar
}
```

#### `new_car_case` Table

```sql
Table new_car_case {
  id varchar [primary key, note: 'uuid']
  case_id varchar [ref: > car_case.case_id]
  site_object_id varchar
  sub_site_object_id varchar
  dev_object_id varchar
  additional_dev_info jsonb
  car_code varchar
  logging_date date
  source varchar
  trigger varchar
  daily_energy_loss float
  remarks varchar
  created_at timestamp
}
```

### 2.2. Current Relationship and Logic

Currently, `car_case` acts as the parent table, and `new_car_case` is its child.
For every new incident logged:

* A `new_car_case` entry is always added.
* If the `new_car_case` incident occurs within a specific monitoring window (determined by logic involving another table, not detailed here), it is grouped with and reuses an existing `car_case`.
* Otherwise, a new `car_case` entry is created to group the `new_car_case` incident.


The `total_energy_loss` in `car_case` is likely an aggregation of `daily_energy_loss` from its associated `new_car_case` entries. The `car_case` table also holds summary-level information such as `status`, `capacity`, `updated_at`, and `updated_by`, which are not present in `new_car_case`.

### 2.3. Current Data Model Diagram

```mermaid
erDiagram
    car_case {
        varchar case_id PK
        varchar site_object_id
        varchar sub_site_object_id
        varchar dev_object_id
        jsonb additional_dev_info
        varchar car_code
        date logging_date
        varchar source
        varchar trigger
        float total_energy_loss
        varchar remarks
        varchar status
        float capacity
        timestamp created_at
        timestamp updated_at
        varchar updated_by
    }

    new_car_case {
        varchar id PK "uuid"
        varchar case_id FK
        varchar site_object_id
        varchar sub_site_object_id
        varchar dev_object_id
        jsonb additional_dev_info
        varchar car_code
        date logging_date
        varchar source
        varchar trigger
        float daily_energy_loss
        varchar remarks
        timestamp created_at
    }

    car_case ||--o{ new_car_case : "has"
```

## 3. Proposed Changes / Target State

### 3.1. New Schema Definition

#### `CAR_CASES` Table

```sql
Table CAR_CASES {
        id number [primary key]
        site_object_id string
        sub_site_object_id string
        device_id string
        additional_device_info jsonb
        tags string
        case_id string
        car_code string
        source string
        title string
        description string
        remarks string
        recommendation json
        metadata jsonb
        logging_date Timestamp
    }
```

### 3.2. Target Relationship and Logic

In the new design, we are using timestamp-based incident tracking and grouping. As long as a new incident happens within the monitoring window, we will continue to group them together using the existing `case_id`; otherwise, a new `case_id` is provided.

The grouping happens at the backend layer and not at the database layer.

### 3.3. Data Handling for Key Metrics

* `total_energy_loss` and `daily_energy_loss` from the legacy tables will be reviewed for their relevance and potential mapping to new fields or derived metrics in the `CAR_CASES` table. Further clarification is needed on how these metrics will be represented or if they will be deprecated.

## 4. Schema Field Comparison

| Legacy Field (car_case) | Legacy Field (new_car_case) | New Field (CAR_CASES) | Data Type     | Migration Strategy      | Notes                                                |
|-------------------------|-----------------------------|-----------------------|---------------|-------------------------|------------------------------------------------------|
| `case_id`               | `case_id`                   | `case_id`             | VARCHAR       | Retained for grouping   | Auto-generated as `tags-{incrementing_number}`       |
| `site_object_id`        | `site_object_id`            | `site_object_id`      | UUID          | Direct mapping          | Foreign key to site                                  |
| `sub_site_object_id`    | `sub_site_object_id`        | `sub_site_object_id`  | UUID          | Direct mapping          | Foreign key to sub-site                              |
| `dev_object_id`         | `dev_object_id`             | `device_id`           | UUID          | Renamed field           | Improved naming for clarity                          |
| `additional_dev_info`   | `additional_dev_info`       | `additional_dev_info` | JSONB         | Direct mapping          | Device-specific information                          |
| `car_code`              | `car_code`                  | `car_code`            | VARCHAR(50)   | Direct mapping          | References CAR code configuration                    |
| `logging_date`          | `logging_date`              | `logging_date`        | TIMESTAMP     | Direct mapping          | Incident timestamp                                   |
| `source`                | `source`                    | `source`              | VARCHAR(100)  | Direct mapping          | Source system identifier                             |
| `trigger`               | `trigger`                   | `title`               | VARCHAR(255)  | Mapped from `trigger`   | Represents incident title                            |
| `total_energy_loss`     | -                           | *(Removed)*           | -             | Not migrated            | No longer tracked in new schema                      |
| -                       | `daily_energy_loss`         | *(Moved to metadata)* | NUMERIC(18,6) | Moved to metadata field | Now stored as an optional value in the metadata JSON |
| `remarks`               | `remarks`                   | `remarks`             | TEXT          | Direct mapping          | Free-text notes                                      |
| `status`                | -                           | *(Derived)*           | -             | Calculated at runtime   | Determined from CAR code configuration               |
| `capacity`              | -                           | *(Removed)*           | -             | Not migrated            | Use `metadata` if needed                             |
| `created_at`            | `created_at`                | *(Removed)*           | -             | Not migrated            | Use `logging_date` instead                           |
| `updated_at`            | -                           | *(Removed)*           | -             | Not migrated            | Use `logging_date` instead                           |
| `updated_by`            | -                           | *(Removed)*           | -             | Not migrated            | No longer tracked                                    |
| -                       | -                           | `id`                  | SERIAL        | New field               | Primary key                                          |
| -                       | -                           | `tags`                | JSONB         | New field               | Grouping key: `{car_code}-{site}-{subsite}-{device}` |
| -                       | -                           | `recommendation`      | JSONB         | New field               | Structured recommendation data                       |
| -                       | -                           | `metadata`            | JSONB         | New field               | Additional unstructured data                         |

## 5. Key Migration Changes

### 5.1. Schema Simplification

* Removed redundant timestamp fields, standardizing on `logging_date`
* Consolidated status management through CAR code configuration
* Moved energy loss metrics to metadata field

### 5.2. New Features

* Added `tags` for flexible incident grouping and categorization
* Introduced `recommendation` field for structured action items
* Added `metadata` JSONB field for future extensibility

### 5.3. Data Integrity Improvements

* Standardized field naming conventions
* Added proper data types and constraints
* Improved referential integrity with UUID foreign keys

## 6. SQL Script for Data Grouping

To group `car_case` and `new_car_case` records by `case_id` for analysis or migration purposes, use the following SQL script:

## 7. Migration Workflow

The following diagram illustrates the migration workflow to consolidate data from the `car_case` and `new_car_case` tables into the new `CAR_CASES` table structure:

```mermaid
flowchart TB
    subgraph "Step 1: Prepare Mapping"
        CC["car_case table"] --> CM["Create case_id mapping"]
    end
    
    subgraph "Step 2: Build Records"
        CM --> BC["Build base records"]
        NCC["new_car_case table"] --> BC
        BC --> TG["Re-create tags"]
    end
    
    subgraph "Step 3: Enrich Data"
        TG --> ED["Enrich with car_case data"]
        ED --> MD["Preserve historical data in metadata"]
    end
    
    subgraph "Step 4: Finalize"
        MD --> CARC["Insert into car_cases table"]
    end
    
    classDef source fill:#e74c3c,stroke:#333,stroke-width:2px
    classDef step fill:#2874a6,stroke:#333,stroke-width:2px
    classDef target fill:#186a3b,stroke:#333,stroke-width:2px
    
    class NCC,CC source
    class CM,BC,TG,ED,MD step
    class CARC target
```

### Migration Steps Explained

#### Step 1: Prepare Mapping

1. **Create Case ID Mapping**: Create a single mapping table that assigns new case_ids to each unique combination of `car_code`, `site_object_id`, `sub_site_object_id`, and `device_id` from the `car_case` table.

   * Generate unique case_ids using our custom function
   * Format: `car_code-site_object_id-sub_site_object_id-device_id-counter`

#### Step 2: Build Records

1. **Build Base Records**: Start with records from the `new_car_case` table as the primary data source.

   * Join with the case_id mapping to assign new unique case_ids
   * Initialize the structure for our new `car_cases` table

2. **Re-create Tags**: Generate the `tags` field using the format:

   * Format: `{car_code}-{site}-{subsite}-{device}`
   * This creates consistent identifiers for grouping related cases

#### Step 3: Enrich Data

1. **Enrich with car_case Data**: Update the temporary records with supplementary data from the `car_case` table:

   * Add recommendation data from `car_case`

2. **Preserve Historical Data**: Store important historical data in the structured JSONB `metadata` field:

   * Save `total_energy_loss` from `car_case`
   * Save `capacity` from `car_case`
   * Save `original_status` from `car_case`
   * Retain `daily_energy_loss` from `new_car_case`
   * Add migration tracking information

#### Step 4: Finalize

1. **Write to Target Table**: Insert the complete records into the new `CAR_CASES` table

### Field Mapping Details

* `new_car_case.dev_object_id` → `CAR_CASES.device_id`
* `new_car_case.trigger` → `CAR_CASES.title`
* `car_case.status` → `CAR_CASES.metadata->original_status`
* `car_case.total_energy_loss` → `CAR_CASES.metadata->total_energy_loss`
* `new_car_case.daily_energy_loss` → `CAR_CASES.metadata->daily_energy_loss`
* `car_case.recommendation` → `CAR_CASES.recommendation`

This approach ensures that each incident in `new_car_case` becomes a record in the new schema, while still preserving the useful summary data from `car_case`.

## 8. Migration SQL Script

Please refer to the file `car_case_migration_workflow.sql` for the migration script.

