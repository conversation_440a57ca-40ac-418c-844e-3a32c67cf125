openapi: 3.0.3
info:
  title: CAR Wrapper Service
  version: 1.0.0
servers:
  - url: grpc://localhost:8081/
    description: gRPC server endpoint
  - url: https://localhost:8080/
    description: REST API server endpoint
tags:
  - name: Healthcheck
    description: Ping the server to check if it is running
  - name: Car Case Management
    description: Operations about CAR cases
  - name: Car Code Site Configuration
    description: Operations about CAR Code Config & Object Site Id
  - name: Car Code Alarm Config
    description: Operations about CAR Code Alarm Configuration


paths:
  /ping:
    get:
      tags:
        - Healthcheck
      summary: "Ping the server to check its availability"
      description: "Health check endpoint for the main Car Wrapper Service. Maps to gRPC method 'Ping'."
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PingRes'
              example:
                message: "Car Wrapper Service is running"

  ################
  # CAR Case API #
  ################

  # Health check for Car Case service
  /api/v1/dev_car/cases/ping:
    get:
      tags:
        - Car Case Management
      summary: "Ping the car case service to check its availability"
      description: "Health check endpoint for the car case service. Maps to gRPC method 'PingCarCase'."
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PingRes'
              example:
                message: "Car Case Service is running"
  # gRPC Method: FindCarCases
  /api/v1/dev_car/cases:
    get:
      tags:
        - Car Case Management
      summary: "Get all CAR cases"
      description: "Find car cases with optional filters including status (open/close). Maps to gRPC method 'FindCarCases'."
      parameters:
        - in: query
          name: start_date
          schema:
            type: string
            format: date-time
          description: Start date for filtering car cases (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-01T00:00:00Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-01"
        - in: query
          name: end_date
          schema:
            type: string
            format: date-time
          description: End date for filtering car cases (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-30T23:59:59Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-30"
        - in: query
          name: site_object_id
          schema:
            type: string
          description: Optional filter by site_object_id
        - in: query
          name: car_code
          schema:
            type: string
          description: Optional filter by car_code
        - in: query
          name: status
          schema:
            type: string
            enum: [open, close, ""]
          description: Optional filter by case status (open, close, or empty string)
        - in: query
          name: source
          schema:
            type: string
          description: Optional filter by source (e.g., 'test', 'solar_os', 'wind_os')
        - in: query
          name: limit
          schema:
            type: integer
            default: 1000
            minimum: 1
            maximum: 10000
          description: "Maximum number of records to return (default: 1000, max: 10000)"
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          description: "Number of records to skip before starting to return records (default: 0)"
        - in: query
          name: sort_order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          description: "Sort order for results (default: DESC)"
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
          examples:
            user:
              summary: Regular user (email/UUID)
              value: "<EMAIL>"
            microservice:
              summary: Microservice authentication
              value: "aCHW7ds8xn"

      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarCasesRes'
        '400':
          description: Bad Request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: ["start_date", "end_date"]
                      error:
                        type: string
                        example: "Invalid date format. Dates must be in ISO 8601 format."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: ["start_date", "end_date"]
                  error: "Invalid date format. Dates must be in ISO 8601 format."

  # gRPC Method: FindCarCasesByIds
  /api/v1/dev_car/cases/ids:
    post:
      tags:
        - Car Case Management
      summary: "Get CAR cases by their IDs"
      description: "Retrieve detailed information about car cases using their IDs. This endpoint accepts any number of IDs (one or many) in the request body array. Maps to gRPC method 'FindCarCasesByIds'."
      requestBody:
        description: Request body for retrieving car cases by their IDs
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - ids
              properties:
                ids:
                  type: array
                  items:
                    type: string
                  description: Array of car case IDs to retrieve
                  example: ["5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc", "4c6a6333-0412-4e79-8726-5894457c6937"]
                limit:
                  type: integer
                  default: 1000
                  minimum: 1
                  maximum: 10000
                  description: 'Maximum number of records to return (default: 1000, max: 10000)'
                offset:
                  type: integer
                  default: 0
                  minimum: 0
                  description: 'Number of records to skip before starting to return records (default: 0)'
                sort_order:
                  type: string
                  enum: [ASC, DESC]
                  default: DESC
                  description: 'Sort order for results (default: DESC)'
                user_id:
                  type: string
                  description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
            examples:
              multiple_ids_user_paginated:
                summary: Multiple IDs with Pagination (Regular User)
                value:
                  ids: ["5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc", "4c6a6333-0412-4e79-8726-5894457c6937", "23294c01-e167-4815-ad14-353e480fe0d8"]
                  limit: 10
                  offset: 0
                  sort_order: "DESC"
                  user_id: "<EMAIL>"
              multiple_ids_microservice_paginated:
                summary: Multiple IDs with Pagination (Microservice)
                value:
                  ids: ["5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc", "4c6a6333-0412-4e79-8726-5894457c6937"]
                  limit: 5
                  offset: 10
                  sort_order: "ASC"
                  user_id: "aCHW7ds8xn"
              single_id_user:
                summary: Single ID (Regular User)
                value:
                  ids: ["5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc"]
                  user_id: "<EMAIL>"
              minimal_request:
                summary: Minimal Request (Only Required Fields)
                value:
                  ids: ["5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc"]
                  user_id: "<EMAIL>"
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarCasesRes'
        '400':
          description: Bad Request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: ["ids"]
                      error:
                        type: string
                        example: "Invalid request format. 'ids' must be an array of strings."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: ["ids"]
                  error: "Invalid request format. 'ids' must be an array of strings."
        '500':
          description: "Internal server error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  # gRPC Method: CreateCarCase
  /api/v1/dev_car/cases/new:
    post:
      tags:
        - Car Case Management
      summary: "Create a new CAR case"
      description: "Create one or more new car cases. Maps to gRPC method 'CreateCarCase'."
      requestBody:
        description: Request body for adding new CAR Case
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCarCaseDAORequest'
      responses:
        '201':
          description: CAR case created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarCasesRes'
        '400':
          description: Bad Request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: string
                        description: JSON strings of problematic CarCaseDAO objects, or empty array if not applicable
                        example: ["{\"site_object_id\":\"SITE-042\",\"car_code\":\"INVERTER-FAULT\"}"]
                      fields:
                        type: array
                        items:
                          type: string
                        description: Fields that caused the error (empty array if not applicable)
                        example: []
                      error:
                        type: string
                        description: Contains the actual error message
                        example: "Failed to process batch request: Database error occurred"
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: ["{\"site_object_id\":\"SITE-042\",\"car_code\":\"INVERTER-FAULT\",\"source\":\"solar_os\"}"]
                  fields: []
                  error: "Failed to process batch request: Car code config for INVERTER-FAULT and SITE-042 not found"
        '404':
          description: Not Found - No resources to process
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Not Found - no resources to process"
                      code:
                        type: integer
                        example: 404
                      message:
                        type: array
                        items:
                          type: string
                        description: JSON strings of problematic CarCaseDAO objects, or empty array if not applicable
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        description: Fields that caused the error (empty array if not applicable)
                        example: []
                      error:
                        type: string
                        description: Contains the actual error message
                        example: "No car case requests provided"
              example:
                error:
                  status: "ERROR: Not Found - no resources to process"
                  code: 404
                  message: []
                  fields: []
                  error: "No car case requests provided"
        '422':
          description: Unprocessable Entity - Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Unprocessable Entity - semantic errors"
                      code:
                        type: integer
                        example: 422
                      message:
                        type: array
                        items:
                          type: string
                        description: JSON strings of problematic CarCaseDAO objects, or empty array if not applicable
                        example: ["{\"site_object_id\":\"\",\"car_code\":\"\",\"source\":\"\"}"]
                      fields:
                        type: array
                        items:
                          type: string
                        example: ["site_object_id", "car_code", "source"]
                      error:
                        type: string
                        description: Contains the actual error message
                        example: "Validation failed: Missing required fields: site_object_id, car_code, source"
              example:
                error:
                  status: "ERROR: Unprocessable Entity - semantic errors"
                  code: 422
                  message: ["{\"site_object_id\":\"\",\"car_code\":\"\",\"source\":\"\"}"]
                  fields: ["site_object_id", "car_code", "source"]
                  error: "Validation failed: Missing required fields: site_object_id, car_code, source"
        '500':
          description: Internal Server Error - Database or server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database or server error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: string
                        description: JSON strings of problematic CarCaseDAO objects, or empty array if not applicable
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        description: Fields that caused the error (empty array if not applicable)
                        example: []
                      error:
                        type: string
                        description: Contains the actual error message
                        example: "Database error: Connection refused"
              example:
                error:
                  status: "ERROR: Internal Server Error - Database or server error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error: Connection refused"

  ################
  # CAR Code Site Config API #
  ################

  # Health check for Car Code Config service
  /api/v1/dev_car/carcode-config/ping:
    get:
      tags:
        - Car Code Site Configuration
      summary: "Ping the car code config service to check its availability"
      description: "Health check endpoint for the car code config service. Maps to gRPC method 'PingCarCodeConfig'."
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PingRes'
              example:
                message: "Car Code Config Service is running"

  # gRPC Method: FindCarCodeConfigs
  /api/v1/dev_car/carcode-config/:
    get:
      tags:
        - Car Code Site Configuration
      summary: "Get all the CAR config"
      description: "Get list of CAR configs with optional filters. Results are sorted by car_code field first, then by site_object_id. Maps to gRPC method 'FindCarCodeConfigs'."
      parameters:
        - in: query
          name: car_code
          schema:
            type: string
          description: Optional filter by car_code
        - in: query
          name: site_object_id
          schema:
            type: string
          description: Optional filter by site_object_id
        - in: query
          name: limit
          schema:
            type: integer
            default: 500
            minimum: 1
            maximum: 1000
          description: "Maximum number of records to return (default: 500, max: 1000)"
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          description: "Number of records to skip before starting to return records (default: 0)"
        - in: query
          name: sort_order
          schema:
            type: string
            enum: [ASC, DESC]
            default: ASC
          description: "Sort order for results by car_code field (default: ASC)"
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
          examples:
            user:
              summary: Regular user (email/UUID)
              value: "<EMAIL>"
            microservice:
              summary: Microservice authentication
              value: "aCHW7ds8xn"
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeConfigDAO'
              example:
                success:
                  status: "success"
                  code: 200
                  message: [
                    {
                      "site_object_id": "site_1",
                      "car_code": "HSO",
                      "monitoring_window": 4,
                      "enabled": true,
                      "thresholds": { "threshold1": "10", "threshold2": "20" },
                      "recommendation": { "action": "Check inverter connections", "priority": "high" },
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  /api/v1/dev_car/carcode-config/history:
    get:
      tags:
        - Car Code Site Configuration
      summary: "Get the history of CAR Configs"
      description: "Get the history of CAR config with optional filters. Maps to gRPC method 'FindCarCodeConfigHistory'."
      parameters:
        - in: query
          name: car_code
          schema:
            type: string
          description: Optional filter by car_code
        - in: query
          name: site_object_id
          schema:
            type: string
          description: Optional filter by site_object_id
        - in: query
          name: start_date
          schema:
            type: string
            format: date-time
          description: Start date for filtering config history (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-01T00:00:00Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-01"
        - in: query
          name: end_date
          schema:
            type: string
            format: date-time
          description: End date for filtering config history (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-30T23:59:59Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-30"
        - in: query
          name: sort_order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          description: "Sort order for results (default: DESC)"
        - in: query
          name: limit
          schema:
            type: integer
            default: 2000
            minimum: 1
            maximum: 5000
          description: "Maximum number of records to return (default: 2000, max: 5000)"
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          description: "Number of records to skip before starting to return records (default: 0)"
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
          examples:
            user:
              summary: Regular user (email/UUID)
              value: "<EMAIL>"
            microservice:
              summary: Microservice authentication
              value: "aCHW7ds8xn"
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeConfigHistoryDAO'
              example:
                success:
                  status: "success"
                  code: 200
                  message: [
                    {
                      "car_code": "HSO",
                      "site_object_id": "site_1",
                      "old_value": "{\"car_code\":\"HSO\",\"site_object_id\":\"site_1\",\"monitoring_window\":3,\"enabled\":false,\"thresholds\":{\"threshold1\":\"10\"},\"recommendation\":{\"action\":\"Check inverter\"},\"updated_at\":\"2023-05-15T10:00:00Z\",\"updated_by\":\"admin\"}",
                      "new_value": "{\"car_code\":\"HSO\",\"site_object_id\":\"site_1\",\"monitoring_window\":3,\"enabled\":true,\"thresholds\":{\"threshold1\":\"10\"},\"recommendation\":{\"action\":\"Check inverter\"},\"updated_at\":\"2023-05-15T10:30:00Z\",\"updated_by\":\"admin\"}",
                      "transaction_type": "update",
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '400':
          description: Bad Request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: ["start_date", "end_date"]
                      error:
                        type: string
                        example: "Invalid date format. Dates must be in ISO 8601 format."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: ["start_date", "end_date"]
                  error: "Invalid date format. Dates must be in ISO 8601 format."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  # gRPC Method: CreateCarCodeConfig
  /api/v1/dev_car/carcode-config/new:
    post:
      tags:
        - Car Code Site Configuration
      summary: "Create a new CAR Config"
      description: "Create one or more new car code configurations. Maps to gRPC method 'CreateCarCodeConfig'."
      requestBody:
        description: Request body for creating new CAR config
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCarCodeConfigDAORequest'
      responses:
        '201':
          description: CAR config created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 201
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeConfigDAO'
              example:
                success:
                  status: "success"
                  code: 201
                  message: [
                    {
                      "site_object_id": "site_1",
                      "car_code": "HSO",
                      "monitoring_window": 4,
                      "enabled": true,
                      "thresholds": '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]',
                      "recommendation": '[{"name": "Check inverter connections", "priority": "high"}]',
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '400':
          description: "400 Bad Request: Invalid request body"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "site_object_id", "car_code", "monitoring_window", "enabled", "updated_at", "updated_by" ]
                      error:
                        type: string
                        example: "The request body is invalid or contains invalid fields."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: [ "site_object_id", "car_code", "monitoring_window", "enabled", "updated_at", "updated_by" ]
                  error: "The request body is invalid or contains invalid fields."
        '409':
          description: "409 Conflict: Duplicate entry of car_code and site_object_id"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Conflict - Duplicate entry"
                      code:
                        type: integer
                        example: 409
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code", "site_object_id" ]
                      error:
                        type: string
                        example: "A record with the same car_code and site_object_id already exists."
              example:
                error:
                  status: "ERROR: Conflict - Duplicate entry"
                  code: 409
                  message: []
                  fields: [ "car_code", "site_object_id" ]
                  error: "A record with the same car_code and site_object_id already exists."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  # gRPC Method: UpdateCarCodeConfig
  /api/v1/dev_car/carcode-config/update:
    post:
      tags:
        - Car Code Site Configuration
      summary: "Update an existing CAR Config using the car_code and site_object_id in the request body"
      description: "Update an existing car code configuration. Maps to gRPC method 'UpdateCarCodeConfig'."
      parameters: []
      requestBody:
        description: Request body for updating an existing CAR config, identified by car_code and site_object_id in the request
        content:
          application/json:
            schema:
              type: object
              required:
                - request
                - user_id
              properties:
                request:
                  type: object
                  required:
                    - car_code
                    - site_object_id
                    - updated_by
                  properties:
                    car_code:
                      type: string
                      description: The car code of the CAR config to update (must match path parameter)
                    site_object_id:
                      type: string
                      description: The site object ID of the CAR config to update (must match path parameter)
                    monitoring_window:
                      type: number
                      description: number of days to monitor the car case from its logging window
                    enabled:
                      type: boolean
                      description: enable / disable new incoming car cases to be accepted
                    thresholds:
                      type: object
                      description: threshold level for the case triggering
                    recommendation:
                      type: object
                      description: if they have recommendation to address the issue, user can provide
                    updated_by:
                      type: string
                      description: the user that would be updating the config
                user_id:
                  type: string
                  description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
            examples:
              user:
                summary: Regular user authentication
                value:
                  request:
                    car_code: "HSO"
                    site_object_id: "site_1"
                    monitoring_window: 4
                    enabled: true
                    thresholds: '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]'
                    recommendation: '[{"name": "Check inverter connections", "priority": "high"}]'
                    updated_by: "admin"
                  user_id: "<EMAIL>"
              microservice:
                summary: Microservice authentication
                value:
                  request:
                    car_code: "HSO"
                    site_object_id: "site_1"
                    monitoring_window: 4
                    enabled: true
                    thresholds: '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]'
                    recommendation: '[{"name": "Check inverter connections", "priority": "high"}]'
                    updated_by: "admin"
                  user_id: "aCHW7ds8xn"
      responses:
        '200':
          description: CAR config updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeConfigDAO'
              example:
                success:
                  status: "success"
                  code: 200
                  message: [
                    {
                      "site_object_id": "site_1",
                      "car_code": "HSO",
                      "monitoring_window": 4,
                      "enabled": true,
                      "thresholds": { "threshold1": "10", "threshold2": "20" },
                      "recommendation": { "action": "Check inverter connections", "priority": "high" },
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '400':
          description: "400 Bad Request: Invalid request body"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "request.car_code", "request.site_object_id", "request.updated_by" ]
                      error:
                        type: string
                        example: "The request body is invalid or contains invalid fields."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: [ "request.car_code", "request.site_object_id", "request.updated_by" ]
                  error: "The request body is invalid or contains invalid fields."
        '404':
          description: "404 Not Found: CAR config not found"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Not Found - no resources to process"
                      code:
                        type: integer
                        example: 404
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code", "site_object_id" ]
                      error:
                        type: string
                        example: "CAR config with the specified car_code and site_object_id not found."
              example:
                error:
                  status: "ERROR: Not Found - no resources to process"
                  code: 404
                  message: []
                  fields: [ "car_code", "site_object_id" ]
                  error: "CAR config with the specified car_code and site_object_id not found."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  # gRPC Method: DeleteCarCodeConfig
  /api/v1/dev_car/carcode-config/delete:
    delete:
      tags:
        - Car Code Site Configuration
      summary: "Delete an existing CAR Config"
      description: "Delete an existing car code configuration. Maps to gRPC method 'DeleteCarCodeConfig'."
      parameters: []
      requestBody:
        description: Request body for deleting an existing CAR config
        content:
          application/json:
            schema:
              type: object
              required:
                - car_code
                - site_object_id
                - updated_by
                - user_id
              properties:
                car_code:
                  type: string
                  description: The car code of the CAR config to delete
                site_object_id:
                  type: string
                  description: The site object ID of the CAR config to delete
                updated_by:
                  type: string
                  description: The individual deleting the car code config
                user_id:
                  type: string
                  description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
            examples:
              user:
                summary: Regular user authentication
                value:
                  car_code: "HSO"
                  site_object_id: "site_1"
                  updated_by: "admin"
                  user_id: "<EMAIL>"
              microservice:
                summary: Microservice authentication
                value:
                  car_code: "HSO"
                  site_object_id: "site_1"
                  updated_by: "admin"
                  user_id: "aCHW7ds8xn"
      responses:
        '200':
          description: "CAR config deleted successfully"
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: "Car config deleted successfully"
              example:
                success:
                  status: "success"
                  code: 200
                  message: "Car config deleted successfully"
        '404':
          description: "CAR config not found"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Not Found - no resources to process"
                      code:
                        type: integer
                        example: 404
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code", "site_object_id" ]
                      error:
                        type: string
                        example: "CAR config with the specified car_code and site_object_id not found."
              example:
                error:
                  status: "ERROR: Not Found - no resources to process"
                  code: 404
                  message: []
                  fields: [ "car_code", "site_object_id" ]
                  error: "CAR config with the specified car_code and site_object_id not found."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  ################
  # CAR Code Alarm Config API #
  ################

  # Health check for Car Code Alarm Config service
  /api/v1/dev_car/alarm-config/ping:
    get:
      tags:
        - Car Code Alarm Config
      summary: "Ping the car code alarm config service to check its availability"
      description: "Health check endpoint for the car code alarm config service. Maps to gRPC method 'PingCarCodeAlarmConfig'."
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PingRes'
              example:
                message: "Car Code Alarm Config Service is running"

  /api/v1/dev_car/alarm-config/:
    get:
      tags:
        - Car Code Alarm Config
      summary: "Get all the CAR Alarm Config"
      description: "Get list of CAR alarm configs with optional filters. Results are sorted by car_code field. Maps to gRPC method 'FindCarCodeAlarmConfigs'."
      parameters:
        - in: query
          name: car_code
          schema:
            type: string
          description: Optional filter by car_code
        - in: query
          name: limit
          schema:
            type: integer
            default: 500
            minimum: 1
            maximum: 1000
          description: "Maximum number of records to return (default: 500, max: 1000)"
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          description: "Number of records to skip before starting to return records (default: 0)"
        - in: query
          name: sort_order
          schema:
            type: string
            enum: [ASC, DESC]
            default: ASC
          description: "Sort order for results (default: ASC) records will be sorted by car_code"
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
          examples:
            user:
              summary: Regular user (email/UUID)
              value: "<EMAIL>"
            microservice:
              summary: Microservice authentication
              value: "aCHW7ds8xn"
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarCodeAlarmConfigsRes'
        '500':
          description: DB error

  /api/v1/dev_car/alarm-config/history:
    get:
      tags:
        - Car Code Alarm Config
      summary: "Get the history of CAR Alarm Config"
      description: "Get the history of CAR alarm config with optional filters. Maps to gRPC method 'FindCarCodeAlarmConfigHistory'."
      parameters:
        - in: query
          name: car_code
          schema:
            type: string
          description: Optional filter by car_code
        - in: query
          name: start_date
          schema:
            type: string
            format: date-time
          description: Start date for filtering alarm config history (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-01T00:00:00Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-01"
        - in: query
          name: end_date
          schema:
            type: string
            format: date-time
          description: End date for filtering alarm config history (ISO 8601 format)
          examples:
            fullFormat:
              summary: Full ISO 8601 format with time
              value: "2025-04-30T23:59:59Z"
            dateOnly:
              summary: Date only format
              value: "2025-04-30"
        - in: query
          name: sort_order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          description: "Sort order for results (default: DESC)"
        - in: query
          name: limit
          schema:
            type: integer
            default: 2000
            minimum: 1
            maximum: 5000
          description: "Maximum number of records to return (default: 2000, max: 5000)"
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          description: "Number of records to skip before starting to return records (default: 0)"
        - in: query
          name: user_id
          required: true
          schema:
            type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
          examples:
            user:
              summary: Regular user (email/UUID)
              value: "<EMAIL>"
            microservice:
              summary: Microservice authentication
              value: "aCHW7ds8xn"
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarCodeAlarmConfigHistoryRes'
              example:
                success:
                  status: "success"
                  code: 200
                  message: [
                    {
                      "car_code": "HSO",
                      "old_value": "{\"car_code\":\"HSO\",\"name\":\"High String Outage\",\"category\":\"Inverter\",\"device_type\":\"String Inverter\",\"should_raise_alarm\":false,\"updated_at\":\"2023-05-15T10:00:00Z\",\"updated_by\":\"admin\"}",
                      "new_value": "{\"car_code\":\"HSO\",\"name\":\"High String Outage\",\"category\":\"Inverter\",\"device_type\":\"String Inverter\",\"should_raise_alarm\":true,\"updated_at\":\"2023-05-15T10:30:00Z\",\"updated_by\":\"admin\"}",
                      "transaction_type": "update",
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '400':
          description: Bad Request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: ["start_date", "end_date"]
                      error:
                        type: string
                        example: "Invalid date format. Dates must be in ISO 8601 format."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: ["start_date", "end_date"]
                  error: "Invalid date format. Dates must be in ISO 8601 format."
        '500':
          description: Internal Server Error - Database error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  /api/v1/dev_car/alarm-config/new:
    post:
      tags:
        - Car Code Alarm Config
      summary: "Create a new CAR Alarm Config"
      description: "Create one or more new car code alarm configurations. Maps to gRPC method 'CreateCarCodeAlarmConfig'."
      requestBody:
        description: Request body for creating new CAR alarm config
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCarCodeAlarmConfigDAORequest'
      responses:
        '201':
          description: CAR Alarm config created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 201
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeAlarmConfigDAO'
              example:
                success:
                  status: "success"
                  code: 201
                  message: [
                    {
                      "car_code": "HSO",
                      "name": "High String Outage",
                      "category": "Inverter",
                      "device_type": "String Inverter",
                      "should_raise_alarm": true,
                      "should_raise_alarm_new_value": true,
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '409':
          description: "409 Conflict: Duplicate entry of car_code"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Conflict - Duplicate entry"
                      code:
                        type: integer
                        example: 409
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code" ]
                      error:
                        type: string
                        example: "A record with the same car_code already exists."
              example:
                error:
                  status: "ERROR: Conflict - Duplicate entry"
                  code: 409
                  message: []
                  fields: [ "car_code" ]
                  error: "A record with the same car_code already exists."
        '400':
          description: "400 Bad Request: Invalid request body"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "requests.car_code", "requests.name", "requests.category" ]
                      error:
                        type: string
                        example: "The following required fields are missing."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: [ "requests.car_code", "requests.name", "requests.category" ]
                  error: "The following required fields are missing."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  /api/v1/dev_car/alarm-config/update:
    post:
      tags:
        - Car Code Alarm Config
      summary: "Update an existing CAR Alarm Config using the car_code in the request body"
      description: "Update an existing car code alarm configuration. Maps to gRPC method 'UpdateCarCodeAlarmConfig'."
      parameters: []
      requestBody:
        description: Request body for updating an existing CAR alarm config, identified by car_code in the request
        content:
          application/json:
            schema:
              type: object
              required:
                - request
                - user_id
              properties:
                request:
                  type: object
                  required:
                    - car_code
                    - updated_by
                  properties:
                    car_code:
                      type: string
                      description: The car code of the CAR definition to update
                    name:
                      type: string
                      description: label for the car_codes
                    category:
                      type: string
                      description: store additional info regarding the categorisation of car_code
                    device_type:
                      type: string
                      description: store additional info regarding the device_type assigned to car_code
                    should_raise_alarm:
                      type: boolean
                      description: used to define whether to raise alarm
                    should_raise_alarm_new_value:
                      type: boolean
                      description: used to define whether to raise alarm (new value)
                    updated_by:
                      type: string
                      description: the user that would be updating the config
                user_id:
                  type: string
                  description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
            examples:
              user:
                summary: Regular user authentication
                value:
                  request:
                    car_code: "HSO"
                    name: "High String Outage"
                    category: "Inverter"
                    device_type: "String Inverter"
                    should_raise_alarm: true
                    should_raise_alarm_new_value: true
                    updated_by: "admin"
                  user_id: "<EMAIL>"
              microservice:
                summary: Microservice authentication
                value:
                  request:
                    car_code: "HSO"
                    name: "High String Outage"
                    category: "Inverter"
                    device_type: "String Inverter"
                    should_raise_alarm: true
                    should_raise_alarm_new_value: true
                    updated_by: "admin"
                  user_id: "aCHW7ds8xn"
      responses:
        '200':
          description: CAR config updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: array
                        items:
                          $ref: '#/components/schemas/CarCodeAlarmConfigDAO'
              example:
                success:
                  status: "success"
                  code: 200
                  message: [
                    {
                      "car_code": "HSO",
                      "name": "High String Outage",
                      "category": "Inverter",
                      "device_type": "String Inverter",
                      "should_raise_alarm": true,
                      "should_raise_alarm_new_value": true,
                      "updated_at": "2023-05-15T10:30:00Z",
                      "updated_by": "admin"
                    }
                  ]
        '400':
          description: "400 Bad Request: Invalid request body"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Bad Request - Invalid input data"
                      code:
                        type: integer
                        example: 400
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "request.car_code", "request.updated_by" ]
                      error:
                        type: string
                        example: "The request body is invalid or contains invalid fields."
              example:
                error:
                  status: "ERROR: Bad Request - Invalid input data"
                  code: 400
                  message: []
                  fields: [ "request.car_code", "request.updated_by" ]
                  error: "The request body is invalid or contains invalid fields."
        '404':
          description: "404 Not Found: CAR config not found"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Not Found - no resources to process"
                      code:
                        type: integer
                        example: 404
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code" ]
                      error:
                        type: string
                        example: "CAR config with the specified car_code not found."
              example:
                error:
                  status: "ERROR: Not Found - no resources to process"
                  code: 404
                  message: []
                  fields: [ "car_code" ]
                  error: "CAR config with the specified car_code not found."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

  /api/v1/dev_car/alarm-config/delete:
    delete:
      tags:
        - Car Code Alarm Config
      summary: "Delete an existing CAR Alarm Config"
      description: "Delete an existing car code alarm configuration. Maps to gRPC method 'DeleteCarCodeAlarmConfig'."
      parameters: []
      requestBody:
        description: Request body for deleting an existing CAR alarm config
        content:
          application/json:
            schema:
              type: object
              required:
                - car_code
                - updated_by
                - user_id
              properties:
                car_code:
                  type: string
                  description: The car code of the CAR definition to delete
                updated_by:
                  type: string
                  description: The user who is deleting the car code alarm config
                user_id:
                  type: string
                  description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
            examples:
              user:
                summary: Regular user authentication
                value:
                  car_code: "HSO"
                  updated_by: "admin"
                  user_id: "<EMAIL>"
              microservice:
                summary: Microservice authentication
                value:
                  car_code: "HSO"
                  updated_by: "admin"
                  user_id: "aCHW7ds8xn"
      responses:
        '200':
          description: "CAR config deleted successfully"
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "success"
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: "Car alarm config deleted successfully"
              example:
                success:
                  status: "success"
                  code: 200
                  message: "Car alarm config deleted successfully"
        '404':
          description: "CAR config not found"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Not Found - no resources to process"
                      code:
                        type: integer
                        example: 404
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: [ "car_code" ]
                      error:
                        type: string
                        example: "CAR alarms config with the specified car_code not found."
              example:
                error:
                  status: "ERROR: Not Found - no resources to process"
                  code: 404
                  message: []
                  fields: [ "car_code" ]
                  error: "CAR alarms config with the specified car_code not found."
        '500':
          description: "500 Internal Server Error: Database error"
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "ERROR: Internal Server Error - Database error"
                      code:
                        type: integer
                        example: 500
                      message:
                        type: array
                        items:
                          type: object
                        example: []
                      fields:
                        type: array
                        items:
                          type: string
                        example: []
                      error:
                        type: string
                        example: "Database error occurred while processing the request."
              example:
                error:
                  status: "ERROR: Internal Server Error - Database error"
                  code: 500
                  message: []
                  fields: []
                  error: "Database error occurred while processing the request."

###########
# Schemas #
###########
components:
  schemas:

    #################
    # Ping Schemas  #
    #################

    PingRes:
      title: "PingRes"
      description: Health Check of the Server
      required:
        - message
      type: object
      properties:
        message:
          type: string
          description: Return "pong" after a successful ping
          example: "pong"

    #######################
    # Car Case Schemas    #
    #######################

    CreateCarCaseDAORequest:
      title: "CreateCarCaseDAORequest"
      description: Request wrapper for creating car cases in batch
      type: object
      required:
        - requests
        - user_id
      properties:
        requests:
          type: array
          description: Array of car case objects to create
          items:
            $ref: '#/components/schemas/CarCaseDAO'
        user_id:
          type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
      example:
        requests:
          - site_object_id: "site_1"
            sub_site_object_id: "sub-002"
            device_id: "dev-039"
            car_code: "hso"
            source: "solar_os"
            title: "Case: hso detected at site_1"
            description: "Test case for hso issue on site_1 with monitoring period of 4 days."
            recommendation: '{"action": "Optimize system settings"}'
            metadata:
              daily_energy_loss: "208"
            logging_date: "2025-04-20T09:13:08.939Z"
          - site_object_id: "site_2"
            sub_site_object_id: "sub-003"
            device_id: "dev-045"
            car_code: "lso"
            source: "wind_os"
            title: "Case: lso detected at site_2"
            description: "Test case for lso issue on site_2 with monitoring period of 3 days."
            recommendation: '{"action": "Inspect wind turbine connections", "priority": "high"}'
            metadata:
              daily_energy_loss: "150"
            logging_date: "2025-04-21T10:45:30.123Z"
        user_id: "<EMAIL>"

    CarCasesRes:
      title: "CarCasesRes"
      description: Car Case Response upon sending CarCaseDAO
      type: object
      oneOf:
        - type: object
          properties:
            success:
              type: object
              properties:
                status:
                  type: string
                  example: "success"
                code:
                  type: integer
                  example: 201
                message:
                  type: array
                  items:
                    $ref: '#/components/schemas/CarCaseConsoDAO'
              example:
                success:
                  status: "success"
                  code: 201
                  message: [
                    {
                      "id": "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc",
                      "conso_id": [ "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc", "4c6a6333-0412-4e79-8726-5894457c6937", "23294c01-e167-4815-ad14-353e480fe0d8" ],
                      "site_object_id": "site_1",
                      "sub_site_object_id": "sub-002",
                      "device_id": "dev-039",
                      "additional_device_info": '{"device_type": "gateway", "firmware": "v1.9.1", "manufacturer": "DeviceMakers", "serial_number": "SN936687"}',
                      "tags": "hso-site_1-sub-002-dev-039",
                      "case_id": "hso-site_1-sub-002-dev-039-1",
                      "car_code": "hso",
                      "source": "wind_os",
                      "title": "Case: hso detected at site_1",
                      "description": "Test case for hso issue on site_1 with monitoring period of 4 days. This case is 2 days old.",
                      "remarks": "Regular monitoring required. Previous incidents have been resolved.",
                      "recommendation": '{"action": "Optimize system settings"}',
                      "metadata": '{"daily_energy_loss": "208"}',
                      "logging_date": "2025-04-20T09:13:08.939Z",
                      "status": "open",
                      "occurrence_count": 3,
                      "first_occurrence": "2025-04-20T09:13:08.939Z",
                      "most_recent_occurrence": "2025-04-20T09:13:08.939Z"
                    }
                  ]
        - type: object
          properties:
            error:
              type: object
              properties:
                status:
                  type: string
                  example: "error"
                code:
                  type: integer
                  example: 400
                message:
                  type: array
                  items:
                    type: string
                  description: JSON strings of problematic CarCaseDAO objects
                  example: ["{\"site_object_id\":\"SITE-042\",\"car_code\":\"INVERTER-FAULT\"}"]
                fields:
                  type: array
                  items:
                    type: string
                  example: ["site_object_id", "car_code"]
                error:
                  type: string
                  example: "Validation failed: Missing required fields"
          example:
            error:
              status: "ERROR: Bad Request - Invalid input data"
              code: 400
              message: ["{\"site_object_id\":\"SITE-042\",\"car_code\":\"INVERTER-FAULT\"}"]
              fields: ["site_object_id", "car_code"]
              error: "Validation failed: Missing required fields"

    CarCaseDAO:
      title: "CarCaseDAO"
      description: Car Case Schema When Sending Payload to CarWrapperService
      type: object
      required:
        - site_object_id
        - car_code
        - source
        - title
        - logging_date
      properties:
        site_object_id:
          type: string
          description: location id
        sub_site_object_id:
          type: string
          description: segment within the location id
        device_id:
          type: string
          description: actual device id
        case_id:
          type: string
          description: Groups consecutive car case entries. Format is the same as tags but with a sequence number appended (e.g., tags-1, tags-2). Auto-generated, not required in POST to CarWrapperService.
        car_code:
          type: string
          description: Used to identify if expiry/normalisation window
        source:
          type: string
          description: Correctly identify which OS Category this data is from
        title:
          type: string
          description: title for case case, if title not available, auto creation is required
        description:
          type: string
          description: description for car case
        remarks:
          type: string
          description: additional remarks or notes about the car case
        recommendation:
          type: string
          description: Structured recommendations with details, stored as a JSON string
          example: '{"action": "Reset inverter and check error logs", "priority": "high"}'
        metadata:
          type: string
          description: Dynamic key-value structure for car case metadata, stored as a JSON string
          example: '{"daily_energy_loss": "150", "error_code": "E-456", "damage_type": "crack"}'
        logging_date:
          type: string
          description: Logging time of the car case
        additional_device_info:
          type: string
          description: Optional. Stores extra device-specific metadata as a JSON string for advanced troubleshooting or integration purposes.
          example: '{"device_type": "gateway", "firmware": "v1.9.1", "manufacturer": "DeviceMakers", "serial_number": "SN936687"}'

    CarCaseMetadataDAO:
      title: "CarCaseMetadataDAO"
      description: "[DEPRECATED] Dynamic key-value structure for car case metadata. This schema is kept for reference only. The metadata structure is now defined directly in CarCaseDAO."
      type: object
      properties:
        details:
          type: object
          description: Key-value pairs containing metadata details
          additionalProperties:
            type: string
          example:
            daily_energy_loss: "150"

    CarCaseConsoDAO:
      title: "CarCaseConsoDAO"
      description: Response object that users will receive when calling the car service.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the consolidated car case (UUID format)
          example: "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc"
        conso_id:
          type: array
          items:
            type: string
            format: uuid
          description: List of related car case IDs (UUID format)
          example: [ "5ce5ac3d-9939-4b7f-8fbd-fd573593f9bc", "4c6a6333-0412-4e79-8726-5894457c6937", "23294c01-e167-4815-ad14-353e480fe0d8" ]
        site_object_id:
          type: string
          description: location id
          example: "site_1"
        sub_site_object_id:
          type: string
          description: segment within the location id
          example: "sub-002"
        device_id:
          type: string
          description: actual device id
          example: "dev-039"
        additional_device_info:
          type: string
          description: Optional. Stores extra device-specific metadata as a JSON string for advanced troubleshooting or integration purposes.
          example: '{"device_type": "gateway", "firmware": "v1.9.1", "manufacturer": "DeviceMakers", "serial_number": "SN936687"}'
        tags:
          type: string
          description: Uniquely identifies the repeated cases. Format is car_code-site_object_id-sub_site_object_id-device_id (auto-generated, no need to pass in POST to CarWrapperService).
          example: "hso-site_1-sub-002-dev-039"
        case_id:
          type: string
          description: Groups consecutive car case entries. Format is the same as tags but with a sequence number appended (e.g., tags-1, tags-2). Auto-generated, not required in POST to CarWrapperService.
          example: "hso-site_1-sub-002-dev-039-1"
        car_code:
          type: string
          description: Used to identify if expiry/normalisation window
          example: "hso"
        source:
          type: string
          description: Correctly identify which OS Category this data is from
          example: "wind_os"
        title:
          type: string
          description: title for case case, if title not available, auto creation is required
          example: "Case: hso detected at site_1"
        description:
          type: string
          description: description for car case
          example: "Test case for hso issue on site_1 with monitoring period of 4 days. This case is 2 days old."
        remarks:
          type: string
          description: additional remarks or notes about the car case
          example: "Regular monitoring required. Previous incidents have been resolved."
        recommendation:
          type: object
          description: Structured recommendations
          additionalProperties:
            type: string
          example:
            action: "Optimize system settings"
        metadata:
          type: string
          description: Dynamic key-value structure for car case metadata stored as a JSON string
          example: '{"daily_energy_loss": "208"}'
        logging_date:
          type: string
          format: date-time
          description: Logging time of the car case
          example: "2025-04-20T09:13:08.939Z"
        status:
          type: string
          enum: [ open, close, "" ]
          description: Indicates whether the case is open or closed based on the monitoring window. Empty string if no monitoring window is configured.
          example: "open"
        occurrence_count:
          type: integer
          description: Number of occurrences of this case
          example: 3
        first_occurrence:
          type: string
          format: date-time
          description: Timestamp of the first occurrence of this case
          example: "2025-04-20T09:13:08.939Z"
        most_recent_occurrence:
          type: string
          format: date-time
          description: Timestamp of the most recent occurrence of this case
          example: "2025-04-20T09:13:08.939Z"

    #################################
    # Car Code Config Schemas       #
    #################################

    CreateCarCodeConfigDAORequest:
      title: "CreateCarCodeConfigDAORequest"
      description: Request wrapper for creating car code configurations in batch
      type: object
      required:
        - requests
        - user_id
      properties:
        requests:
          type: array
          description: Array of car code configuration objects to create
          items:
            $ref: '#/components/schemas/CarCodeConfigDAO'
        user_id:
          type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
      example:
        requests:
          - site_object_id: "site_1"
            car_code: "HSO"
            monitoring_window: 4
            enabled: true
            thresholds: '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]'
            recommendation: '[{"name": "Check inverter connections", "priority": "high"}]'
            updated_at: "2023-05-15T10:30:00Z"
            updated_by: "admin"
          - site_object_id: "site_2"
            car_code: "AMD"
            monitoring_window: 3
            enabled: true
            thresholds: '[{"name": "threshold1", "value": "15"}, {"name": "threshold2", "value": "25"}]'
            recommendation: '[{"name": "Inspect solar panels", "priority": "medium"}]'
            updated_at: "2023-05-15T10:30:00Z"
            updated_by: "admin"
        user_id: "<EMAIL>"

    CarCodeConfigRes:
      title: "CarCodeConfigRes"
      description: Car Code Config Response
      type: object
      oneOf:
        - type: object
          properties:
            success:
              type: object
              properties:
                status:
                  type: string
                  example: "success"
                code:
                  type: integer
                  example: 201
                message:
                  type: array
                  items:
                    $ref: '#/components/schemas/CarCodeConfigDAO'
          example:
            success:
              status: "success"
              code: 201
              message: [
                {
                  "site_object_id": "site_1",
                  "car_code": "HSO",
                  "monitoring_window": 4,
                  "enabled": true,
                  "thresholds": '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]',
                  "recommendation": '[{"name": "Check inverter connections", "priority": "high"}]',
                  "updated_at": "2023-05-15T10:30:00Z",
                  "updated_by": "admin"
                }
              ]
        - type: object
          properties:
            error:
              type: object
              properties:
                status:
                  type: string
                  example: "ERROR: Bad Request - Invalid input data"
                code:
                  type: integer
                  example: 400
                message:
                  type: array
                  items:
                    type: object
                  example: []
                fields:
                  type: array
                  items:
                    type: string
                  example: ["site_object_id", "car_code"]
                error:
                  type: string
                  example: "The request body is invalid or contains invalid fields."
          example:
            error:
              status: "ERROR: Bad Request - Invalid input data"
              code: 400
              message: []
              fields: ["site_object_id", "car_code"]
              error: "The request body is invalid or contains invalid fields."


    CarCodeConfigDeleteRes:
      title: "CarCodeConfigDeleteRes"
      description: Car Code Config Delete Response
      type: object
      properties:
        status:
          type: string
          example: "success"
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Car config with site_object_id {site_object_id} and car_code {car_code} deleted successfully"

    CarCodeConfigHistoryRes:
      title: "CarCodeConfigHistoryRes"
      description: Car Code Config History Response
      type: object
      properties:
        status:
          type: string
          example: "success"
        code:
          type: integer
          example: 200
        message:
          type: array
          items:
            $ref: '#/components/schemas/CarCodeConfigHistoryDAO'

    CarCodeConfigDAO:
      title: "CarCodeConfigDAO"
      description: Car Code Config Schema
      type: object
      required:
        - site_object_id
        - car_code
        - monitoring_window
        - enabled
        - updated_at
        - updated_by
      properties:
        site_object_id:
          type: string
          description: location id
        car_code:
          type: string
          description: label used to identify expiry/normalisation window
        monitoring_window:
          type: number
          description: number of days to monitor the car case from its logging window
        enabled:
          type: boolean
          description: enable / disable new incoming car cases to be accepted
        thresholds:
          type: string
          description: Threshold level for the case triggering, stored as a stringified JSON array of objects
          example: '[{"name": "threshold1", "value": "10"}, {"name": "threshold2", "value": "20"}]'
        recommendation:
          type: string
          description: Recommendations to address the issue, stored as a stringified JSON array of objects
          example: '[{"name": "Check inverter connections", "priority": "high"}]'
        updated_at:
          type: string
          format: date-time
          description: the updated timestamp when the config was updated
        updated_by:
          type: string
          description: the user that would be updating the config


    CarCodeConfigHistoryDAO:
      title: "CarCodeConfigHistoryDAO"
      description: Car Code Config History Schema
      type: object
      required:
        - car_code
        - site_object_id
        - old_value
        - new_value
        - transaction_type
        - updated_at
        - updated_by
      properties:
        car_code:
          type: string
          description: Primary key - label which was updated in the CAR_CODE_CONFIG table
        site_object_id:
          type: string
          description: Primary key - location id associated with the configuration
        old_value:
          type: string
          description: old value stored as JSON string
        new_value:
          type: string
          description: new value stored as JSON string
        transaction_type:
          type: string
          description: type of transaction ('create', 'update', 'delete')
          enum: ["create", "update", "delete"]
        updated_at:
          type: string
          format: date-time
          description: the updated timestamp when the config was updated
        updated_by:
          type: string
          description: the user that would be updating the config

    #################################
    # Car Code Alarm Config Schemas #
    #################################

    CreateCarCodeAlarmConfigDAORequest:
      title: "CreateCarCodeAlarmConfigDAORequest"
      description: Request wrapper for creating car code alarm configurations in batch
      type: object
      required:
        - requests
        - user_id
      properties:
        requests:
          type: array
          description: Array of car code alarm config objects to create
          items:
            $ref: '#/components/schemas/CarCodeAlarmConfigDAO'
        user_id:
          type: string
          description: "User ID for ACL check. Can be a user's email/UUID or the microservice secret key. If the microservice secret key is provided, it takes precedence over user authentication."
      example:
        requests:
          - car_code: "HSO"
            name: "High String Outage"
            category: "Inverter"
            device_type: "String Inverter"
            should_raise_alarm: true
            updated_at: "2023-05-15T10:30:00Z"
            updated_by: "admin"
          - car_code: "AMD"
            name: "Array Module Damage"
            category: "Panel"
            device_type: "Solar Panel"
            should_raise_alarm: true
            updated_at: "2023-05-15T10:30:00Z"
            updated_by: "admin"
        user_id: "<EMAIL>"

    CarCodeAlarmConfigsRes:
      title: "CarCodeAlarmConfigsRes"
      description: Car Code Alarm Config Response
      type: object
      oneOf:
        - type: object
          properties:
            success:
              type: object
              properties:
                status:
                  type: string
                  example: "success"
                code:
                  type: integer
                  example: 201
                message:
                  type: array
                  items:
                    $ref: '#/components/schemas/CarCodeAlarmConfigDAO'
          example:
            success:
              status: "success"
              code: 201
              message: [
                {
                  "car_code": "HSO",
                  "name": "High String Outage",
                  "category": "Inverter",
                  "device_type": "String Inverter",
                  "should_raise_alarm": true,
                  "updated_at": "2023-05-15T10:30:00Z",
                  "updated_by": "admin"
                }
              ]
        - type: object
          properties:
            error:
              type: object
              properties:
                status:
                  type: string
                  example: "ERROR: Bad Request - Invalid input data"
                code:
                  type: integer
                  example: 400
                message:
                  type: array
                  items:
                    type: object
                  example: []
                fields:
                  type: array
                  items:
                    type: string
                  example: ["car_code", "name", "category"]
                error:
                  type: string
                  example: "The request body is invalid or contains invalid fields."
          example:
            error:
              status: "ERROR: Bad Request - Invalid input data"
              code: 400
              message: []
              fields: ["car_code", "name", "category"]
              error: "The request body is invalid or contains invalid fields."

    CarCodeAlarmConfigDeleteRes:
      title: "CarCodeAlarmConfigDeleteRes"
      description: Car Code Alarm Config Delete Response
      type: object
      properties:
        status:
          type: string
          example: "success"
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Car code {car_code} deleted successfully"

    CarCodeAlarmConfigHistoryRes:
      title: "CarCodeAlarmConfigHistoryRes"
      description: Car Code Alarm Config History Response
      type: object
      properties:
        status:
          type: string
          example: "success"
        code:
          type: integer
          example: 200
        message:
          type: array
          items:
            $ref: '#/components/schemas/CarCodeAlarmConfigHistoryDAO'



    CarCodeAlarmConfigDAO:
      title: "CarCodeAlarmConfigDAO"
      description: Car Code Alarm Config Schema
      type: object
      required:
        - car_code
        - name
        - category
        - should_raise_alarm
        - updated_at
        - updated_by
      properties:
        car_code:
          type: string
          description: identifier for car_codes
        name:
          type: string
          description: label for the car_codes
        category:
          type: string
          description: store additional info regarding the categorisation of car_code
        device_type:
          type: string
          description: store additional info regarding the device_type assigned to car_code
        should_raise_alarm:
          type: boolean
          description: used to define whether to raise alarm
          example: false
        updated_at:
          type: string
          format: date-time
          description: the updated timestamp when the config was updated
        updated_by:
          type: string
          description: the user that would be updating the config

    CarCodeAlarmConfigHistoryDAO:
      title: "CarCodeAlarmConfigHistoryDAO"
      description: Car Code Alarm Config History Schema
      type: object
      required:
        - car_code
        - old_value
        - new_value
        - transaction_type
        - updated_at
        - updated_by
      properties:
        car_code:
          type: string
          description: Primary key - label which was updated in the CAR_CODE_alarm-config table
        old_value:
          type: string
          description: old value stored as JSON string
        new_value:
          type: string
          description: new value stored as JSON string
        transaction_type:
          type: string
          description: type of transaction ('create', 'update', 'delete')
          enum: ["create", "update", "delete"]
        updated_at:
          type: string
          format: date-time
          description: the updated timestamp when the config was updated
        updated_by:
          type: string
          description: the user that would be updating the config
