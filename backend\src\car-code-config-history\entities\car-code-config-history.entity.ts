import { Entity, Column, PrimaryColumn } from 'typeorm';
import { DB_TABLES } from '../../common/constants';

/**
 * Entity representing the history of changes to car code configurations
 * Tracks all create, update, and delete operations
 */
@Entity(DB_TABLES.CAR_CODE_CONFIG_HISTORY)
export class CarCodeConfigHistory {
  @PrimaryColumn({ type: 'varchar' })
  car_code: string;

  @PrimaryColumn({ type: 'varchar' })
  site_object_id: string;

  @Column({ type: 'text', nullable: false })
  old_value: string;

  @Column({ type: 'text', nullable: false })
  new_value: string;

  @Column({
    type: 'varchar',
    nullable: false,
    enum: ['create', 'update', 'delete'],
  })
  transaction_type: 'create' | 'update' | 'delete';

  @Column({ type: 'timestamp', nullable: false })
  updated_at: Date;

  @Column({ type: 'varchar', nullable: false })
  updated_by: string;
}
