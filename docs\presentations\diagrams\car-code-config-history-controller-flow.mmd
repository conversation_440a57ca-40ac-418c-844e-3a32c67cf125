sequenceDiagram
    title Car Code Config History Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeConfigHistoryController
    participant CarCodeConfigHistoryService
    participant CarCodeConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeConfigH<PERSON><PERSON><PERSON>ontroller,CarCodeConfigHistoryService: History controllers are read-only and provide access to audit logs
    Note over CarCodeConfigHistoryService: Provides access to historical records of all configuration changes
    
    %% Main Flow - Find History Records
    
    Client->>CarCodeConfigHistoryController: FindCarCodeConfigHistory(request)
    Note over CarCodeConfigHistoryController: Validate and normalize request parameters
    
    %% Normalize sort order
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: normalizeSortOrder(request.sort_order)
    
    %% Prepare query parameters
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: Prepare queryParams object
    Note over CarCodeConfigHistoryController: Extract car_code, site_object_id, start_date, end_date, sort_order, limit
    
    %% Call service method
    CarCodeConfigHistoryController->>CarCodeConfigHistoryService: findCarCodeConfigsHistory(queryParams)
    
    %% Build query conditions
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: Build where conditions
    Note over CarCodeConfigHistoryService: Apply car_code and site_object_id filters if provided
    
    %% Apply date filtering
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: applyDateFiltering(start_date, end_date)
    Note over CarCodeConfigHistoryService: Create date range filter if dates provided
    
    %% Execute query
    CarCodeConfigHistoryService->>CarCodeConfigHistoryRepository: find(where, order, take)
    CarCodeConfigHistoryRepository->>Database: SELECT * FROM CAR_CODE_CONFIG_HISTORY
    Note over Database: Filter by car_code, site_object_id, date range
    Note over Database: Order by updated_at
    Note over Database: Limit results
    
    %% Return results
    Database-->>CarCodeConfigHistoryRepository: History Records
    CarCodeConfigHistoryRepository-->>CarCodeConfigHistoryService: History Records
    
    %% Map to DAO objects
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: mapCarCodeConfigHistoryToDAO(historyRecords)
    Note over CarCodeConfigHistoryService: Convert database entities to DAO objects
    Note over CarCodeConfigHistoryService: Ensure updated_at is formatted as ISO string
    
    %% Return service response
    CarCodeConfigHistoryService-->>CarCodeConfigHistoryController: ServiceResponse(CarCodeConfigHistoryDAO array)
    
    %% Convert to controller response
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: Convert to CarCodeConfigHistoryResponse
    
    %% Return response to client
    CarCodeConfigHistoryController-->>Client: CarCodeConfigHistoryResponse
