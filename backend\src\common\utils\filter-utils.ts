/**
 * Filter utility functions for data manipulation and transformation
 */

import { SORT_ORDER } from '../constants';

/**
 * Normalizes a sort order string to a valid SORT_ORDER enum value
 *
 * @param sortOrder The sort order string to normalize
 * @param defaultSortOrder The default sort order to use if the provided sort order is invalid
 * @returns A valid SORT_ORDER enum value
 */
export function normalizeSortOrder(
  sortOrder: string | undefined,
  defaultSortOrder: SORT_ORDER = SORT_ORDER.DESC,
): SORT_ORDER {
  if (!sortOrder) {
    return defaultSortOrder;
  }

  // Convert to uppercase for case-insensitive comparison
  const upperSortOrder = sortOrder.toUpperCase();

  // Validate against valid enum values
  // Use Object.values to get all enum values for type-safe comparison
  const validSortOrders = Object.values(SORT_ORDER);
  if (validSortOrders.includes(upperSortOrder as SORT_ORDER)) {
    return upperSortOrder as SORT_ORDER;
  }

  return defaultSortOrder;
}

/**
 * Returns a new array containing only the first occurrence of each unique key, preserving order.
 * @param arr Array of objects to extract unique values from
 * @param keySelector Function to select the key to make unique
 * @returns Array of unique keys
 */
export function distinctBy<T, K>(arr: T[], keySelector: (item: T) => K): K[] {
  const seen = new Set<K>();
  const result: K[] = [];
  for (const item of arr) {
    const key = keySelector(item);
    if (!seen.has(key)) {
      seen.add(key);
      result.push(key);
    }
  }
  return result;
}
