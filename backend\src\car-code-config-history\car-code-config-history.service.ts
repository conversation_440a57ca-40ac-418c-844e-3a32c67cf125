import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, FindOptionsWhere } from 'typeorm';
import { CarCodeConfigHistory } from './entities';
import { CarCodeConfig } from '../car-code-config/entities';
import {
  TransactionType,
  CarCodeConfigHistoryDAO,
  CarCodeConfigHistoryQueryParams,
} from './interfaces';
import {
  ServiceResponse,
  applyDateFiltering,
} from '../common/utils/service-utils';
import {
  DEFAULT_QUERY_LIMITS,
  DEFAULT_SORT_SETTINGS,
} from '../common/constants';

/**
 * Type for config objects used in history records
 * - For create/update: Full CarCodeConfig entity
 * - For delete: Minimal object with required car_code, site_object_id and optional updated_by
 */
type ConfigForHistory =
  | CarCodeConfig
  | (Pick<CarCodeConfig, 'car_code' | 'site_object_id'> & {
      updated_by?: string;
    });

@Injectable()
export class CarCodeConfigHistoryService {
  private readonly logger = new Logger(CarCodeConfigHistoryService.name);

  constructor(
    @InjectRepository(CarCodeConfigHistory)
    private carCodeConfigHistoryRepository: Repository<CarCodeConfigHistory>,
  ) {}

  /**
   * Create history records for car code configs for any operation type
   * @param params Object containing all parameters
   * @param params.oldRecords Map of old values by car_code (for update/delete)
   * @param params.configs Array of config objects - for create/update these should be full entities, for delete minimal objects are sufficient
   * @param params.transactionType Type of transaction ('create', 'update', or 'delete')
   * @param params.entityManager Optional transaction manager
   * @returns Array of error messages (empty if all succeed)
   */
  async createNewHistory(params: {
    oldRecords: Record<string, string>;
    configs: ConfigForHistory[];
    transactionType: TransactionType;
    entityManager?: EntityManager;
  }): Promise<string[]> {
    const { oldRecords, configs, transactionType, entityManager } = params;
    const errors: string[] = [];
    const repo = entityManager
      ? entityManager.getRepository(CarCodeConfigHistory)
      : this.carCodeConfigHistoryRepository;

    for (const config of configs) {
      try {
        // Determine new value based on transaction type
        const newValue =
          transactionType !== 'delete'
            ? JSON.stringify({
                car_code: config.car_code,
                site_object_id: config.site_object_id,
                monitoring_window:
                  'monitoring_window' in config ? config.monitoring_window : 0,
                enabled: 'enabled' in config ? config.enabled : false,
                thresholds: 'thresholds' in config ? config.thresholds : {},
                recommendation:
                  'recommendation' in config ? config.recommendation : {},
              })
            : '{}';

        await repo.save({
          car_code: config.car_code,
          site_object_id: config.site_object_id,
          old_value: oldRecords[config.car_code] || '{}',
          new_value: newValue,
          transaction_type: transactionType,
          updated_at: new Date(),
          updated_by: config.updated_by || 'system',
        });
      } catch (err) {
        errors.push(
          `Failed to create audit record for car_code ${config.car_code}: ${err instanceof Error ? err.message : err}`,
        );
      }
    }
    return errors;
  }

  /**
   * Find car code config history records
   *
   * @param filters Query parameters for filtering history records
   * @returns ServiceResponse with history records or error details
   */
  async findCarCodeConfigsHistory(
    filters: CarCodeConfigHistoryQueryParams,
  ): Promise<ServiceResponse<CarCodeConfigHistoryDAO>> {
    try {
      const {
        car_code,
        site_object_id,
        start_date,
        end_date,
        sort_order,
        limit,
      } = filters;

      // Log query filters
      this.logger.log(
        `Finding car code config history with filters - limit: ${limit}, sort: ${sort_order}`,
      );

      this.logger.log(
        `Additional filters: ${JSON.stringify({ car_code, site_object_id, start_date, end_date })}`,
      );

      // Build query conditions
      const where: FindOptionsWhere<CarCodeConfigHistory> = {};

      // Apply basic filters
      if (car_code) where.car_code = car_code;
      if (site_object_id) where.site_object_id = site_object_id;

      // Handle date filtering using the utility function
      const [success, dateFilter, errorResponse] =
        applyDateFiltering<CarCodeConfigHistoryDAO>(
          start_date,
          end_date,
          this.logger,
        );
      if (!success)
        return errorResponse as ServiceResponse<CarCodeConfigHistoryDAO>;
      if (dateFilter) where.updated_at = dateFilter;

      // Execute query with sort order and limit
      const historyRecords = await this.carCodeConfigHistoryRepository.find({
        where,
        order: { updated_at: sort_order || DEFAULT_SORT_SETTINGS.HISTORY_LOGS },
        take: limit || DEFAULT_QUERY_LIMITS.HISTORY_LOGS, // Apply the limit to the query
      });
      this.logger.log(
        `Found ${historyRecords.length} car code config history records`,
      );
      const mappedHistoryRecords =
        this.mapCarCodeConfigHistoryToDAO(historyRecords);

      return {
        success: true,
        data: mappedHistoryRecords,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error finding car code config history: ${errorMessage}`,
      );

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Converts CarCodeConfigHistory entities to DAO objects (CarCodeConfigHistoryDAO[])
   * Ensures updated_at is always a string (ISO format)
   */
  private mapCarCodeConfigHistoryToDAO(
    historyRecords: CarCodeConfigHistory[],
  ): CarCodeConfigHistoryDAO[] {
    return historyRecords.map((record) => ({
      car_code: record.car_code,
      site_object_id: record.site_object_id,
      old_value: record.old_value,
      new_value: record.new_value,
      transaction_type: record.transaction_type,
      updated_at: record.updated_at.toISOString(),
      updated_by: record.updated_by,
    }));
  }
}
