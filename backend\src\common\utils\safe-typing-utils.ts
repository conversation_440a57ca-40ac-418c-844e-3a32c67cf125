/**
 * Utility functions for safely handling type conversions and data transformations
 */


/**
 * Safely converts a value to a string
 *
 * @param value The value to convert
 * @returns A string representation of the value
 */
export function safelyHandleString(value: unknown): string {
  if (value === null || value === undefined) {
    return '';
  }

  if (typeof value === 'string') {
    return value;
  }

  if (typeof value === 'number' || typeof value === 'boolean') {
    return String(value);
  }

  // For objects, arrays, etc., use JSON.stringify
  try {
    // This will handle objects, arrays, symbols, functions, etc.
    return JSON.stringify(value);
  } catch {
    // If JSON.stringify fails, return a type description instead
    return `[${typeof value}]`;
  }
}

/**
 * Safely converts a value to an optional string (string or undefined)
 *
 * @param value The value to convert
 * @returns A string representation of the value or undefined
 */
export function safelyHandleOptionalString(value: unknown): string | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }

  return safelyHandleString(value);
}

/**
 * Safely handles potentially complex objects by returning them as-is or undefined
 *
 * @param value The value to handle
 * @returns The original object or undefined
 */
export function safelyHandleObject(
  value: unknown,
): object | string | number | boolean | any[] | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }
  return value;
}
