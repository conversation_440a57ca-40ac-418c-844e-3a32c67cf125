# Design Documentation CarWrapperService

## 📜 Version History

| Version | Date                | Author     | Description of Changes                                                                                                                                                                                                                                                                   |
|---------|---------------------|------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1.0.6   | 2025-04-25 11:30:45 | sayyidkhan | Updated:<br> - Renamed `CAR_CODE_DEFINITION` to `CAR_CODE_ALARM_CONFIG` for better intuition in the database schema<br> - Renamed `CAR_CODE_DEFINITION_HISTORY` to `CAR_CODE_ALARM_CONFIG_HISTORY`<br> - Updated ER diagram and all references in documentation                          |
| 1.0.5   | 2025-04-22 08:30:00 | sayyidkhan | Added:<br> - DESIGN CONSTRAINTS section to document known limitations and future considerations<br> - Documented edge case handling for missing car_code configurations                                                                                                                  |
| 1.0.4   | 2025-04-22 07:15:00 | sayyidkhan | Updated:<br> - Added limit parameter to FindCarCases endpoint (default: 1000 records)<br> - Added sort_order parameter to FindCarCases and FindCarCasesByIds endpoints (default: DESC)<br> - Created constants file for application-wide settings including query limits and sort orders |
| 1.0.3   | 2025-04-21 14:45:00 | sayyidkhan | Updated:<br> - SQL queries for open/closed case detection<br> - Added explicit mapping to CarCaseConsoDAO<br> - Added status field to case queries<br> - Removed total_energy_loss aggregation                                                                                           |
| 1.0.2   | 2025-04-15 10:30:00 | sayyidkhan | Added:<br> - transaction_type column to CAR_CODE_CONFIG_HISTORY and CAR_CODE_ALARM_CONFIG_HISTORY tables (formerly CAR_CODE_DEFINITION_HISTORY)<br> - Updated ER diagram and table schemas                                                                                               |
| 1.0.1   | 2025-04-07 09:05:10 | sayyidkhan | Added:<br> - Version History<br> - Executive Summary<br> - Data Retention Policy<br> - Database Optimization                                                                                                                                                                             |
| 1.0.0   | 2025-04-04 09:00:00 | sayyidkhan | Initial documentation including:<br> - Problem Statement<br> - Business Requirements<br> - Architecture Design<br> - Database Schema<br> - Tech Stack Selection                                                                                                                          |


## Sitemap

- [Executive Summary](#executive-summary)
- [Design Constraints](#design-constraints)
    - [Known Limitations](#known-limitations)
    - [Future Considerations](#future-considerations)
- [Problem Statement](#problem-statement)
    - [Define Problem Statement](#define-problem-statement)
- [Business Requirements](#business-requirements)
    - [Breakdown of the Business Requirements](#breakdown-of-the-business-requirements)
- [Architecting](#architecting)
    - [Flow Diagram For Incoming Car Cases](#flow-diagram-for-incoming-car-cases)
    - [Application Sequence Diagrams](#application-sequence-diagrams)
    - [Semantic Tags & Grouping ID Design](#semantic-tags--grouping-id-design)
    - [Generation Technique for creation of Tag ID to distinctly identify car cases](#generation-technique-for-creation-of-tag-id-to-distinctly-identify-car-cases)
- [Database Solutioning](#database-solutioning)
    - [Database Schema Overview](#database-schema-overview)
    - [Data Retention Policy](#data-retention-policy)
    - [SQL Queries to Find Open and Close Cases](#sql-queries-to-find-open-and-close-cases)
    - [Case Study to Track Open And Close Cases](#case-study-to-track-open-and-close-cases)
- [Tech Stack](#tech-stack)
    - [Core Technologies](#core-technologies)
    - [Choice of Backend Stack](#choice-of-backend-stack)
        - [Framework Comparison](#framework-comparison)
        - [Selected Stack: NestJS](#selected-stack-nestjs)
    - [Choice of API Architecture](#choice-of-api-architecture)
        - [gRPC API](#grpc-api)
        - [REST API](#rest-api)
        - [API Design Considerations](#api-design-considerations)
            - [Protocol Independence](#protocol-independence)
            - [Access Control and Authorization](#access-control-and-authorization)
            - [Shared Logic Approach](#shared-logic-approach)
            - [Comprehensive Filtering and Sorting](#comprehensive-filtering-and-sorting)
        - [Controller Naming Convention (REST & gRPC)](#controller-naming-convention-rest--grpc)
- [Unit Testing](#unit-testing)
- [ORM Transaction Handling](#orm-transaction-handling)
        - [Persistence Logic Flow](#persistence-logic-flow)
        - [Configuration Lifecycle Persistence](#configuration-lifecycle-persistence)
        - [Key Transaction Principles](#key-transaction-principles)
- [Sprint Planning](#sprint-planning)

## Executive Summary

The CarWrapperService is a critical component designed to handle Corrective Action Reports (CAR) in sustainability farm environments. It manages incidents when services are down across different OS categories including solarOS, windOS, and thermalOS.

**Key Features:**
1. **Case Management**: Tracks and groups incidents using semantic tags and case IDs, following an append-only and read-only model to reduce redundancies and maintain a single source of truth.
2. **Auto-Normalization**: Implements configurable case normalization based on configurable monitoring windows
3. **Multi-OS Support**: Handles incidents across different sustainability systems (solarOS, windOS, thermalOS)
4. **gRPC API Architecture**: Utilizes gRPC for all service communications endpoints for high-throughput ingestion and backend integration.
5. **REST API Architecture**: is provided for querying car case and configuration data, specifically for frontend applications and dashboards.
6. **Comprehensive Filtering**: Implements robust filtering capabilities with configurable query parameters and sensible defaults (1000 record limit) to optimize data retrieval

**Business Impact:**
- Improved incident tracking and resolution
- Automated case normalization reducing manual overhead
- Enhanced visibility across different sustainability systems
- Streamlined integration capabilities through gRPC API

## Design Constraints
[Back to Top](#sitemap)

This section documents known limitations, edge cases, and future considerations for the CarWrapperService. These constraints have been identified during the design phase and represent areas that may require attention in future iterations.

### Known Limitations

#### Missing Car Code Configurations

**Issue Description 1:**
When a car case record exists in the database but there is no corresponding configuration in the CAR_CODE_CONFIG table for that car_code and site_object_id combination,
we are going to return status as empty string. currently we will not be able to retrieve status if the CAR_CODE_CONFIG table is removed.

**Impact:**
- Users won’t see any warning or message that a default value is being used.
- This can cause confusion and make it harder to understand or check why a certain status was applied.

### Future Considerations

#### Enhanced Configuration Validation

**Proposed Enhancement:**
Implement a validation layer that verifies car_code and site_id combinations exist in the configuration table when new car cases are created.

**Benefits:**
- Prevents cases from being created without proper configuration
- Ensures consistent application of business rules
- Improves data quality

#### Configuration Status Indicator

**Proposed Enhancement:**
Add a flag in the response to indicate when default configuration values are being used.

**Implementation Options:**
- Add an `is_using_default_config` boolean field to the CarCaseConsoDAO interface
- Modify SQL queries to detect when the default is being used
- Add logging when default configurations are applied

#### Administrative Reporting

**Proposed Enhancement:**
Create a simple admin endpoint to list cases using default configurations.

**Benefits:**
- Provides visibility into configuration issues
- Helps administrators identify and fix missing configurations
- Supports data quality initiatives

## Problem Statement
[Back to Top](#sitemap)

### Define Problem Statement

**Clarification:**

*   **CAR (Corrective Action Report):** Represents an incident in a sustainability farm, triggered when services are down.
*   **OS Categories:**  Refer to different types of sustainability assets within the farm. In this context, they include:
    *   solarOS
    *   windOS
    *   thermalOS
    > Note: we are using the source column to classify the os categories from the CAR_CASE table.

1.  Need to allow payload to capture more OS categories and grouping of similar car cases
    - I need the gRPC payload to be upgraded so that it can capture payload from the other sustainability category (OS).
    - I need to group and classify all same car case together
    - I need every car case that is coming into the microservice to track the normalization status
    - I need every new car case added into the system to raise an alarm

2.  Poor Case Auto-Normalization Trigger / Updates
    - The Current System relies on hardcoded timeframes (5 to 10 days) for auto-normalization making it inflexible to send alarms for specific case scenarios. If there are any updates that come from the existing car cases, we are not tracking the frequency (counter) of the car cases and refreshing to the updated car cases coming in.
    - Currently, there is no way for specific Car Cases to automatically raise alarm.

3.  Definition of Normalization
    - A case is normalized when a recurring issue no longer re-appearing within the monitoring window.
    - What are the conditions of normalization?
    - Issue recurrence stops – The issue that was previously recurring has not reappeared within the monitoring window.

    **Legend:**
    **Car Code** - Classification Identifiers
    unique code that classify specific car cases on their severity.

## Business Requirements
[Back to Top](#sitemap)

### Breakdown of the Business Requirements

**Clarification:**

1. **Normalization Logic:**
    - A car case is considered "normalized" (closed) when the issue that triggered it has not reoccurred for a duration longer than the `monitoring_window` defined in the `CAR_CODE_CONFIG` table. This window starts from the `logging_date` of the most recent event associated with the case.
    - If no new events are logged for a specific case within its monitoring window, the case is automatically closed.

2. **Case Creation & Tracking**

    - Every new issue is logged as a new case in car_cases & assign a unique tag data.
    - If a same issue occurs, another new case in the car_cases, and it will reuse the unique tag data if the metadata is coming from the same location.
    - No updates to existing cases—only new inserts.

3. **Expiry-Based Case Reset**

    - Cases expire based on the expiry_days in car_code_config.
    - Expiry is determined by the latest event date (created_at in car_cases).
    - If a new event occurs before expiry, it knew case is created with unique tag from its metadata.
    - If a new event occurs after expiry, another case entry is created and reuse the tag if the metadata is same.

4. **Open & Closed Case [Date] Handling**

    - Open Cases → Cases where events are not expired; to_days(today's date - case most recent date) < expiry_date config
    - Closed Cases → Cases where all events are expired; to_days(today's date – case most recent date) >= expiry date config

5. **Car Code Configuration**

    - Each case is linked to a car code (solar-site-11, water-site-24, thermal-site-23, gas-site-13, gas-site-14).
    - car code – Track different type of abnormally. (some car codes require longer expiry_date window)
    - car code – must be provided all the time, otherwise will reject. (so no edge cases where new cases added w/o car code)

## Architecting
[Back to Top](#sitemap)

### Flow Diagram For Incoming Car Cases

```mermaid
flowchart LR
    subgraph Car Case
        subgraph Diagram Flow for Incoming Car Cases
            A(Case Received) --> car_code_config[(car_code_config<br/>DB)] --> X{Car Code &<br/>Site ID config<br/>enabled?}
            X -- Yes --> C{existingCase ?<br/>& isRecent ?}
            X -- No --> Y((Case Not Logged))

            C -- No --> D{New Case<br/>Created with<br/>new tag &<br/>case_id}
            C -- Yes --> E{check<br/>latest date &<br/>case_id for<br/>incoming case}
            E --> F{New Case<br/>Created with<br/>existing tag &<br/>case_id}

            D --> N1[Raise Notification]
            N1 --> G[Raise Alarm]
            G --> I((Case Logged))

            F --> N2[Raise Notification]
            N2 --> car_code_alarm_config[(car_code_alarm_config<br/>DB)]
            car_code_alarm_config --> H{Car Code<br/>Match?}
            H -- Yes --> G2[Raise Alarm]
            G2 --> I
            H -- No --> I

            I --> Z[Case Process Completed]
            Y --> Z
        end
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px,color:#000
    style I fill:#f9f,stroke:#333,stroke-width:2px,color:#000
    style Y fill:#fbb,stroke:#333,stroke-width:2px,color:#000
    style Z fill:#cfc,stroke:#333,stroke-width:2px,color:#000
    style car_code_config fill:#ffeb3b,stroke:#333,stroke-width:2px,color:#000
    style car_code_alarm_config fill:#ffeb3b,stroke:#333,stroke-width:2px,color:#000
    style H width:50px,height:50px
    style C width:50px,height:50px
    style X width:60px,height:60px


```

### Application Sequence Diagrams
[Back to Top](#sitemap)

The following sequence diagrams provide a detailed view of control and data flow within the CarWrapperService application. These diagrams illustrate the interactions between controllers, services, repositories, and the database for various operations across different components of the system.

#### Car Case Controller Flow

This diagram illustrates the complete flow of operations for the Car Case Controller, including health check, finding car cases, finding car cases by IDs, and creating new car cases. It shows the separation of concerns between the `CarCaseService` for case creation and management and the `CarCaseConsoService` for consolidated views.

Key flows depicted:
- Ping/Health Check operation
- Finding car cases with filtering, sorting, and pagination
- Finding car cases by specific IDs
- Creating car cases with proper validation and config checks
- Case tagging and monitoring window logic during creation

```mermaid
sequenceDiagram
    title Car Case Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCaseController
    participant CarCaseConsoService
    participant CarCaseService
    participant EntityManager
    participant CarCaseRepository
    participant CarCodeConfigRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCaseService,CarCaseConsoService: Both services are independent and directly injected into the controller.
    Note over CarCaseConsoService: Handles consolidated views with direct database access.
    Note over CarCaseService: Handles car case creation and management.

    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCaseController: PingCarCase()
    CarCaseController-->>Client: PingResponse

    %% 2. Find Car Cases Flow
    Client->>CarCaseController: FindCarCases(request)
    Note over CarCaseController: Normalize request parameters
    CarCaseController->>CarCaseConsoService: findCarCases(request)
    CarCaseConsoService->>CarCaseConsoService: buildFindCarCasesQuery(filters, limit)
    CarCaseConsoService->>CarCaseConsoService: initializeBaseQuery()
    CarCaseConsoService->>CarCaseConsoService: applyFiltersToQuery(query, filters)
    CarCaseConsoService->>CarCaseConsoService: finalizeSortingAndLimit(query, limit, filters)
    CarCaseConsoService->>EntityManager: query(sql, parameters)
    EntityManager->>Database: Execute SQL Query
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    CarCaseController-->>Client: CarCasesResponse

    %% 3. Find Car Cases By IDs Flow
    Client->>CarCaseController: FindCarCasesByIds(request)
    Note over CarCaseController: Extract IDs and normalize sort order
    CarCaseController->>CarCaseConsoService: findCarCasesByIds(ids, sortOrder)
    CarCaseConsoService->>EntityManager: query(sql with placeholders, ids)
    EntityManager->>Database: Execute SQL Query with ID parameters
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    CarCaseController-->>Client: CarCasesResponse

    %% 4. Create Car Case Flow
    Client->>CarCaseController: CreateCarCase(request)
    Note over CarCaseController: Validate request parameters
    CarCaseController->>CarCaseService: createCarCase(requests)
    
    %% Car Case Service Processing
    CarCaseService->>CarCaseService: checkCarCodeConfig(carCaseDAOList)
    CarCaseService->>CarCodeConfigRepository: find({car_code, site_object_id})
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG
    Database-->>CarCodeConfigRepository: Car Code Configs
    CarCodeConfigRepository-->>CarCaseService: Car Code Configs
    
    %% Process Each Car Case
    loop For each car case
        CarCaseService->>CarCaseService: generateTags(carCase)
        CarCaseService->>CarCaseService: determineCase(tags, monitoringWindow, caseIdMap)
        
        %% Check for existing cases
        alt Check in-memory cache
            Note right of CarCaseService: In-memory cache prevents duplicate case IDs for the same incident
            Note right of CarCaseService: If multiple incidents with identical tags are processed in the same batch,
            Note right of CarCaseService: they will be grouped under the same case ID rather than creating separate cases
            CarCaseService->>CarCaseService: checkInMemoryCache(tags, inMemoryCache)
        else Check database
            CarCaseService->>CarCaseService: findMostRecentCaseByTags(tags)
            CarCaseService->>CarCaseRepository: findOne({tags}, {order})
            CarCaseRepository->>Database: SELECT * FROM CAR_CASES WHERE tags = ?
            Database-->>CarCaseRepository: Most Recent Case
            CarCaseRepository-->>CarCaseService: Most Recent Case
            CarCaseService->>CarCaseService: isWithinMonitoringWindow(date, window)
        end
        
        %% Save Car Case
        CarCaseService->>CarCaseService: saveCarCaseDAO(carCase, index, totalCount)
        CarCaseService->>CarCaseRepository: save(carCaseEntity)
        CarCaseRepository->>Database: INSERT INTO CAR_CASES
        Database-->>CarCaseRepository: Saved Entity
        CarCaseRepository-->>CarCaseService: Saved Entity with ID
    end
    
    CarCaseService-->>CarCaseController: ServiceResponse(CarCaseDAO array)
    
    %% Fetch Consolidated Data for Created Cases
    CarCaseController->>CarCaseConsoService: findCarCasesByIds(caseIds)
    CarCaseConsoService->>EntityManager: query(sql with placeholders, ids)
    EntityManager->>Database: Execute SQL Query
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    
    CarCaseController-->>Client: CarCasesResponse
```

#### Car Code Config Controller Flow

This diagram details the operations handled by the Car Code Config Controller, which manages the configuration settings for car codes tied to specific sites. It demonstrates the CRUD operations and the history tracking mechanism for audit purposes.

Key flows depicted:
- Finding car code configurations
- Creating new configurations with duplicate checking
- Updating existing configurations
- Deleting configurations
- Transaction handling for atomic operations
- History record creation for all modifications

```mermaid
sequenceDiagram
    title Car Code Config Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeConfigController
    participant CarCodeConfigService
    participant CarCodeConfigHistoryService
    participant EntityManager
    participant CarCodeConfigRepository
    participant CarCodeConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeConfigService,CarCodeConfigHistoryService: CarCodeConfigService handles CRUD operations while CarCodeConfigHistoryService maintains audit logs
    Note over CarCodeConfigHistoryService: Maintains audit trail of all configuration changes
    
    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCodeConfigController: PingCarCodeConfig()
    CarCodeConfigController-->>Client: PingResponse

    %% 2. Find Car Code Configs Flow
    Client->>CarCodeConfigController: FindCarCodeConfigs(request)
    Note over CarCodeConfigController: Validate request parameters
    CarCodeConfigController->>CarCodeConfigService: findCarCodeConfigs(request)
    CarCodeConfigService->>CarCodeConfigRepository: find(where, order, take)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG
    Database-->>CarCodeConfigRepository: Configurations
    CarCodeConfigRepository-->>CarCodeConfigService: Configurations
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO(configs)
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 3. Create Car Code Config Flow
    Client->>CarCodeConfigController: CreateCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: createCarCodeConfig(requests)
    
    %% Check for duplicates
    CarCodeConfigService->>CarCodeConfigRepository: find(uniqueKeys)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE (car_code, site_object_id) IN (...)
    Database-->>CarCodeConfigRepository: Existing Configurations
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configurations
    
    %% If no duplicates, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Save new configs
    loop For each config
        CarCodeConfigService->>EntityManager: save(CarCodeConfig)
        EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG
        Database-->>EntityManager: Saved Entity
    end
    
    %% Create history records
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    loop For each config
        CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
        CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
        EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
        Database-->>EntityManager: Saved History
    end
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO(configs)
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 4. Update Car Code Config Flow
    Client->>CarCodeConfigController: UpdateCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: updateCarCodeConfig(request)
    
    %% Find existing config
    CarCodeConfigService->>CarCodeConfigRepository: findOne(where)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE car_code = ? AND site_object_id = ?
    Database-->>CarCodeConfigRepository: Existing Configuration
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Save updated config
    CarCodeConfigService->>EntityManager: save(updatedConfig)
    EntityManager->>Database: UPDATE CAR_CODE_CONFIG
    Database-->>EntityManager: Updated Entity
    
    %% Create history record
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
    CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService->>CarCodeConfigService: mapCarCodeConfigToDAO([updatedConfig])
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(CarCodeConfigDAO array)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse

    %% 5. Delete Car Code Config Flow
    Client->>CarCodeConfigController: DeleteCarCodeConfig(request)
    Note over CarCodeConfigController: Validate required fields
    CarCodeConfigController->>CarCodeConfigService: deleteCarCodeConfig(car_code, site_object_id, updated_by)
    
    %% Find existing config
    CarCodeConfigService->>CarCodeConfigRepository: findOne(where)
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG WHERE car_code = ? AND site_object_id = ?
    Database-->>CarCodeConfigRepository: Existing Configuration
    CarCodeConfigRepository-->>CarCodeConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeConfigService: Begin transaction for atomic operations
    CarCodeConfigService->>EntityManager: transaction(callback)
    
    %% Remove config
    CarCodeConfigService->>EntityManager: remove(CarCodeConfig, existingConfig)
    EntityManager->>Database: DELETE FROM CAR_CODE_CONFIG
    Database-->>EntityManager: Deletion Result
    
    %% Create history record for audit
    Note over CarCodeConfigHistoryService: Store the deleted config details for audit trail
    EntityManager->>CarCodeConfigHistoryService: createNewHistory(params)
    CarCodeConfigHistoryService->>EntityManager: getRepository(CarCodeConfigHistory)
    CarCodeConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeConfigService: Transaction Result
    CarCodeConfigService-->>CarCodeConfigController: ServiceResponse(message: string)
    CarCodeConfigController-->>Client: CarCodeConfigsResponse
```

#### Car Code Config History Controller Flow

This diagram shows the read-only operations for accessing the audit history of car code configurations. It demonstrates how to query the historical records with various filters.

Key flows depicted:
- Finding history records with filtering by car_code, site_object_id, date range
- Sorting and pagination of results
- Data mapping between entity and DAO layers

```mermaid
sequenceDiagram
    title Car Code Config History Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeConfigHistoryController
    participant CarCodeConfigHistoryService
    participant CarCodeConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeConfigHistoryController,CarCodeConfigHistoryService: History controllers are read-only and provide access to audit logs
    Note over CarCodeConfigHistoryService: Provides access to historical records of all configuration changes
    
    %% Main Flow - Find History Records
    
    Client->>CarCodeConfigHistoryController: FindCarCodeConfigHistory(request)
    Note over CarCodeConfigHistoryController: Validate and normalize request parameters
    
    %% Normalize sort order
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: normalizeSortOrder(request.sort_order)
    
    %% Prepare query parameters
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: Prepare queryParams object
    Note over CarCodeConfigHistoryController: Extract car_code, site_object_id, start_date, end_date, sort_order, limit
    
    %% Call service method
    CarCodeConfigHistoryController->>CarCodeConfigHistoryService: findCarCodeConfigsHistory(queryParams)
    
    %% Build query conditions
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: Build where conditions
    Note over CarCodeConfigHistoryService: Apply car_code and site_object_id filters if provided
    
    %% Apply date filtering
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: applyDateFiltering(start_date, end_date)
    Note over CarCodeConfigHistoryService: Create date range filter if dates provided
    
    %% Execute query
    CarCodeConfigHistoryService->>CarCodeConfigHistoryRepository: find(where, order, take)
    CarCodeConfigHistoryRepository->>Database: SELECT * FROM CAR_CODE_CONFIG_HISTORY
    Note over Database: Filter by car_code, site_object_id, date range
    Note over Database: Order by updated_at
    Note over Database: Limit results
    
    %% Return results
    Database-->>CarCodeConfigHistoryRepository: History Records
    CarCodeConfigHistoryRepository-->>CarCodeConfigHistoryService: History Records
    
    %% Map to DAO objects
    CarCodeConfigHistoryService->>CarCodeConfigHistoryService: mapCarCodeConfigHistoryToDAO(historyRecords)
    Note over CarCodeConfigHistoryService: Convert database entities to DAO objects
    Note over CarCodeConfigHistoryService: Ensure updated_at is formatted as ISO string
    
    %% Return service response
    CarCodeConfigHistoryService-->>CarCodeConfigHistoryController: ServiceResponse(CarCodeConfigHistoryDAO array)
    
    %% Convert to controller response
    CarCodeConfigHistoryController->>CarCodeConfigHistoryController: Convert to CarCodeConfigHistoryResponse
    
    %% Return response to client
    CarCodeConfigHistoryController-->>Client: CarCodeConfigHistoryResponse
```

#### Car Code Alarm Config Controller Flow

This diagram illustrates the operations for managing alarm configurations tied to car codes. It shows how alarm settings are created, updated, and deleted, along with the history tracking mechanism.

Key flows depicted:
- Finding alarm configurations
- Creating new alarm configurations
- Updating existing alarm configurations
- Deleting configurations
- Transaction handling for atomic operations
- History record creation for all modifications

```mermaid
sequenceDiagram
    title Car Code Alarm Config Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeAlarmConfigController
    participant CarCodeAlarmConfigService
    participant CarCodeAlarmConfigHistoryService
    participant EntityManager
    participant CarCodeAlarmConfigRepository
    participant CarCodeAlarmConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeAlarmConfigService,CarCodeAlarmConfigHistoryService: CarCodeAlarmConfigService handles CRUD operations while CarCodeAlarmConfigHistoryService maintains audit logs
    Note over CarCodeAlarmConfigHistoryService: Maintains audit trail of all configuration changes with old and new values
    
    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCodeAlarmConfigController: PingCarCodeAlarmConfig()
    CarCodeAlarmConfigController-->>Client: PingResponse

    %% 2. Find Car Code Alarm Configs Flow
    Client->>CarCodeAlarmConfigController: FindCarCodeAlarmConfigs(request)
    Note over CarCodeAlarmConfigController: Validate request parameters
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: findCarCodeAlarmConfigs(request)
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: find(where, order, take)
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG
    Database-->>CarCodeAlarmConfigRepository: Configurations
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Configurations
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO(configs)
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 3. Create Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: CreateCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, name, should_raise_alarm, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: createCarCodeAlarmConfig(requests)
    
    %% Check for duplicates
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: find(where: { car_code: In(uniqueKeys) })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code IN (...)
    Database-->>CarCodeAlarmConfigRepository: Existing Configurations
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configurations
    
    %% If no duplicates, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Save new configs
    loop For each config
        CarCodeAlarmConfigService->>EntityManager: save(CarCodeAlarmConfig)
        EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG
        Database-->>EntityManager: Saved Entity
    end
    
    %% Create history records
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    loop For each config
        CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
        CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
        EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
        Database-->>EntityManager: Saved History
    end
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO(configs)
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 4. Update Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: UpdateCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: updateCarCodeAlarmConfig(request)
    
    %% Find existing config
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: findOne(where: { car_code })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code = ?
    Database-->>CarCodeAlarmConfigRepository: Existing Configuration
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Save updated config
    CarCodeAlarmConfigService->>EntityManager: save(updatedConfig)
    EntityManager->>Database: UPDATE CAR_CODE_ALARM_CONFIG
    Database-->>EntityManager: Updated Entity
    
    %% Create history record
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
    CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService->>CarCodeAlarmConfigService: mapCarCodeAlarmConfigsToDAO([updatedConfig])
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(CarCodeAlarmConfigDAO array)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse

    %% 5. Delete Car Code Alarm Config Flow
    Client->>CarCodeAlarmConfigController: DeleteCarCodeAlarmConfig(request)
    Note over CarCodeAlarmConfigController: Validate required fields (car_code, updated_by)
    CarCodeAlarmConfigController->>CarCodeAlarmConfigService: deleteCarCodeAlarmConfig(car_code, updated_by)
    
    %% Find existing config
    CarCodeAlarmConfigService->>CarCodeAlarmConfigRepository: findOne(where: { car_code })
    CarCodeAlarmConfigRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG WHERE car_code = ?
    Database-->>CarCodeAlarmConfigRepository: Existing Configuration
    CarCodeAlarmConfigRepository-->>CarCodeAlarmConfigService: Existing Configuration
    
    %% If exists, start transaction
    Note over CarCodeAlarmConfigService: Begin transaction for atomic operations
    CarCodeAlarmConfigService->>EntityManager: transaction(callback)
    
    %% Remove config
    CarCodeAlarmConfigService->>EntityManager: remove(CarCodeAlarmConfig, existingConfig)
    EntityManager->>Database: DELETE FROM CAR_CODE_ALARM_CONFIG
    Database-->>EntityManager: Deletion Result
    
    %% Create history record for audit
    Note over CarCodeAlarmConfigHistoryService: Store the deleted config details for audit trail
    EntityManager->>CarCodeAlarmConfigHistoryService: createNewHistory(params)
    CarCodeAlarmConfigHistoryService->>EntityManager: getRepository(CarCodeAlarmConfigHistory)
    CarCodeAlarmConfigHistoryService->>EntityManager: save(historyRecord)
    EntityManager->>Database: INSERT INTO CAR_CODE_ALARM_CONFIG_HISTORY
    Database-->>EntityManager: Saved History
    
    EntityManager-->>CarCodeAlarmConfigService: Transaction Result
    CarCodeAlarmConfigService-->>CarCodeAlarmConfigController: ServiceResponse(message: string)
    CarCodeAlarmConfigController-->>Client: CarCodeAlarmConfigsResponse
```

#### Car Code Alarm Config History Controller Flow

This diagram demonstrates the read-only operations for accessing the audit history of alarm configurations. It shows the filtering and retrieval mechanisms for historical records.

Key flows depicted:
- Finding history records with filtering by car_code, date range
- Sorting and pagination of results
- Data mapping between entity and DAO layers

```mermaid
sequenceDiagram
    title Car Code Alarm Config History Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeAlarmConfigHistoryController
    participant CarCodeAlarmConfigHistoryService
    participant CarCodeAlarmConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeAlarmConfigHistoryController,CarCodeAlarmConfigHistoryService: History controllers are read-only and provide access to audit logs
    Note over CarCodeAlarmConfigHistoryService: Provides access to historical records of all configuration changes
    
    %% Main Flow - Find History Records
    
    Client->>CarCodeAlarmConfigHistoryController: FindCarCodeAlarmConfigHistory(request)
    Note over CarCodeAlarmConfigHistoryController: Validate and normalize request parameters
    
    %% Normalize sort order
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: normalizeSortOrder(request.sort_order)
    
    %% Prepare query parameters
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: Prepare queryParams object
    Note over CarCodeAlarmConfigHistoryController: Extract car_code, start_date, end_date, sort_order, limit
    
    %% Call service method
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryService: findCarCodeAlarmConfigsHistory(queryParams)
    
    %% Build query conditions
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: Build where conditions
    Note over CarCodeAlarmConfigHistoryService: Apply car_code filter if provided
    
    %% Apply date filtering
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: applyDateFiltering(start_date, end_date)
    Note over CarCodeAlarmConfigHistoryService: Create date range filter if dates provided
    
    %% Execute query
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryRepository: find(where, order, take)
    CarCodeAlarmConfigHistoryRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG_HISTORY
    Note over Database: Filter by car_code, date range
    Note over Database: Order by updated_at
    Note over Database: Limit results
    
    %% Return results
    Database-->>CarCodeAlarmConfigHistoryRepository: History Records
    CarCodeAlarmConfigHistoryRepository-->>CarCodeAlarmConfigHistoryService: History Records
    
    %% Map to DAO objects
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: mapCarCodeAlarmConfigHistoryToDAO(historyRecords)
    Note over CarCodeAlarmConfigHistoryService: Convert database entities to DAO objects
    Note over CarCodeAlarmConfigHistoryService: Ensure updated_at is formatted as ISO string
    
    %% Return service response
    CarCodeAlarmConfigHistoryService-->>CarCodeAlarmConfigHistoryController: ServiceResponse(CarCodeAlarmConfigHistoryDAO array)
    
    %% Convert to controller response
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: Convert to CarCodeAlarmConfigHistoryResponse
    
    %% Return response to client
    CarCodeAlarmConfigHistoryController-->>Client: CarCodeAlarmConfigHistoryResponse
```

### Semantic Tags & Grouping ID Design

> Use tags with Case ID to group related cases instead of parent-child hierarchy

- **easy to query:** (eg; "show all cases with label x")
- **flexibility:** We can introduce multiple grouping(s) / categories on top of existing tags

**Semantic tags:**
- we can establish better understanding just by looking at within tags.
- use metadata such as site_id, device_name, location to create the tags.
- Note: tag needs to be dynamic, able to configure. Either from payload need to send or we configure to create the tags.
- There will be situation that some of the metadata is not available.

**Grouping ID Generation:**

*   **New Case**: When a completely new incident occurs (i.e., an incident with a unique combination of `car_code`, `site_object_id`, `sub_site_object_id`, and `dev_object_id` that hasn't been seen before), a new tag is generated (e.g., `car_code_1-site_object_1-sub_site_object_id_1-dev_object_id_1`), and a unique `case_id` is assigned to this initial case.

*   **Recurring Case (Within Monitoring Window)**: If the same type of case (identified by the same tag) occurs again and is considered "recent" (i.e., within the `monitoring_window` defined in `CAR_CODE_CONFIG`), it will be associated with the *existing* case, reusing the same `case_id`. This groups consecutive occurrences of the same issue under a single `case_id`.

*   **Recurring Case (Expired/Outside Monitoring Window)**: If the same type of case reoccurs but is *not* considered "recent" (i.e., it's outside the `monitoring_window`), then a *new* `case_id` will be created for this new occurrence, even though it has the same tag. This indicates a new incident sequence, separate from the previous one.

**Case ID** is added to help group consecutive cases together, if after the monitoring_window has passed, then a new case_id is created.
Otherwise, will reuse existing case_id.

#### Case Grouping Process Flow

```mermaid
flowchart LR
    Incident1("Incident 1: Day 0") --> Extract1("Extract Tags")
    Incident2("Incident 2: Day 2") --> Extract2("Extract Tags")
    Incident3("Incident 3: Day 10") --> Extract3("Extract Tags")

    Extract1 --> Check1("Check monitoring window: 7 days")
    Extract2 --> Check2("Check monitoring window: 7 days")
    Extract3 --> Check3("Check monitoring window: 7 days")

    Check1 --> NewCase("Create New Case: CASE-site_1-hso-0")

    Check2 --> Window1{"Within window?"}
    Window1 -->|Yes| ReuseCase("Reuse Case: CASE-site_1-hso-0")

    Check3 --> Window2{"Within window?"}
    Window2 -->|No| NewCase2("Create New Case: CASE-site_1-hso-1")

    NewCase --> DB[(Database)]
    ReuseCase --> DB
    NewCase2 --> DB

    DB --> OpenCases("Open Cases: NOW() - last_occurrence < window")
    DB --> ClosedCases("Closed Cases: NOW() - last_occurrence >= window")
```

The diagram above illustrates how incidents with the same tags are processed based on the monitoring window:
1. The first incident creates a new case
2. The second incident (Day 2) falls within the monitoring window (7 days) and reuses the existing case ID
3. The third incident (Day 10) falls outside the monitoring window and creates a new case ID
4. Case status (open/close) is determined at query time based on the most recent occurrence and monitoring window

#### Logging Date & Time Sample

| Date & Time     | ID | Tags              | Grouping ID | Notes                                                                  |
|-----------------|----|-------------------|-------------|------------------------------------------------------------------------|
| 1/3/2024 9:00   | 1  | car_code_1-site_1 | GRP_1       | Root cause investigation started.                                      |
| 5/3/2024 14:30  | 2  | car_code_1-site_1 | GRP_1       | Same issue resurfaces. Linked to SOLAR-100 via timestamp order.        |
| 12/3/2024 11:15 | 3  | car_code_1-site_1 | GRP_1       | Issue reoccurs again. Timestamp shows it is part of the same sequence. |
| 19/3/2024 11:15 | 4  | car_code_1-site_1 | GRP_2       | Issue reoccurring again, new entry created with GRP_2.                 |

## Generation Technique for creation of Tag ID to distinctly identify car cases

```mermaid
graph LR
subgraph "Must Provide"
    A[car_code]
    B[site_object_id]
end

subgraph "At least one of these values must be provided"
    C[sub_site_object_id]
    D[dev_object_id]
end

style A fill:#ADD8E6,stroke:#333,stroke-width:2px
style A fill:#ADD8E6,stroke:#333,stroke-width:2px,color:#000
style B fill:#ADD8E6,stroke:#333,stroke-width:2px,color:#000
style C fill:#FFDAB9,stroke:#333,stroke-width:2px,color:#000
style D fill:#FFDAB9,stroke:#333,stroke-width:2px,color:#000
```

| Example                                                                         |
|---------------------------------------------------------------------------------|
| Eg. [car_code_id_1]-[site_object_id_1]-[sub_site_object_id_1]-[dev_object_id_1] |
| Eg 1. car_code_1-site_object_1-sub_site_object_id_1-dev_object_id_1             |
| Eg 2. car_code_1-site_object_1-0-0                                              |

#### Rationale for the generation technique:

Tags are generated by concatenating `car_code`, `site_object_id`, `sub_site_object_id`, and `dev_object_id` using hyphens as delimiters.
For example, if `sub_site_object_id` and `dev_object_id` are missing, the tag would be `car_code_1-site_object_1-0-0`.

Car Code is always required so we will use that a starter tag

We will follow it up with the asset id’s because Car Code can be used for multiple asset id,so it is not distinct enough.

At least 1 one of these of value must be present, otherwise we will reject the car-case.

If there is no value present for that variable, we will default to 0.

## Database Solutioning
[Back to Top](#sitemap)

### Database Schema Overview

> DATABASE NAME: `carwrapper`

#### Available Tables

##### Entity Relation Diagram (ER Diagram)

```mermaid
erDiagram
    CAR_CASES {
        number id PK
        string site_object_id
        string sub_site_object_id
        string device_id
        string additional_device_info
        string tags
        string case_id
        string car_code
        string source
        string title
        string description
        string remarks
        json recommendation
        jsonb metadata
        Timestamp logging_date
    }
    CAR_CODE_CONFIG {
        string site_object_id PK
        string car_code PK
        number monitoring_window
        boolean enabled
        json thresholds
        json recommendation
        timestamp updated_at
        string updated_by
    }
    CAR_CODE_ALARM_CONFIG {
        string car_code PK
        string name
        string description
        string category
        string device_type
        boolean should_raise_alarm
        timestamp updated_at
        string updated_by
    }
    CAR_CODE_ALARM_CONFIG_HISTORY {
        string car_code PK
        string old_value
        string new_value
        string transaction_type
        timestamp updated_at
        string updated_by
    }
    CAR_CODE_CONFIG_HISTORY {
        string car_code PK
        string site_object_id PK
        string old_value
        string new_value
        string transaction_type
        timestamp updated_at
        string updated_by
    }

    CAR_CASES ||--o{ CAR_CODE_CONFIG : "has"
    CAR_CASES ||--o{ CAR_CODE_ALARM_CONFIG : "uses"
    CAR_CODE_ALARM_CONFIG ||--o{ CAR_CODE_ALARM_CONFIG_HISTORY : "has"
    CAR_CODE_CONFIG ||--o{ CAR_CODE_CONFIG_HISTORY : "has"
```

#### Available Tables

| Column                        | Description                                                          |
|-------------------------------|----------------------------------------------------------------------|
| CAR_CASES                     | to store each car case entry                                         |
| CAR_CODE_CONFIG               | to store config for monitoring_window for car_cases                  |
| CAR_CODE_ALARM_CONFIG         | to store config to raise alarms & store car code definition metadata |
| CAR_CODE_ALARM_CONFIG_HISTORY | to store the config changes made in the CAR_CODE_ALARM_CONFIG        |
| CAR_CODE_CONFIG_HISTORY       | to store the config changes made in the CAR_CODE_CONFIG              |

#### History Tables Data Retention Policy

**Important Note on History Records:**
- History records in `CAR_CODE_ALARM_CONFIG_HISTORY` and `CAR_CODE_CONFIG_HISTORY` tables are preserved even when the parent records in `CAR_CODE_ALARM_CONFIG` or `CAR_CODE_CONFIG` are deleted.
- This ensures a complete audit trail is maintained regardless of changes to the current configuration.
- The relationship between history tables and their parent tables is designed with `onDelete: 'SET NULL'` to maintain historical data integrity.
- When querying history records for deleted configurations, the application should handle null references appropriately.
- Both history tables include a `transaction_type` column with values 'create', 'update', or 'delete' to track the type of change made.
- This prevents audit bypassing where someone could delete and recreate a record to avoid proper change tracking.
- The design of storing complete JSON representations in `old_value` and `new_value` fields makes the history tables resilient to schema changes in the parent tables. If new fields are added to `CAR_CODE_CONFIG` or `CAR_CODE_ALARM_CONFIG`, the history tables can continue to store the complete state without requiring structural changes.


#### Available Tables Schema

###### CAR_CASES Table

| Column                 | Type      | Required | Description                                                            |
|------------------------|-----------|----------|------------------------------------------------------------------------|
| id                     | number    | Required | Uniquely identifies each car case entry                                |
| site_object_id         | string    | Required | location id                                                            |
| sub_site_object_id     | string    | Optional | segment within the location id                                         |
| device_id              | string    | Optional | actual device                                                          |
| additional_device_info | string    | optional | additional device info if applicable                                   |
| tags                   | string    | Required | Uniquely identifies the repeated cases                                 |
| case_id                | string    | Required | Identifier used to group consecutive car case entries                  |
| car_code               | string    | Required | Used to identify if expiry/normalisation window                        |
| source                 | string    | Required | Correctly identify which OS Category this data is from eg. solarOS     |
| title                  | string    | Required | title for case case, if title not available, auto creation is required |
| description            | string    | Optional | description for car case                                               |
| remarks                | string    | Optional | additional remarks might be provided                                   |
| recommendation         | json      | Optional | if they have recommendation to address the issue, user can provide     |
| metadata               | jsonb     | Optional | Store data related the car cases not related to grouping of cases      |
| logging_date           | Timestamp | Required | Logging time of the car case                                           |


###### CAR_CODE_ALARM_CONFIG

| Column             | Type      | Required | Description                                                          |
|--------------------|-----------|----------|----------------------------------------------------------------------|
| car_code           | string    | Required | identifier for car_codes                                             |
| name               | string    | Required | label for the car_codes                                              |
| description        | string    | Optional | provide additional info on the purpose of having this car code       |
| category           | string    | Optional | store additional info regarding the categorisation of car_code       |
| device_type        | string    | Optional | store additional info regarding the device_type assigned to car_code |
| should_raise_alarm | boolean   | Required | used to define whether to raise alarm                                |
| updated_at         | timestamp | Required | the updated timestamp when the config was updated                    |
| updated_by         | string    | Required | the user that would be updating the config                           |

###### CAR_CODE_ALARM_CONFIG_HISTORY Table

| Column           | Type      | Required | Description                                                              |
|------------------|-----------|----------|--------------------------------------------------------------------------|
| car_code         | string    | Required | Primary key - label which was updated in the CAR_CODE_ALARM_CONFIG table |
| old_value        | string    | Required | old value stored as JSON string                                          |
| new_value        | string    | Required | new value stored as JSON string                                          |
| transaction_type | string    | Required | type of transaction ('create', 'update', 'delete')                       |
| updated_at       | timestamp | Required | the updated timestamp when the config was updated                        |
| updated_by       | string    | Required | the user that would be updating the config                               |


###### CAR_CODE_CONFIG Table

| Column            | Type      | Required | Description                                                        |
|-------------------|-----------|----------|--------------------------------------------------------------------|
| site_object_id    | string    | Required | location id                                                        |
| car_code          | string    | Required | label used to identify expiry/normalisation window                 |
| monitoring_window | number    | Required | number of days to monitor the car case from its logging window     |
| enabled           | boolean   | Required | enable / disable new incoming car cases to be accepted             |
| thresholds        | json      | Optional | threshold level for the case triggering                            |
| recommendation    | json      | Optional | if they have recommendation to address the issue, user can provide |
| updated_at        | timestamp | Required | the updated timestamp when the config was updated                  |
| updated_by        | string    | Required | the user that would be updating the config                         |


###### CAR_CODE_CONFIG_HISTORY Table

| Column                | Type      | Required | Description                                                            |
|-----------------------|-----------|----------|------------------------------------------------------------------------|
| car_code              | string    | Required | Primary key - label which was updated in the CAR_CODE_CONFIG table     |
| site_object_id        | string    | Required | Primary key - location id associated with the configuration            |
| old_value             | string    | Required | old value stored as JSON string                                        |
| new_value             | string    | Required | new value stored as JSON string                                        |
| transaction_type      | string    | Required | type of transaction ('create', 'update', 'delete')                     |
| updated_at            | timestamp | Required | the updated timestamp when the config was updated                      |
| updated_by            | string    | Required | the user that would be updating the config                             |



**Glossary:**

- tags: uses to identify each specific case or issue
- case_id: uses to aggregate or group consecutive occurrences of that case into a single incident

> normalisation will be based on the logging date.

#### Audit Fields in Configuration Tables

Both `car_code_config` and `car_code_alarm_config` tables now include the following audit fields:
- `updated_at` (TIMESTAMP): The last time the row was modified.
- `updated_by` (VARCHAR): The user or process that made the last modification.

These fields are mandatory and must be set on every update to ensure traceability and compliance with auditing requirements.

### Data Retention Policy

#### Car Cases Data Retention

1. **Active Car Cases**
    - Retention Period: Full data retention while monitoring_window is active
    - Storage Location: Primary PostgresSQL database
    - Access Level: Full read/write access
    - Monitoring: Real-time metrics and alerts active

2. **Recently Closed Car Cases**
    - Retention Period: 6 months from last logging_date
    - Storage Location: Primary PostgresSQL database
    - Access Level: Read-only access
    - Note: Maintains quick access for trend analysis and reoccurrence detection

3. **Historical Car Cases**
    - Retention Period: 2 years from last logging_date
    - Storage Location: TimescaleDB hyper tables
    - Access Level: Read-only with authorized access
    - Compression: Automated compression after 3 months
    - Note: Optimized for analytical queries

#### Configuration Data Retention

1. **CAR_CODE_CONFIG Changes**
    - Retention Period: Indefinite for current configurations
    - History Retention: 1 year in CAR_CODE_CONFIG_HISTORY
    - Audit Trail: All changes logged with updated_by and updated_at
    - Version Control: Complete configuration history maintained

2. **CAR_CODE_ALARM_CONFIG Changes**
    - Retention Period: Indefinite for current alarm configurations
    - History Retention: 1 year in CAR_CODE_ALARM_CONFIG_HISTORY
    - Change Tracking: Full audit trail of alarm configuration changes
    - Documentation: Mandatory change documentation

#### Compliance & Auditing

1. **Data Access Logs**
    - Retention Period: 1 year
    - Tracked Information:
        - Car Cases
        - Configuration changes
        - Data modifications

### SQL Queries to Find Open and Close Cases

> **Note:** As per discussion with stakeholder Eng Yeow, we are not doing any aggregation of the `total_energy_loss` anymore. That will be handled by another microservice.
>
> The results of these queries should be mapped to the `CarCaseConsoDAO` schema defined in the API documentation. This DAO will be used as the response payload for the FindCarCases and FindCarCaseById endpoints.

#### Open/Closed Case Detection Logic

```mermaid
graph LR
    A[Check Monitoring Window] -->|Is NULL| B[Status = '']
    A -->|Has Value| C[Compare Dates]
    C -->|Recent < Window| D[Status = 'open']
    C -->|Recent >= Window| E[Status = 'close']
```

The diagram above illustrates the logic for determining case status:
1. If monitoring window is NULL, status is empty string
2. If NOW() - most_recent_occurrence < monitoring_window, status is 'open'
3. If NOW() - most_recent_occurrence >= monitoring_window, status is 'close'

**EXPECTED OPEN CASES: **

```sql
/***
Find Open Cases
Condition: number of day(s) between today's date and case's most recent date is less than the monitoring window
defined in the CAR_CODE_CONFIG table.
***/

WITH latest_group AS (
    SELECT
        case_id,
        tags,
        MAX(logging_date) AS most_recent_occurrence,              -- Most recent occurrence
        MIN(logging_date) as first_occurrence,                    -- Track when issue first appeared
        COUNT(*) as occurrence_count,                             -- Count of occurrences
        array_agg(id) as related_ids                              -- Collect all related IDs for the same case_id
    FROM
        car_cases
    GROUP BY
        case_id, tags
)
SELECT
    cc.id,                                                      -- Primary key
    lg.related_ids as conso_id,                                 -- Include related IDs for CarCaseConsoDAO
    cc.site_object_id,                                          -- Site identifier
    cc.sub_site_object_id,                                      -- Sub-site identifier
    cc.device_id,                                               -- Device identifier
    cc.tags,                                                    -- Tags for grouping
    cc.case_id,                                                 -- Case identifier
    cc.car_code,                                                -- Car code
    cc.source,                                                  -- Source system
    cc.title,                                                   -- Case title
    cc.description,                                             -- Case description
    cc.recommendation,                                          -- Recommendation object
    cc.metadata,                                                -- Metadata object
    cc.logging_date,                                            -- Original logging date
    lg.occurrence_count,                                        -- Include occurrence count
    lg.first_occurrence,                                        -- When the issue first appeared
    lg.most_recent_occurrence,                                  -- Most recent occurrence
    'open' as status                                            -- Status field (open/close)
FROM
    car_cases cc
        JOIN
    car_code_config ccc
    ON cc.car_code = ccc.car_code AND cc.site_object_id = ccc.site_object_id
        JOIN
    latest_group lg
    ON cc.case_id = lg.case_id
        AND cc.tags = lg.tags
WHERE
    NOW()::DATE - lg.most_recent_occurrence::date < ccc.monitoring_window
ORDER BY
    cc.logging_date;
```

**EXPECTED CLOSED CASES: **

```sql
/***
Find Closed Cases
Condition: number of day(s) between today's date and case's most recent date is greater than or equal to the monitoring window
defined in the CAR_CODE_CONFIG table.
***/

WITH latest_group AS (
    SELECT
        case_id,
        tags,
        MAX(logging_date) AS most_recent_occurrence,              -- Most recent occurrence
        MIN(logging_date) as first_occurrence,                    -- Track when issue first appeared
        COUNT(*) as occurrence_count,                             -- Count of occurrences
        array_agg(id) as related_ids                              -- Collect all related IDs for the same case_id
    FROM
        car_cases
    GROUP BY
        case_id, tags
)
SELECT
    cc.id,                                                      -- Primary key
    lg.related_ids as conso_id,                                 -- Include related IDs for CarCaseConsoDAO
    cc.site_object_id,                                          -- Site identifier
    cc.sub_site_object_id,                                      -- Sub-site identifier
    cc.device_id,                                               -- Device identifier
    cc.tags,                                                    -- Tags for grouping
    cc.case_id,                                                 -- Case identifier
    cc.car_code,                                                -- Car code
    cc.source,                                                  -- Source system
    cc.title,                                                   -- Case title
    cc.description,                                             -- Case description
    cc.recommendation,                                          -- Recommendation object
    cc.metadata,                                                -- Metadata object
    cc.logging_date,                                            -- Original logging date
    lg.occurrence_count,                                        -- Include occurrence count
    lg.first_occurrence,                                        -- When the issue first appeared
    lg.most_recent_occurrence,                                  -- Most recent occurrence
    'close' as status                                           -- Status field (open/close)
FROM
    car_cases cc
        JOIN
    car_code_config ccc
    ON cc.car_code = ccc.car_code AND cc.site_object_id = ccc.site_object_id
        JOIN
    latest_group lg
    ON cc.case_id = lg.case_id
        AND cc.tags = lg.tags
WHERE
    NOW()::DATE - lg.most_recent_occurrence::date >= ccc.monitoring_window
ORDER BY
    lg.most_recent_occurrence DESC;                                        -- Most recent closed cases first
```

### Case Study to Track Open And Close Cases

Case Study 1: `Close Case Only`
Today's Date: `2024-04-12`

**CAR CODE CONFIG:**

| id | car_code | monitoring_window |
|----|----------|-------------------|
| 1  | CC001    | 5                 |

**CAR CASE TABLE:**

| id | site_object_id | sub_site_object_id | dev_object_id | car_code | tags                | grouping_id | os_category | logging_date     |
|----|----------------|--------------------|---------------|----------|---------------------|-------------|-------------|------------------|
| 1  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-03-29T09:00 |
| 2  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-03-30T09:00 |
| 3  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-04-01T11:00 |
| 4  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-04-11T11:00 |

**EXPECTED OPEN CASES:**

Open cases → Days Since Logging < monitoring_window (5 days)

| id | site_object_id | sub_site_object_id | dev_object_id | car_code | tags                | grouping_id | os_category | logging_date     |
|----|----------------|--------------------|---------------|----------|---------------------|-------------|-------------|------------------|
| 4  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-04-11T11:00 |

**EXPECTED CLOSED CASES:**

Closed cases → Days Since Logging >= monitoring_window (5 days)

| id | site_object_id | sub_site_object_id | dev_object_id | car_code | tags                | grouping_id | os_category | logging_date     |
|----|----------------|--------------------|---------------|----------|---------------------|-------------|-------------|------------------|
| 1  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-03-29T09:00 |
| 2  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-03-30T09:00 |
| 3  | SO_1           | SSO_1              | D1            | CC001    | CC001-SO_1-SSO_1-D1 | 1           | solarOS     | 2024-04-01T11:00 |

**Final Verification**

- ✅ If today’s date is 2024-04-12,
- ✅ Only Case 4 is Open (1 day < 5 days).
- ✅ Cases 1, 2, 3 are Closed (11+ days ≥ 5 days).

---

Case Study 2: `Consecutive Days`
Today's Date: `2024-04-12`

**CAR CODE CONFIG:**

| id | car_code | monitoring_window |
|----|----------|-------------------|
| 1  | CC002    | 7                 |

**CAR CASE TABLE:**

| id | site_object_id | sub_site_object_id | dev_object_id | car_code | tags                | grouping_id | os_category | logging_date     |
|----|----------------|--------------------|---------------|----------|---------------------|-------------|-------------|------------------|
| 1  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-03T10:00 |
| 2  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-07T10:00 |
| 3  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-10T14:00 |

**EXPECTED OPEN CASES:**

Open cases → Days Since Logging < monitoring_window (7 days)

| id | site_object_id | sub_site_object_id | dev_object_id | car_code | tags                | grouping_id | os_category | logging_date     |
|----|----------------|--------------------|---------------|----------|---------------------|-------------|-------------|------------------|
| 1  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-03T10:00 |
| 2  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-07T10:00 |
| 3  | SO_2           | SSO_2              | D2            | CC002    | CC002-SO_2-SSO_2-D2 | 1           | solarOS     | 2024-04-10T14:00 |

**EXPECTED CLOSED CASES:**

Closed cases → Days Since Logging >= monitoring_window (7 days)

*None in this case, as all are within the monitoring window.*

**Final Verification:**

- ✅ If today’s date is **2024-04-12**,
- ✅ **All three cases** are **Open** as they are consecutive and within the **7-day monitoring window**.

## Tech Stack
[Back to Top](#sitemap)

### Core Technologies

| Technology    | Purpose                                                              | Version       |
|---------------|----------------------------------------------------------------------|---------------|
| Node.js       | Runtime environment                                                  | Latest LTS    |
| TypeScript    | Programming language for type safety and better developer experience | Latest Stable |
| Docker/Podman | Container runtime for application packaging and deployment           | Latest Stable |

#### Technology Stack Diagram

```mermaid
flowchart TB
    subgraph "Frontend"
        Client[Client Applications]
    end

    subgraph "API Layer"
        GRPC[gRPC Service]
    end

    subgraph "Application Layer"
        NestJS[NestJS Framework]
        Controllers[Controllers]
        Services[Services]
        Repositories[Repositories]
    end

    subgraph "Data Layer"
        TypeORM[TypeORM]
        PostgreSQL[(PostgreSQL)]
    end

    subgraph "DevOps"
        Docker[Docker/Podman]
        Jest[Jest Testing]
    end

    Client --> GRPC
    GRPC --> Controllers
    Controllers --> Services
    Services --> Repositories
    Repositories --> TypeORM
    TypeORM --> PostgreSQL
    NestJS --- Controllers
    NestJS --- Services
    NestJS --- Repositories
    Docker --- NestJS
    Jest --- Services
    Jest --- Controllers
```

**Programming Environment Details:**
```yaml name=tech-stack.yml
Development:
  Language: TypeScript
  Runtime: Node.js
  Framework: NestJS
  HTTP Server: Express.js/Fastify
  Code Quality:
    - ESLint
    - Prettier
  Version Control: Git

Build Tools:
  - npm/yarn
  - tsc (TypeScript Compiler)
  - webpack

Containerization:
  Platform:
    - Docker
    - Podman
  Configuration:
    - Dockerfile
    - docker-compose.yml
    - .dockerignore
  Registry:
    - Docker Hub
    - Container Registry
```

### Choice of Backend Stack

#### Framework Comparison

| Feature            | NestJS                             | Fastify                       |
|--------------------|------------------------------------|-------------------------------|
| Architecture       | Modular, with dependency injection | Lightweight, plugin-based     |
| TypeScript Support | Native, TypeScript-first           | Supported via plugins         |
| Performance        | Good for complex applications      | Optimized for high throughput |
| ORM Integration    | Built-in support (TypeORM, Prisma) | Manual integration required   |
| Microservices      | Native support for gRPC, Kafka     | Plugin-based support          |
| Learning Curve     | Steeper, but structured            | Gentle, minimalistic          |

#### Selected Stack: NestJS

**Decision Rationale:**

After evaluating both frameworks, we chose NestJS for the following reasons:

1. **ORM Integration**
    - Native support for TypeORM and Prisma
    - Type-safe database operations
    - Built-in migration support

2. **Microservices Architecture**
    - First-class gRPC support
    - Built-in microservices module
    - Strong typing for API contracts

3. **Code Organization**
    - Modular architecture by default
    - Clear separation of concerns
    - Decorator-based structure
    - Enterprise-ready patterns

#### NestJS Architectural Pattern

```mermaid
flowchart LR
    Client("Client Request") --> Controller("Controller
    Handles gRPC requests")

    Controller --> Service("Service
    Implements business logic")

    Service --> Repository("Repository
    Abstracts database operations")

    Repository --> Entity("Entity
    Defines data structure")

    Entity -.-> Database[(Database)]
```

**Component Responsibilities:**
- **Controller**: Handles gRPC requests and routes them to appropriate services
- **Service**: Implements business logic and orchestrates operations
- **Repository**: Abstracts database operations and data access
- **Entity**: Defines data structure and relationships

This choice aligns with our needs for maintaining a scalable and maintainable codebase while leveraging modern development practices.

### Choice of API Architecture

This service implements a gRPC-only API architecture approach:

#### gRPC API
- **Used for:** All service communication, both internal and external
- **Benefits:**
    - High performance binary communication
    - Strong typing with Protocol Buffers
    - Bi-directional streaming capabilities
    - Excellent for service-to-service communication
    - Built-in load balancing and health checking
    - Contract-first development approach

```protobuf name=car-wrapper.proto
syntax = "proto3";

package carwrapper;

service CarWrapperService {
  rpc CreateCarCase (CarCaseRequest) returns (CarCaseResponse) {}
  rpc GetCarCase (GetCarCaseRequest) returns (CarCaseResponse) {}
  // Additional RPC methods...
}
```

#### REST API: Read-Only Exposure

For frontend and dashboard integration, the CarWrapperService exposes only a **read-only REST API**. Specifically, only the `findQuery` (e.g., `FindCarCases` and `FindCarCasesByIds`) endpoints are available via REST for querying car case and configuration data. No endpoints are provided for creating, updating, or deleting records through the REST interface.

> **Rationale:**
> - The REST API is intended strictly for data retrieval and analytics purposes.
> - All data mutations (create, update, delete) are handled exclusively via internal gRPC APIs for security and integrity.
> - This approach reduces the attack surface and enforces a clear separation of responsibilities between internal (gRPC) and external (REST) API layers.

**Summary:**
- Only `findQuery` endpoints are exposed via REST.
- REST API is strictly read-only.
- All write operations are restricted to gRPC/internal interfaces.

#### API Design Considerations

##### Protocol Independence
- gRPC and REST implementations are fully decoupled at the controller level
- Each protocol evolves independently - protobuf changes don't require REST interface updates
- Prevents cascading changes across protocol boundaries
- Enables protocol-specific optimizations

##### Access Control and Authorization
- All API controllers use the AclGuard for authorization control:
  ```typescript
  @UseGuards(AclGuard)
  @UseFilters(AclExceptionFilter)
  ```
- The AclGuard ensures proper authorization checks are performed before request processing
- AclExceptionFilter handles unauthorized access attempts with appropriate error responses
- Authentication uses microserviceSecretKey for secure service-to-service communication:
  ```typescript
  microserviceSecretKey: process.env.MICROSERVICE_SECRET_KEY || 'aCHW7ds8xn',
  ```

##### Shared Logic Approach
- Common business logic resides exclusively in service layer
- Helper functions encapsulate cross-protocol functionality
- Controllers remain lightweight with protocol-specific:
  - Request/response handling
  - Error formatting  
  - Validation rules

##### Comprehensive Filtering and Sorting

To optimize data retrieval and ensure optimal performance, all list/search endpoints implement comprehensive filtering capabilities with configurable parameters and sorting options:

**Query Limits:**
- **Default Limit**: 1000 records
- **Implementation**: The limit is stored in a centralized constants file (`src/common/constants.ts`) for easy maintenance
- **Affected Endpoints**:
  - `FindCarCases` - Returns car cases based on filter criteria

**Sort Order:**
- **Default Sort Order**: DESC (most recent first)
- **Available Options**: ASC (oldest first) or DESC (most recent first)
- **Implementation**: The sort order is stored in a centralized constants file (`src/common/constants.ts`) for easy maintenance
- **Affected Endpoints**:
  - `FindCarCases` - Returns car cases based on filter criteria
  - `FindCarCasesByIds` - Returns car cases based on specific IDs

```typescript
// Example from src/common/constants.ts
export const DEFAULT_QUERY_LIMITS = {
  CAR_CASES: 1000,
};

export enum SORT_ORDER {
  ASC = 'ASC',
  DESC = 'DESC'
}

export const DEFAULT_SORT_SETTINGS = {
  CAR_CASES: SORT_ORDER.DESC,
};
```

This approach provides several benefits:
1. **Performance Protection**: Prevents unintentionally large queries that could impact system performance
2. **Consistent Behavior**: Standardizes query behavior across all endpoints
3. **Centralized Configuration**: Makes it easy to adjust limits globally if needed
4. **Client Control**: Allows clients to override the default when necessary by explicitly specifying a limit

**API Architecture Decision Matrix:**

| Aspect      | gRPC (Internal)            | REST (External)          |
|-------------|----------------------------|--------------------------|
| Performance | High (binary)              | Medium (JSON)            |
| Use Case    | Microservice Communication | External API Integration |
| Schema      | Strict (Proto3)            | Flexible (OpenAPI)       |
| Streaming   | Bi-directional             | Webhook-based            |

### Controller Implementation Standards

#### File Structure
```plaintext
src/
  car-case/
    car-case.controller.ts       # gRPC endpoints
    car-case.controller.rest.ts       # REST endpoints  
    car-case.service.ts               # Shared business logic
    __tests__/
      car-case.controller.spec.ts
      car-case.controller.rest.spec.ts
      car-case.service.spec.ts
```

#### Key Principles
1. **Separation of Concerns**
   - gRPC controllers handle protobuf serialization
   - REST controllers manage HTTP semantics
   - Services contain pure business logic

2. **Testing Benefits**  
   - Isolated protocol tests
   - Clear failure boundaries
   - Mockable service layer

3. **Evolution Safety**
   - gRPC changes won't break REST clients
   - REST updates won't affect gRPC performance

4. **Error Handling**: Detailed error responses are returned to the client to facilitate troubleshooting.

### Unit Testing

To ensure the reliability and robustness of the CarWrapperService, we will employ a comprehensive testing strategy that includes unit tests and integration tests.

*   **Unit Tests (Jest):** We will use Jest to test individual components, functions, and classes in isolation. This will help ensure that each part of our application is working correctly.

*   **gRPC Integration Tests (Jest):** We will use Jest with the `@grpc/grpc-js` library to test the gRPC API. This will involve calling the gRPC methods and asserting the responses.

### ORM Transaction Handling

The CarWrapperService implements a robust transaction management system to ensure data integrity, especially for configuration changes that require audit logging. This section describes the transaction flow and rollback mechanisms.

#### Persistence Logic Flow

The following diagram illustrates the transaction flow for configuration operations:

```mermaid
flowchart LR
    A[Start Transaction] --> B[Pre-load Records to Save]
    B --> C{Records Valid?}
    C -->|No| D[Rollback Transaction]
    C -->|Yes| E[Pre-load Records for Audit Log]
    E --> F{Audit Records Valid?}
    F -->|No| D
    F -->|Yes| G[Save Main Records]
    G --> H{Save Successful?}
    H -->|No| D
    H -->|Yes| I[Save Audit Log Records]
    I --> J{Audit Log Successful?}
    J -->|No| D
    J -->|Yes| K[Commit Transaction]
    D --> L[Return Error Response]
    K --> M[Return Success Response]

    style A fill:#d4f1f9,stroke:#000,stroke-width:2px,color:#000
    style B fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style C fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style D fill:#ffcccc,stroke:#000,stroke-width:2px,color:#000
    style E fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style F fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style G fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style H fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style I fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style J fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style K fill:#d5f5d5,stroke:#000,stroke-width:2px,color:#000
    style L fill:#ffcccc,stroke:#000,stroke-width:2px,color:#000
    style M fill:#d5f5d5,stroke:#000,stroke-width:2px,color:#000
```

#### Configuration Lifecycle Persistence

The following diagram shows the complete lifecycle of configuration operations (create, update, delete) and how they persist to the database:

```mermaid
flowchart LR
    %% Common elements
    Start[Client Request] --> Validate{Validate Input}
    Validate -->|Invalid| Error[Return Error]
    Validate -->|Valid| Transaction[Begin Transaction]

    %% Main flow
    Transaction --> PreLoad[Pre-load Data]
    PreLoad --> PreAudit[Pre-load Audit Data]
    PreAudit --> Operation{Operation Type}

    %% Create path
    Operation -->|Create| Create[Save New Config]

    %% Update path
    Operation -->|Update| Fetch[Fetch Existing Config]
    Fetch --> Update[Save Updated Config]

    %% Delete path
    Operation -->|Delete| FetchDel[Fetch Existing Config]
    FetchDel --> Delete[Delete Config]

    %% Common ending
    Create --> SaveAudit[Save Audit History]
    Update --> SaveAudit
    Delete --> SaveAudit
    SaveAudit --> AuditCheck{Audit Success?}
    AuditCheck -->|No| Rollback[Rollback Transaction]
    AuditCheck -->|Yes| Commit[Commit Transaction]
    Rollback --> Error
    Commit --> Success[Return Success]

    %% Direct styling for each node with darker text
    style Start fill:#d4f1f9,stroke:#000,stroke-width:2px,color:#000
    style Validate fill:#e1bee7,stroke:#000,stroke-width:2px,color:#000
    style Error fill:#ffcccc,stroke:#000,stroke-width:2px,color:#000
    style Transaction fill:#fff9c4,stroke:#000,stroke-width:2px,color:#000
    style PreLoad fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style PreAudit fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style Operation fill:#bbdefb,stroke:#000,stroke-width:2px,color:#000
    style Create fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style Fetch fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style Update fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style FetchDel fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style Delete fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style SaveAudit fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style AuditCheck fill:#ffffff,stroke:#000,stroke-width:2px,color:#000
    style Rollback fill:#ffcccc,stroke:#000,stroke-width:2px,color:#000
    style Commit fill:#d5f5d5,stroke:#000,stroke-width:2px,color:#000
    style Success fill:#d5f5d5,stroke:#000,stroke-width:2px,color:#000

    %% Make link text darker
    linkStyle default stroke:#000,stroke-width:1.5px,color:#000
```

#### Key Transaction Principles

1. **Atomic Operations**: All database operations for both main records and audit logs are wrapped in a single transaction to ensure atomicity.

2. **Pre-validation**: Records are validated before any database operations to minimize the chance of transaction failures.

3. **Complete Rollback**: If any part of the transaction fails (including audit logging), all changes are rolled back to maintain data integrity.

4. **Audit Logging**: Every create, update, and delete operation is logged with both old and new values for complete traceability.

5. **Error Handling**: Detailed error responses are returned to the client to facilitate troubleshooting.

This transaction handling approach applies specifically to the `car-code-config` and `car-code-alarm-config` operations, ensuring that configuration changes are always properly tracked and can be rolled back if necessary.

[Back to Top](#sitemap)

## Sprint Planning
[Back to Top](#sitemap)

We follow a 2-week sprint cycle for development. The planning process includes:

### Sprint Structure
- Starting with folder `02` (combining sprints 1 & 2)
- Each sprint runs for 2 weeks (10 working days)
- Sprint planning breaks down features into manageable activities
- Detailed sprint structure documented in `SPRINT_PLANNING.md`

### File Organization

Sprint-related files and documentation follow this standard structure:

```
docs/
├── planning/
│   ├── DESIGN_DOCS.md             # High-level design documentation
│   ├── API_DOCS.yaml              # API specifications
│   ├── 02/                        # Sprint folder (Sprint 1+2)
│   │   ├── SPRINT_PLANNING.md     # Sprint planning document
│   │   ├── DAILY_PROGRESS.md      # Daily updates and task tracking
│   └── 03/
├── DESIGN_DOCS.md # project context file
```

Each sprint folder contains its own planning documents and related resources, maintaining separation between sprints while following a consistent structure.

### Sprint Planning Template Structure

1. **Header**
   * Sprint number and project name
   * Epic description of main goal
2. **Features and User Stories**
   * Features broken down by sprint
   * User stories for each feature
   * Tasks for each user story
3. **Task Table**
   * Table organized by sprint
   * Implementation type, user story, task, duration, dates, and status
4. **Holiday Tracking**
   * List of observed holidays affecting the sprint
5. **Gantt Chart**
   * Week-by-week visual representation of tasks
   * Legend for interpreting the chart
   * Timeline breakdown with date ranges
6. **Next Steps**
   * Brief description of immediate actions
7. **Backlog**
   * Features deferred to future sprints with reasons
8. **Risks and Challenges**
   * Potential obstacles to project success
9. **Success Metrics**
   * Key indicators to measure project performance
10. **Definition of Done**
    * Clear criteria for task completion

### Example

```markdown
# Sprint 3 - CarWrapperService

Epic: CarWrapperService: Multi-OS Support and Performance Optimization

## Features and User Stories

### Sprint 3 Features

- Feature: Implement Multi-OS Support
  - User Story: Process and Normalize Incoming Car Cases from Multiple OS Sources
    - Task: Develop unified data model for different OS formats
    - Task: Implement adapters for each OS format
    - Task: Create integration tests for multi-OS support

- Feature: Performance Optimization
  - User Story: Optimize Database Queries for Car Case Processing
    - Task: Identify and resolve slow queries
    - Task: Implement database indexes
    - Task: Add query caching mechanisms

### Sprint 4 Features

- Feature: Advanced Reporting
  - User Story: Generate Statistical Reports on Car Cases
    - Task: Design reporting data structures
    - Task: Implement reporting API endpoints

---

## Task Table

### Sprint 3

| Type of Impl | User Story                                     | Task                                    | Duration (Days) | Affected Dates     | Status      |
|--------------|------------------------------------------------|-----------------------------------------|-----------------|-------------------|-------------|
| Feature      | Process Car Cases from Multiple OS Sources      | Develop unified data model              | 3 day(s)        | 12-05-2025 - 14-05-2025 | Not Started |
| Feature      | Process Car Cases from Multiple OS Sources      | Implement adapters for each OS format   | 4 day(s)        | 15-05-2025 - 20-05-2025 | Not Started |
| Feature Test | Process Car Cases from Multiple OS Sources      | Create integration tests               | 2 day(s)        | 21-05-2025 - 22-05-2025 | Not Started |
| Performance  | Optimize Database Queries for Car Case Processing | Identify and resolve slow queries     | 2 day(s)        | 12-05-2025 - 13-05-2025 | Not Started |
| Performance  | Optimize Database Queries for Car Case Processing | Implement database indexes            | 1 day(s)        | 14-05-2025           | Not Started |
| Performance  | Optimize Database Queries for Car Case Processing | Add query caching mechanisms          | 2 day(s)        | 15-05-2025 - 16-05-2025 | Not Started |

### Sprint 4

| Type of Impl | User Story                             | Task                             | Duration (Days) | Affected Dates     | Status      |
|--------------|-----------------------------------------|----------------------------------|-----------------|-------------------|-------------|
| Feature      | Generate Statistical Reports on Car Cases | Design reporting data structures | 3 day(s)        | 26-05-2025 - 28-05-2025 | Not Started |
| Feature      | Generate Statistical Reports on Car Cases | Implement reporting API endpoints | 4 day(s)        | 29-05-2025 - 03-06-2025 | Not Started |

Observed Holidays:
- 19-05-2025 - Company-wide Training Day

## Gantt Chart (Week View)

| User Story                                     | Task                                  | Week 1               | Week 2               | Week 3 | Week 4 | Status      |
|------------------------------------------------|---------------------------------------|----------------------|----------------------|--------|--------|-------------|
| Process Car Cases from Multiple OS Sources      | Develop unified data model            | ████████████         |                      |        |        | Not Started |
| Process Car Cases from Multiple OS Sources      | Implement adapters for each OS format |                      | ████████████████    |        |        | Not Started |
| Process Car Cases from Multiple OS Sources      | Create integration tests              |                      | ████████            |        |        | Not Started |
| Optimize Database Queries for Car Case Processing | Identify and resolve slow queries   | ████████            |                      |        |        | Not Started |
| Optimize Database Queries for Car Case Processing | Implement database indexes          | ████                |                      |        |        | Not Started |
| Optimize Database Queries for Car Case Processing | Add query caching mechanisms        |                      | ████████            |        |        | Not Started |

**Legend:**
- 2 characters (██) = 0.5 day
- 4 characters (████) = 1 day
- X used to represent Holiday
- █ used to represent manhours

**Breakdown of Timelines:**
- Week 1: 2025-05-12 to 2025-05-16
- Week 2: 2025-05-19 to 2025-05-23
- Week 3: 2025-05-26 to 2025-05-30
- Week 4: 2025-06-02 to 2025-06-06

**Next Steps:** Begin implementation of the unified data model for multi-OS support, paralleled with database optimization tasks.

## Backlog

- Feature: AI-Based Anomaly Detection
  - User Story: Automatically detect anomalies in Car Case patterns
    - Task: Implement machine learning models for pattern recognition
Reason: Requires additional research and specialized skills. Planned for future sprints after core functionality is stable.

## Risks and Challenges

* Different OS data formats may have incompatible fields
* Database optimization might introduce regression bugs
* Holiday and training day may impact sprint velocity

## Success Metrics

* 95% successful processing rate for Car Cases from all OS sources
* 40% reduction in database query execution time
* Zero regression bugs in existing functionality

## Definition of Done (DoD)

* All unit and integration tests pass
* Performance benchmarks meet targets
* Code review completed
* Documentation updated
```

### Sprint Planning Template

- Feature: [Feature Name]
  - User Story: [User Story Description]
    - Task: [Specific task]
    - Task: [Specific task]

---

## Task Table

### Sprint [Number]

| Type of Impl | User Story               | Task                     | Duration (Days) | Affected Dates | Status      |
|--------------|--------------------------|--------------------------|-----------------|----------------|-------------|
| [Type]       | [User Story Description] | [Task Description]       | [X] day(s)      | [Date Range]   | [Status]    |
| [Type]       | [User Story Description] | [Task Description]       | [X] day(s)      | [Date Range]   | [Status]    |

### Sprint [Number+1]

| Type of Impl | User Story               | Task                     | Duration (Days) | Affected Dates | Status      |
|--------------|--------------------------|--------------------------|-----------------|----------------|-------------|
| [Type]       | [User Story Description] | [Task Description]       | [X] day(s)      | [Date Range]   | [Status]    |
| [Type]       | [User Story Description] | [Task Description]       | [X] day(s)      | [Date Range]   | [Status]    |

Observed Holidays:
- [Date] - [Holiday Name]

## Gantt Chart (Week View)

| User Story                     | Task                     | Week 1 | Week 2 | Week 3 | Week 4 | Status      |
|--------------------------------|--------------------------|--------|--------|--------|--------|-------------|
| [User Story Description]       | [Task Description]       | ████   |        |        |        | [Status]    |
| [User Story Description]       | [Task Description]       |        | ████████|        |        | [Status]    |
| [User Story Description]       | [Task Description]       |        |        | ████   |        | [Status]    |

**Legend:**
- 2 characters (██) = 0.5 day
- 4 characters (████) = 1 day
- X used to represent Holiday
- █ used to represent manhours

**Breakdown of Timelines:**
- Week 1: [YYYY-MM-DD] to [YYYY-MM-DD]
- Week 2: [YYYY-MM-DD] to [YYYY-MM-DD]
- Week 3: [YYYY-MM-DD] to [YYYY-MM-DD]
- Week 4: [YYYY-MM-DD] to [YYYY-MM-DD]

**Next Steps:** [Brief description of next steps]

## Backlog

- Feature: [Feature Name]
  - User Story: [User Story Description]
    - Task: [Specific task]
Reason: [Explanation of why this feature is in the backlog]

## Risks and Challenges

* [Risk/Challenge 1]
* [Risk/Challenge 2]
* [Risk/Challenge 3]

## Success Metrics

* [Metric 1]
* [Metric 2]
* [Metric 3]

## Definition of Done (DoD)

* [Requirement 1]
* [Requirement 2]
* [Requirement 3]
```

### Daily Progress Tracking

- `DAILY_PROGRESS.md` file tracks daily work status and complements `SPRINT_PLANNING.md`
- Located in each sprint's folder (e.g., `docs/planning/02/DAILY_PROGRESS.md`)
- Updated daily to maintain team synchronization
- Provides a historical record of sprint activities and decisions

#### DAILY_PROGRESS.md Structure

1. **Header and Introduction**
   * Document title with project name
   * Brief explanation of document purpose
   * Relationship to other planning documents

2. **Table of Contents**
   * Links to all major sections
   * Quick navigation to daily entries by date

3. **Current Sprint Overview**
   * Table format summary of sprint tasks
   * Columns: Type, Task, Status, Assigned To, Due Date
   * Quick reference for overall sprint progress

4. **Daily Updates Section**
   * Reverse chronological order (newest first)
   * Each date as a separate subsection with H3 heading (###)
   * Standardized format for all daily entries:
     ```markdown
     ### YYYY-MM-DD
     
     #### Completed
     - Task 1 with implementation details
     - Task 2 with implementation details
       - Sub-point with specific technical details
       - Sub-point with component-specific information
     
     #### In Progress
     - Current task with status and next steps
     - Planned task for completion today/tomorrow
     
     #### Notes
     - Important decisions or findings from the day
     - Technical challenges and solutions
     - Reference links or dependencies
     ```

5. **Summary Sections**
   * Completed Tasks - comprehensive list across the sprint
   * In Progress Tasks - active work with status
   * Upcoming Tasks - planned work on the horizon
   * Blockers and Issues - tracking impediments

This structured approach ensures that daily progress is tracked consistently and provides a clear historical record of sprint activities, technical decisions, and implementation details that complement the higher-level planning in the SPRINT_PLANNING.md document.