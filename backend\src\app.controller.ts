import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { PingResponse } from './interfaces';

@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor() {}

  @GrpcMethod('CarWrapperService', 'Ping')
  ping(): PingResponse {
    this.logger.log('Received ping request');
    return { message: 'Car Wrapper Service is running' };
  }
}
