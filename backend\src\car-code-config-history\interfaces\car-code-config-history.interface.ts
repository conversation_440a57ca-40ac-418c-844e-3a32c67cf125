/**
 * Interfaces for the Car Code Config History
 */

/**
 * Transaction types for history records
 */
export type TransactionType = 'create' | 'update' | 'delete';

/**
 * Car code configuration history data
 */
export interface CarCodeConfigHistoryDAO {
  car_code: string;
  site_object_id: string;
  old_value: string;
  new_value: string;
  transaction_type: TransactionType;
  updated_at: string; // ISO 8601 format
  updated_by: string;
}

/**
 * Query parameters for filtering car code config history
 */
export interface CarCodeConfigHistoryQueryParams {
  car_code?: string;
  site_object_id?: string;
  start_date?: string; // ISO 8601 format
  end_date?: string; // ISO 8601 format
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  limit?: number; // Maximum number of records to return
}

/**
 * Response for car code configuration history queries
 */
export interface CarCodeConfigHistoryResponse {
  success?: {
    status: string;
    code: number;
    message: CarCodeConfigHistoryDAO[];
  };
  error?: {
    status: string;
    code: number;
    message: any[];
    fields: string[];
    error: string;
  };
}
