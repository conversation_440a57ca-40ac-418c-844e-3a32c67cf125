/**
 * Interfaces for the Car Code Alarm Config History
 */

/**
 * Transaction types for history records
 */
export type TransactionType = 'create' | 'update' | 'delete';

/**
 * Car code alarm config history data
 */
export interface CarCodeAlarmConfigHistoryDAO {
  car_code: string;
  old_value: string;
  new_value: string;
  transaction_type: TransactionType;
  updated_at: string; // ISO 8601 format
  updated_by: string;
}

/**
 * Query parameters for filtering car code alarm config history
 */
export interface CarCodeAlarmConfigHistoryQueryParams {
  car_code?: string;
  start_date?: string; // ISO 8601 format
  end_date?: string; // ISO 8601 format
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  limit?: number; // Maximum number of records to return
}

/**
 * Response for car code alarm config history queries
 */
export interface CarCodeAlarmConfigHistoryResponse {
  success?: {
    status: string;
    code: number;
    message: CarCodeAlarmConfigHistoryDAO[];
  };
  error?: {
    status: string;
    code: number;
    message: any[];
    fields: string[];
    error: string;
  };
}
