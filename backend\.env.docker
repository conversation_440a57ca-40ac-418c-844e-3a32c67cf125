# .env.docker - Environment configuration for Docker containers
#
# Purpose:
# This file contains environment variables specifically for running the application in Docker containers.
# It is used by both development and production Docker environments and is automatically loaded when
# the DOCKER_ENVIRONMENT flag is set to true.
#
# Key differences from .env file:
# - DB_HOST is set to 'postgres' (Docker service name) instead of 'localhost'
# - DOCKER_ENVIRONMENT flag is set to true
# - Used by both docker-compose.yml (production) and docker-compose.dev.yml (development)
#
# This file is explicitly included in Docker builds (see .dockerignore) and copied into containers
# during the build process (see Dockerfile and local.Dockerfile).

# Environment Identifier
ENV=DOCKER
DOCKER_ENVIRONMENT=true

# gRPC Server Configuration
GRPC_HOST=0.0.0.0
GRPC_PORT=8081

# REST Server Configuration has been removed
# Application now only uses gRPC

# Database Configuration
DB_HOST=postgresdb
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=root_password
DB_NAME=postgres
DB_SCHEMA=carwrapper
# DB_SSL should be set to true on commit
DB_SSL=true # IMPORTANT: Must be true for commits, can be false for local development # IMPORTANT: Must be true for commits, can be false for local development # Set to false for local development, true for production
DB_SYNCHRONIZE=false

# Logging Configuration
LOGPATH_API=./logs/car-wrapper-api_%DATE%.log
LOGPATH_GRPC=./logs/car-wrapper-grpc_%DATE%.log
LOGPATH_DEFAULT=./logs/car-wrapper-default_%DATE%.log
VOLUME_MOUNT_PATH=./mnt

# External Service Endpoints
ACL_HOST=***********:8087
ALERT_HOST=127.0.0.1:8104
DATA_FETCHING_HOST=***********:8089
NOTIFICATION_HOST=127.0.0.1:8113
DATAFETCH_SERVICE_USER_CODE=9aU05w3

# Feature Flags
ALERT_NOTIFICATION_STREAM_ENABLED=true
AUTO_CLOSE_CAR_CASE_ENABLED=true
NORMALIZE_CAR_CASE_STATUS_ENABLED=true
WEEKLY_OPEN_CAR_CASE_REPORT_ENABLED=false
# Microservice authentication
MICROSERVICE_SECRET_KEY=aCHW7ds8xn

# Note: NODE_ENV will be set by the Docker container
# For development containers: NODE_ENV=development
# For production containers: NODE_ENV=production
