WITH RECURSIVE case_id_seq AS (
  SELECT 1 AS n
  UNION ALL
  SELECT n + 1 FROM case_id_seq WHERE n < 1000000  -- Adjust upper bound as needed
),
case_id_mapping AS (
  SELECT
    cc.case_id AS legacy_case_id,
    cc.car_code,
    cc.site_object_id,
    cc.sub_site_object_id,
    cc.dev_object_id,
    CONCAT(
      cc.car_code, '-',
      cc.site_object_id, '-',
      COALESCE(NULLIF(cc.sub_site_object_id, ''), '0'), '-',
      COALESCE(NULLIF(cc.dev_object_id, ''), '0'), '-',
      (SELECT n FROM case_id_seq ORDER BY RANDOM() LIMIT 1)  -- Ensure unique sequence
    ) AS new_case_id,
    ROW_NUMBER() OVER (ORDER BY cc.logging_date) as rn
  FROM "carwrapper"."car_case" cc
  WHERE cc.logging_date >= DATE '2025-04-01'
  AND cc.car_code IS NOT NULL
  AND cc.site_object_id IS NOT NULL
),
joined_data AS (
  SELECT 
    ncc.id AS id,
    COALESCE(
      cm.new_case_id,
      CONCAT(
        ncc.car_code, '-',
        ncc.site_object_id, '-',
        COALESCE(NULLIF(ncc.sub_site_object_id, ''), '0'), '-',
        COALESCE(NULLIF(ncc.dev_object_id, ''), '0'), '-',
        (SELECT n FROM case_id_seq ORDER BY RANDOM() LIMIT 1)
      )
    ) AS new_case_id,
    ncc.site_object_id,
    ncc.sub_site_object_id,
    ncc.dev_object_id AS device_id,
    ncc.additional_dev_info AS additional_device_info,
    CONCAT(
      ncc.car_code, '-',
      ncc.site_object_id, '-',
      COALESCE(NULLIF(ncc.sub_site_object_id, ''), '0'), '-',
      COALESCE(NULLIF(ncc.dev_object_id, ''), '0')
    ) AS tags,
    ncc.car_code,
    ncc.source,
    ncc.trigger AS title,
    '' AS description,
    ncc.remarks,
    NULL::json AS recommendation,
    jsonb_build_object(
      'daily_energy_loss', ncc.daily_energy_loss,
      'migrated_from', CASE WHEN cm.legacy_case_id IS NOT NULL THEN 'new_car_case+car_case' ELSE 'new_car_case' END,
      'migrated_at', NOW(),
      'original_id', ncc.id,
      'original_case_id', ncc.case_id
    ) AS metadata,
    ncc.logging_date
  FROM "carwrapper"."new_car_case" ncc
  LEFT JOIN case_id_mapping cm ON ncc.case_id = cm.legacy_case_id
  WHERE ncc.logging_date >= DATE '2025-04-01'
  AND ncc.car_code IS NOT NULL
  AND ncc.site_object_id IS NOT NULL
)
-- Final insert or select
-- INSERT INTO "carwrapper"."car_cases" (...)
SELECT
  jd.id,
  jd.site_object_id,
  jd.sub_site_object_id,
  jd.device_id,
  jd.additional_device_info,
  jd.tags,
  jd.new_case_id AS case_id,
  jd.car_code,
  jd.source,
  jd.title,
  jd.description,
  jd.remarks,
  jd.recommendation,
  jd.metadata || jsonb_build_object(
    'total_energy_loss', cc.total_energy_loss,
    'capacity', cc.capacity,
    'original_status', cc.status
  ) AS metadata,
  jd.logging_date
FROM joined_data jd
LEFT JOIN "carwrapper"."car_case" cc
  ON jd.metadata->>'original_case_id' = cc.case_id
  AND cc.logging_date >= DATE '2025-04-01';