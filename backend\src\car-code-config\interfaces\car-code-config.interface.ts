/**
 * Interfaces for the Car Code Config gRPC service
 */

/**
 * Car code configuration data
 */
export interface CarCodeConfigDAO {
  site_object_id: string;
  car_code: string;
  monitoring_window: number;
  enabled: boolean;
  thresholds?: string; // JSON string of threshold objects with fields: 
  // name: string - The parameter being measured (e.g. 'temperature')
  // unit: string - Measurement unit (e.g. 'kWh', '%')
  // value: string - Threshold value
  // severity?: 'low'|'medium'|'high'|'critical' - Severity level
  // duration?: string - Time threshold must be exceeded (e.g. '30 minutes')
  // condition?: string - Comparison operator (e.g. '>', '<=', '==')
  // alert_type?: string - Type of alert to trigger (e.g. 'email', 'sms')
  // escalation_path?: string - Team to notify (e.g. 'operations_team')
  recommendation?: string; // JSON string of recommendation objects with fields:
  // name: string - Action to take
  // man_hour: string - Hours of labor required
  // num_of_man: string - Number of people required
  // equipment_cost: string - Cost of materials/equipment
  // priority?: 'low'|'medium'|'high' - Urgency of recommendation
  // required_certifications?: string[] - Needed certifications
  // estimated_downtime?: string - Expected system downtime
  // safety_requirements?: string[] - Safety protocols needed
  // follow_up_actions?: string[] - Post-repair verification steps
  updated_at: string; // ISO 8601 format
  updated_by: string;
}

/**
 * Request to create multiple car code configurations
 */
export interface CreateCarCodeConfigsRequest {
  requests: CarCodeConfigDAO[];
  user_id: string; // User ID for ACL check
}

/**
 * Request to find car code configurations with optional filters
 */
export interface FindCarCodeConfigsRequest {
  car_code?: string;
  site_object_id?: string;
  limit?: number; // Maximum number of records to return
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  user_id: string; // User ID for ACL check
}

/**
 * Request for finding car code config history
 */
export interface FindCarCodeConfigHistoryRequest {
  car_code?: string;
  site_object_id?: string;
  start_date?: string; // ISO 8601 format
  end_date?: string; // ISO 8601 format
  sort_order?: 'ASC' | 'DESC'; // Default is DESC
  limit?: number; // Maximum number of records to return
  user_id: string; // User ID for ACL check
}

/**
 * Type for updating a car code configuration
 * Requires car_code and site_object_id, all other fields are optional
 */
export interface UpdateCarCodeConfigDTO {
  car_code: string;
  site_object_id: string;
  monitoring_window?: number;
  enabled?: boolean;
  thresholds?: string; // JSON string of threshold objects with fields: 
  // name: string - The parameter being measured (e.g. 'temperature')
  // unit: string - Measurement unit (e.g. 'kWh', '%')
  // value: string - Threshold value
  // severity?: 'low'|'medium'|'high'|'critical' - Severity level
  // duration?: string - Time threshold must be exceeded (e.g. '30 minutes')
  // condition?: string - Comparison operator (e.g. '>', '<=', '==')
  // alert_type?: string - Type of alert to trigger (e.g. 'email', 'sms')
  // escalation_path?: string - Team to notify (e.g. 'operations_team')
  recommendation?: string; // JSON string of recommendation objects with fields:
  // name: string - Action to take
  // man_hour: string - Hours of labor required
  // num_of_man: string - Number of people required
  // equipment_cost: string - Cost of materials/equipment
  // priority?: 'low'|'medium'|'high' - Urgency of recommendation
  // required_certifications?: string[] - Needed certifications
  // estimated_downtime?: string - Expected system downtime
  // safety_requirements?: string[] - Safety protocols needed
  // follow_up_actions?: string[] - Post-repair verification steps
  updated_at?: string;
  updated_by?: string;
}

/**
 * Request to update a car code configuration
 */
export interface UpdateCarCodeConfigRequest {
  request: UpdateCarCodeConfigDTO;
  user_id: string; // User ID for ACL check
}

/**
 * Request to delete a car code configuration
 */
export interface DeleteCarCodeConfigRequest {
  car_code: string;
  site_object_id: string;
  updated_by: string;
  user_id: string; // User ID for ACL check
}

/**
 * Success response for car code configuration operations
 */
export interface CarCodeConfigsSuccessResponse {
  status: string;
  code: number;
  message: CarCodeConfigDAO[];
}

/**
 * Error response for car code configuration operations
 */
export interface CarCodeConfigsErrorResponse {
  status: string;
  code: number;
  message: any[];
  fields: string[];
  error: string;
}

/**
 * Standard response format for car code configuration operations
 */
export interface CarCodeConfigsResponse {
  success?: CarCodeConfigsSuccessResponse;
  error?: CarCodeConfigsErrorResponse;
}

/**
 * Response for health check
 */
export interface PingResponse {
  message: string;
}
