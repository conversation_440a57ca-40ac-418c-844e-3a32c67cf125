-- CAR_CASE Migration Script - Updated Workflow
-- This script migrates data from the old car_case and new_car_case tables to the new CAR_CASES table
-- Following the updated migration workflow that starts with new_car_case as the primary source

-- Step 1: Prepare for migration by setting up case_id generation
-- First, create a temporary sequence to generate counters
DROP SEQUENCE IF EXISTS temp_case_counter;
CREATE TEMPORARY SEQUENCE temp_case_counter START 1; -- Explicitly start from 1

-- Create a function to generate the new case_id
CREATE OR REPLACE FUNCTION generate_case_id(car_code TEXT, site_id TEXT, subsite_id TEXT, device_id TEXT) 
RETURNS TEXT AS $$
DECLARE
    base_id TEXT;
    counter INT;
    new_case_id TEXT;
BEGIN
    base_id := CONCAT(
      car_code, '-',
      site_id, '-',
      COALESCE(NULLIF(subsite_id, ''), '0'), '-',
      COALESCE(NULLIF(device_id, ''), '0')
    );
    SELECT nextval('temp_case_counter') INTO counter;
    new_case_id := CONCAT(base_id, '-', counter::TEXT);
    RETURN new_case_id;
END;
$$ LANGUAGE plpgsql;

-- Create a mapping table to assign new case_ids to legacy case_ids in a single step
CREATE TEMPORARY TABLE case_id_mapping AS
SELECT
    case_id AS legacy_case_id,
    car_code,
    site_object_id,
    sub_site_object_id,
    dev_object_id,
    generate_case_id(
        car_code::TEXT,
        site_object_id::TEXT,
        sub_site_object_id::TEXT,
        dev_object_id::TEXT
    ) AS new_case_id
FROM (
    SELECT DISTINCT
        cc.case_id,
        cc.car_code,
        cc.site_object_id,
        cc.sub_site_object_id,
        cc.dev_object_id,
        cc.logging_date
    FROM "carwrapper"."car_case" cc
    WHERE cc.logging_date >= DATE '2025-01-01'
    ORDER BY cc.logging_date
) ordered_cases;

-- Step 2: Create a temporary table to build complete records
CREATE TEMPORARY TABLE temp_car_cases AS 
-- Start with new_car_case as the primary data source
SELECT 
  ncc.id AS id,
  -- Generate case_id using car_code and site_object_id with sequence number
  CONCAT(
    ncc.car_code, '-',
    ncc.site_object_id, '-',
    COALESCE(NULLIF(ncc.sub_site_object_id, ''), '0'), '-',
    COALESCE(NULLIF(ncc.dev_object_id, ''), '0'), '-',
    nextval('temp_case_counter')::TEXT
  ) AS case_id,
  ncc.site_object_id,
  ncc.sub_site_object_id,
  ncc.dev_object_id AS device_id,
  ncc.additional_dev_info AS additional_device_info,
  -- Create formatted tags immediately
  CONCAT(
  ncc.car_code, '-',
  ncc.site_object_id, '-',
  COALESCE(NULLIF(ncc.sub_site_object_id, ''), '0'), '-',
  COALESCE(NULLIF(ncc.dev_object_id, ''), '0')
) AS tags,
  ncc.car_code,
  ncc.source,
  ncc.trigger AS title,
  '' AS description,
  ncc.remarks,
  NULL::json AS recommendation, -- No recommendation in legacy schema
  jsonb_build_object(
    'daily_energy_loss', ncc.daily_energy_loss,
    'migrated_from', CASE WHEN cm.legacy_case_id IS NOT NULL THEN 'new_car_case+car_case' ELSE 'new_car_case' END,
    'migrated_at', NOW(),
    'original_id', ncc.id,
    'original_case_id', ncc.case_id
  ) AS metadata,
  ncc.logging_date
FROM "carwrapper"."new_car_case" ncc
LEFT JOIN case_id_mapping cm ON ncc.case_id = cm.legacy_case_id
WHERE ncc.logging_date >= DATE '2025-01-01'
  AND ncc.car_code IS NOT NULL
  AND ncc.site_object_id IS NOT NULL;

-- Step 3: Enrich with data from car_case
-- For records that have a matching car_case entry
-- No recommendation field in legacy schema, so skip updating recommendation
UPDATE temp_car_cases tc
SET 
  -- Preserve historical data in metadata
  metadata = jsonb_build_object(
    'daily_energy_loss', tc.metadata->'daily_energy_loss',
    'total_energy_loss', cc.total_energy_loss,
    'capacity', cc.capacity,
    'original_status', cc.status,
    'migrated_from', 'new_car_case+car_case',
    'migrated_at', NOW()
  )
FROM "carwrapper"."car_case" cc
WHERE tc.metadata->>'original_case_id' = cc.case_id;

-- Step 4: Insert the complete records into the new CAR_CASES table
-- Uncomment the following lines to perform the actual insert
-- INSERT INTO "carwrapper"."car_cases" (
--   site_object_id,
--   sub_site_object_id,
--   device_id,
--   additional_device_info,
--   tags,
--   case_id,
--   car_code,
--   source,
--   title,
--   description,
--   remarks,
--   recommendation,
--   metadata,
--   logging_date
-- )
SELECT
  id,
  site_object_id,
  sub_site_object_id,
  device_id,
  additional_device_info,
  tags,
  case_id,
  car_code,
  source,
  title,
  description,
  remarks,
  recommendation,
  metadata,
  logging_date
FROM temp_car_cases;

-- Step 5: Clean up temporary objects
DROP FUNCTION IF EXISTS generate_case_id(TEXT, TEXT, TEXT, TEXT);
DROP SEQUENCE IF EXISTS temp_case_counter;
DROP TABLE IF EXISTS case_id_mapping;
DROP TABLE IF EXISTS temp_car_cases;

