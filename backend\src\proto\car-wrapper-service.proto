syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";

package car_wrapper_service;

// Main service that combines all car-related services
service CarWrapperService {
  // Main health check
  rpc Ping (PingRequest) returns (PingResponse) {}

  /***
  Car Case Service Methods
  ***/
  rpc PingCarCase (PingRequest) returns (PingResponse) {}
  rpc CreateCarCase (CreateCarCasesRequest) returns (CarCasesResponse) {}
  rpc FindCarCases (FindCarCasesRequest) returns (CarCasesResponse) {}
  rpc FindCarCasesByIds (FindCarCasesByIdsRequest) returns (CarCasesResponse) {}

  /***
  Car Code Config Service Methods
  ***/
  rpc PingCarCodeConfig (PingRequest) returns (PingResponse) {}
  rpc CreateCarCodeConfig (CreateCarCodeConfigsRequest) returns (CarCodeConfigsResponse) {}
  rpc FindCarCodeConfigs (FindCarCodeConfigsRequest) returns (CarCodeConfigsResponse) {}
  rpc FindCarCodeConfigById (FindCarCodeConfigByIdRequest) returns (CarCodeConfigsResponse) {}
  rpc FindCarCodeConfigHistory (FindCarCodeConfigHistoryRequest) returns (CarCodeConfigHistoryResponse) {}
  rpc UpdateCarCodeConfig (UpdateCarCodeConfigRequest) returns (CarCodeConfigsResponse) {}
  rpc DeleteCarCodeConfig (DeleteCarCodeConfigRequest) returns (CarCodeConfigsResponse) {}

  /***
  Car Code Alarm Config Service Methods
  ***/
  rpc PingCarCodeAlarmConfig (PingRequest) returns (PingResponse) {}
  rpc CreateCarCodeAlarmConfig (CreateCarCodeAlarmConfigsRequest) returns (CarCodeAlarmConfigsResponse) {}
  rpc FindCarCodeAlarmConfigs (FindCarCodeAlarmConfigsRequest) returns (CarCodeAlarmConfigsResponse) {}
  rpc FindCarCodeAlarmConfigHistory (FindCarCodeAlarmConfigHistoryRequest) returns (CarCodeAlarmConfigHistoryResponse) {}
  rpc UpdateCarCodeAlarmConfig (UpdateCarCodeAlarmConfigRequest) returns (CarCodeAlarmConfigsResponse) {}
  rpc DeleteCarCodeAlarmConfig (DeleteCarCodeAlarmConfigRequest) returns (CarCodeAlarmConfigsResponse) {}

}

/***
Common Messages
***/
// Empty message for requests that don't need parameters
message PingRequest {}

// Ping response for health check
message PingResponse {
  string message = 1;
}

/***
Car Case Messages
***/
// Base interface for key-value pair structures
// Both key and value must be strings.
// Example: details = { "a": "foo", "b": "bar" }

// Request to create a new car case
message CreateCarCaseRequest {
  string site_object_id = 1;
  string sub_site_object_id = 2;
  string device_id = 3;
  string additional_device_info = 4; // JSON string (e.g., "{\"key\":\"value\"}")
  string car_code = 5;
  string source = 6;
  string title = 7;
  string description = 8;
  string remarks = 9;
  string recommendation = 10; // JSON string (e.g., "{\"key\":\"value\"}")
  string metadata = 11; // JSON string (e.g., "{\"key\":\"value\"}")
  string logging_date = 12; // ISO 8601 format (e.g., "2025-04-22T09:13:08.939Z" or "2025-04-22")
}

// Request to create multiple car cases in a single request
message CreateCarCasesRequest {
  repeated CreateCarCaseRequest requests = 1;
  string user_id = 2; // User ID for ACL check
}

// Response for car case operations
message CarCasesResponse {
  oneof response {
    CarCasesSuccessResponse success = 1;
    CarCasesErrorResponse error = 2;
  }
}

// Success response format for car cases
message CarCasesSuccessResponse {
  string status = 1;
  int32 code = 2;
  repeated CarCaseConsoDAO message = 3;
}

// Error response format for car cases
message CarCasesErrorResponse {
  string status = 1;
  int32 code = 2;
  repeated string message = 3;
  repeated string fields = 4;
  string error = 5;
}

// Request to find car cases with filters
message FindCarCasesRequest {
  string site_object_id = 1;
  string car_code = 2;
  string start_date = 3; // ISO 8601 format (e.g., "2025-04-01T00:00:00Z" or "2025-04-01")
  string end_date = 4; // ISO 8601 format (e.g., "2025-04-30T23:59:59Z" or "2025-04-30")
  string status = 5; // "open", "closed", or empty for all
  string source = 6;
  string sort_order = 7; // ASC or DESC, default is DESC
  int32 limit = 8; // Maximum number of records to return, default is 1000
  optional int32 offset = 9; // Offset for pagination, default is 0
  string user_id = 10; // User ID for ACL check
}

// Request to find car cases by their IDs
message FindCarCasesByIdsRequest {
  repeated string ids = 1;
  int32 limit = 2; // Maximum number of records to return, default is 1000
  optional int32 offset = 3; // Offset for pagination, default is 0
  string sort_order = 4; // ASC or DESC, default is DESC
  string user_id = 5; // User ID for ACL check
}

// Consolidated car case data with all related information
message CarCaseConsoDAO {
  string id = 1;
  repeated string conso_id = 2;
  string site_object_id = 3;
  string sub_site_object_id = 4;
  string device_id = 5;
  string additional_device_info = 6; // JSON string (e.g., "{\"key\":\"value\"}")
  string tags = 7;
  string case_id = 8;
  string car_code = 9;
  string source = 10;
  string title = 11;
  string description = 12;
  string remarks = 13;
  string recommendation = 14; // JSON string (e.g., "{\"key\":\"value\"}")
  string metadata = 15; // JSON string (e.g., "{\"key\":\"value\"}")
  string logging_date = 16; // ISO 8601 format (e.g., "2025-04-22T09:13:08.939Z" or "2025-04-22")
  int32 occurrence_count = 17;
  string first_occurrence = 18;
  string most_recent_occurrence = 19;
  string status = 20;
}

/***
Car Code Config Messages
***/
// Car code configuration data
message CarCodeConfigDAO {
  string site_object_id = 1;
  string car_code = 2;
  int32 monitoring_window = 3;
  bool enabled = 4;
  string thresholds = 5; // JSON string (e.g., "{\"key\":\"value\"}")
  string recommendation = 6; // JSON string (e.g., "{\"key\":\"value\"}")
  string updated_at = 7; // ISO 8601 format (e.g., "2025-04-22T09:13:08.939Z" or "2025-04-22")
  string updated_by = 8;
}

// Request to create multiple car code configurations in a single request
message CreateCarCodeConfigsRequest {
  repeated CarCodeConfigDAO requests = 1;
  string user_id = 2; // User ID for ACL check
}

// Response for car code config operations
message CarCodeConfigsResponse {
  oneof response {
    CarCodeConfigsSuccessResponse success = 1;
    CarCodeConfigsErrorResponse error = 2;
  }
}

// Success response format for car code configs
message CarCodeConfigsSuccessResponse {
  string status = 1;
  int32 code = 2;
  repeated CarCodeConfigDAO message = 3;
}

// Error response format for car code configs
message CarCodeConfigsErrorResponse {
  string status = 1;
  int32 code = 2;
  repeated string message = 3;
  repeated string fields = 4;
  string error = 5;
}

// Request to find car code configurations with filters
message FindCarCodeConfigsRequest {
  string car_code = 1;
  string site_object_id = 2;
  int32 limit = 3; // Maximum number of records to return, default is 500
  string sort_order = 4; // ASC or DESC, default is ASC
  string user_id = 5; // User ID for ACL check
}

// Request to find a specific car code configuration by ID
message FindCarCodeConfigByIdRequest {
  string car_code = 1;
  string site_object_id = 2;
  string user_id = 3; // User ID for ACL check
}

// Car code config history data
message CarCodeConfigHistoryDAO {
  string car_code = 1;
  string site_object_id = 2;
  string old_value = 3; // JSON string
  string new_value = 4; // JSON string
  string transaction_type = 5; // "create", "update", or "delete"
  string updated_at = 6; // ISO 8601 format
  string updated_by = 7;
}

// Response for car code config history operations
message CarCodeConfigHistoryResponse {
  oneof response {
    CarCodeConfigHistorySuccessResponse success = 1;
    CarCodeConfigHistoryErrorResponse error = 2;
  }
}

// Success response format for car code config history
message CarCodeConfigHistorySuccessResponse {
  string status = 1;
  int32 code = 2;
  repeated CarCodeConfigHistoryDAO message = 3;
}

// Error response format for car code config history
message CarCodeConfigHistoryErrorResponse {
  string status = 1;
  int32 code = 2;
  repeated string message = 3;
  repeated string fields = 4;
  string error = 5;
}

// Request to find car code config history
message FindCarCodeConfigHistoryRequest {
  string car_code = 1;
  string site_object_id = 2;
  string start_date = 3; // ISO 8601 format (e.g., "2025-04-01T00:00:00Z" or "2025-04-01")
  string end_date = 4; // ISO 8601 format (e.g., "2025-04-30T23:59:59Z" or "2025-04-30")
  string sort_order = 5; // ASC or DESC, default is DESC
  int32 limit = 6; // Maximum number of records to return, default is 2000
  string user_id = 7; // User ID for ACL check
}

// Request to update a car code configuration
message UpdateCarCodeConfigRequest {
  CarCodeConfigDAO request = 1;
  string user_id = 2; // User ID for ACL check
}

// Request to delete a car code configuration
message DeleteCarCodeConfigRequest {
  string car_code = 1;
  string site_object_id = 2;
  string updated_by = 3;
  string user_id = 4; // User ID for ACL check
}

/***
Car Code Alarm Config Messages
***/
// Car code alarm configuration data
message CarCodeAlarmConfigDAO {
  string car_code = 1;
  string name = 2;
  string category = 3;
  string device_type = 4;
  bool should_raise_alarm = 5;
  bool should_raise_alarm_new_value = 6;
  string updated_at = 7; // ISO 8601 format
  string updated_by = 8;
}

// Request to create multiple car code alarm configurations in a single request
message CreateCarCodeAlarmConfigsRequest {
  repeated CarCodeAlarmConfigDAO requests = 1;
  string user_id = 2; // User ID for ACL check
}

// Response for car code alarm config operations
message CarCodeAlarmConfigsResponse {
  oneof response {
    CarCodeAlarmConfigsSuccessResponse success = 1;
    CarCodeAlarmConfigsErrorResponse error = 2;
  }
}

// Success response format for car code alarm configs
message CarCodeAlarmConfigsSuccessResponse {
  string status = 1;
  int32 code = 2;
  repeated CarCodeAlarmConfigDAO message = 3;
}

// Error response format for car code alarm configs
message CarCodeAlarmConfigsErrorResponse {
  string status = 1;
  int32 code = 2;
  repeated string message = 3;
  repeated string fields = 4;
  string error = 5;
}

// Request to find car code alarm configurations with filters
message FindCarCodeAlarmConfigsRequest {
  string car_code = 1;
  int32 limit = 2; // Maximum number of records to return, default is 500
  string sort_order = 3; // ASC or DESC, default is ASC
  string user_id = 4; // User ID for ACL check
}

// Car code alarm config history data
message CarCodeAlarmConfigHistoryDAO {
  string car_code = 1;
  string old_value = 2; // JSON string
  string new_value = 3; // JSON string
  string transaction_type = 4; // "create", "update", or "delete"
  string updated_at = 5; // ISO 8601 format
  string updated_by = 6;
}

// Response for car code alarm config history operations
message CarCodeAlarmConfigHistoryResponse {
  oneof response {
    CarCodeAlarmConfigHistorySuccessResponse success = 1;
    CarCodeAlarmConfigHistoryErrorResponse error = 2;
  }
}

// Success response format for car code alarm config history
message CarCodeAlarmConfigHistorySuccessResponse {
  string status = 1;
  int32 code = 2;
  repeated CarCodeAlarmConfigHistoryDAO message = 3;
}

// Error response format for car code alarm config history
message CarCodeAlarmConfigHistoryErrorResponse {
  string status = 1;
  int32 code = 2;
  repeated string message = 3;
  repeated string fields = 4;
  string error = 5;
}

// Request to find car code alarm config history
message FindCarCodeAlarmConfigHistoryRequest {
  string car_code = 1;
  string start_date = 2; // ISO 8601 format (e.g., "2025-04-01T00:00:00Z" or "2025-04-01")
  string end_date = 3; // ISO 8601 format (e.g., "2025-04-30T23:59:59Z" or "2025-04-30")
  string sort_order = 4; // ASC or DESC, default is DESC
  int32 limit = 5; // Maximum number of records to return, default is 2000
  string user_id = 6; // User ID for ACL check
}

// Request to update a car code alarm configuration
message UpdateCarCodeAlarmConfigRequest {
  CarCodeAlarmConfigDAO request = 1;
  string user_id = 2; // User ID for ACL check
}

// Request to delete a car code alarm configuration
message DeleteCarCodeAlarmConfigRequest {
  string car_code = 1;
  string updated_by = 2;
  string user_id = 3; // User ID for ACL check
}
