# Docker Compose file for development environment
#
# This file defines the development services for the Car Wrapper Service:
# - PostgreSQL database for data storage
# - CloudBeaver for database management
# - NestJS gRPC microservice with HMR enabled
#
# Container naming convention:
# - Development containers have no suffix (e.g., nestjs_carwrapperservice)
# - Production containers have a '_prod' suffix (e.g., nestjs_carwrapperservice_prod)
# This helps distinguish between development and production containers when both are running.
services:
  # PostgreSQL database for storing car wrapper data
  postgresdb:
    image: postgres:latest
    container_name: nestjs_postgresdb
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: user
      POSTGRES_PASSWORD: root_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/db/init.sql:/docker-entrypoint-initdb.d/01-init.sql
    networks:
      - backend_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Database management web UI for PostgreSQL
  cloudbeaver:
    image: dbeaver/cloudbeaver:latest
    container_name: nestjs_cloudbeaver
    ports:
      - "8978:8978"
    volumes:
      - cloudbeaver_data:/opt/cloudbeaver/workspace
    networks:
      - backend_network
    depends_on:
      - postgresdb
    restart: unless-stopped

  # NestJS gRPC microservice for handling car wrapper operations
  car-wrapper-service-backend:
    build:
      context: .
      dockerfile: local.Dockerfile
    container_name: nestjs_carwrapperservice
    ports:
      - "8081:8081"
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    # Use the .env.docker file for environment variables
    # This ensures all variables are in one place and reduces duplication
    env_file:
      - .env.docker
    # Override only the variables that need to be different from .env.docker
    environment:
      - NODE_ENV=development
      - DB_HOST=postgresdb  # Must point to the service name in docker-compose
      - DOCKER_ENVIRONMENT=true
      - ENV=development
    networks:
      - backend_network
    depends_on:
      postgresdb:
        condition: service_healthy
    # For development with HMR and file watching in Docker
    command: ["npm", "run", "start:docker:dev"]

volumes:
  postgres_data:
    name: nestjs_postgres_data
  cloudbeaver_data:
    name: nestjs_cloudbeaver_data

networks:
  backend_network:
    name: nestjs_backend_network
    driver: bridge
