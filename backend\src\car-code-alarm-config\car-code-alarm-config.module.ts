import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CarCodeAlarmConfig } from './entities';
import { CarCodeAlarmConfigService } from './car-code-alarm-config.service';
import { CarCodeAlarmConfigController } from './car-code-alarm-config.controller';
import { CarCodeAlarmConfigHistoryModule } from '../car-code-alarm-config-history/car-code-alarm-config-history.module';
import { AclGuard } from '../client/acl/acl.guard';
import { AclExceptionFilter } from '../client/acl/acl-exception.filter';
import { AclModule } from '../client/acl/acl.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CarCodeAlarmConfig]),
    CarCodeAlarmConfigHistoryModule,
    AclModule, // Import AclModule to make AclService available
  ],
  providers: [CarCodeAlarmConfigService, AclGuard, AclExceptionFilter],
  controllers: [CarCodeAlarmConfigController],
  exports: [TypeOrmModule, CarCodeAlarmConfigService],
})
export class CarCodeAlarmConfigModule {}
