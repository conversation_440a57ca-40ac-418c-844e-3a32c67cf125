#!/usr/bin/env python3
"""
Generate dummy data for testing the car-code-alarm-config API.

This script generates realistic test data for the car-code-alarm-config API in gRPC format
for testing with grpcurl.

Usage:
    python generate_car_code_alarm_config_test_data.py [OPTIONS]

Options:
    --count     Number of car code alarm configurations to generate (default: 10)
    --output    Output file name (default: car_code_alarm_configs_grpc.json)
    --output-dir Output directory (default: output)
    --port      gRPC server port (default: 8081)
"""

import argparse
import json
import random
import datetime
import os
import sys
from typing import List, Dict, Any


# Load the shared test data config
def load_test_data_config():
    """Load the shared test data configuration."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shared_dir = os.path.join(os.path.dirname(script_dir), 'shared')
    config_path = os.path.join(shared_dir, 'test_data_config.json')

    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Shared test data config not found at {config_path}")
        print("This file is required for test data generation.")
        print("Please ensure the file exists before running this script.")
        sys.exit(1)


# Global test data config
TEST_DATA_CONFIG = load_test_data_config()

# Probability of omitting optional fields (0.0 to 1.0)
# 0.25 means 25% chance of being omitted
OMIT_FIELD_PROBABILITY = 0.25


def generate_car_code_alarm_config() -> Dict[str, Any]:
    """Generate a single car code alarm configuration with realistic data matching the CarCodeAlarmConfigDAO interface.

    Note: This function selects a random car code from the shared config,
    but the car_code may be overridden by the calling function to ensure
    strict adherence to the shared test data configuration.
    """
    # Get a random car code from the shared config
    car_codes = list(TEST_DATA_CONFIG['car_codes'].keys())
    car_code = random.choice(car_codes).lower()  # Ensure car_code is lowercase

    # Generate a name based on the car code
    name = f"{car_code.upper()} Alarm Configuration"

    # Generate a description (with chance to omit)
    description = None
    if random.random() >= OMIT_FIELD_PROBABILITY:
        # Use the description from the test data config if available
        if 'description' in TEST_DATA_CONFIG['car_codes'][car_code]:
            base_description = TEST_DATA_CONFIG['car_codes'][car_code]['description']
            descriptions = [
                f"Alarm configuration for {base_description}",
                f"Detects {car_code.upper()} issues and raises alarms",
                f"Monitors {car_code.upper()} events for critical conditions",
                f"Triggers notifications for {car_code.upper()} anomalies",
                f"Alerts system when {car_code.upper()} thresholds are exceeded"
            ]
        else:
            descriptions = [
                f"Alarm configuration for {car_code.upper()}",
                f"Detects {car_code.upper()} issues and raises alarms",
                f"Monitors {car_code.upper()} events for critical conditions",
                f"Triggers notifications for {car_code.upper()} anomalies",
                f"Alerts system when {car_code.upper()} thresholds are exceeded"
            ]
        description = random.choice(descriptions)

    # Generate a category (with chance to omit)
    category = None
    if random.random() >= OMIT_FIELD_PROBABILITY:
        categories = [
            "Performance",
            "Safety",
            "Maintenance",
            "Efficiency",
            "Operational",
            "Critical",
            "Warning",
            "Informational"
        ]
        category = random.choice(categories)

    # Generate a device type (with chance to omit)
    device_type = None
    if random.random() >= OMIT_FIELD_PROBABILITY:
        device_types = [
            "Inverter",
            "Panel",
            "Sensor",
            "Controller",
            "Battery",
            "Tracker",
            "Meter",
            "Gateway"
        ]
        device_type = random.choice(device_types)

    # Generate a timestamp for updated_at
    days_ago = random.randint(0, 30)
    updated_at = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

    # Create the car code alarm config object matching the CarCodeAlarmConfigDAO interface
    config = {
        "car_code": car_code,
        "name": name,
        "should_raise_alarm": random.choice([True, False]),  # Boolean value as required by the interface
        "updated_at": updated_at,
        "updated_by": "test-script"
    }

    # Only add optional fields if they are not None
    if description is not None:
        config["description"] = description

    if category is not None:
        config["category"] = category

    if device_type is not None:
        config["device_type"] = device_type

    return config


def generate_car_code_alarm_configs(count: int) -> List[Dict[str, Any]]:
    """Generate multiple car code alarm configurations.

    If count is less than or equal to the number of available car codes,
    uses a random subset of the car codes. Otherwise, reuses car codes with different
    other attributes (name, description, etc.) to reach the desired count.
    """
    car_codes = list(TEST_DATA_CONFIG['car_codes'].keys())
    configs = []

    if count <= len(car_codes):
        # Use a random subset of the car codes
        selected_car_codes = random.sample(car_codes, count)

        for car_code in selected_car_codes:
            # Create a config based on this car code
            config = generate_car_code_alarm_config()
            config['car_code'] = car_code.lower()
            configs.append(config)
    else:
        # Generate one config for each car code first
        for car_code in car_codes:
            # Create a config based on this car code
            config = generate_car_code_alarm_config()
            config['car_code'] = car_code.lower()
            configs.append(config)

        # If we need more configs, cycle through the car codes again with different other attributes
        remaining = count - len(car_codes)
        while remaining > 0:
            for car_code in car_codes:
                if remaining <= 0:
                    break

                # Create a new config with the same car_code but different other attributes
                config = generate_car_code_alarm_config()
                config['car_code'] = car_code.lower()
                configs.append(config)
                remaining -= 1

    return configs


def save_as_json(configs: List[Dict[str, Any]], output_file: str) -> None:
    """Save car code alarm configurations as a plain JSON array."""
    with open(output_file, 'w') as f:
        json.dump(configs, f, indent=2)


def main():
    parser = argparse.ArgumentParser(description='Generate dummy data for car-code-alarm-config API testing')
    parser.add_argument('--count', type=int, default=10, help='Number of car code alarm configurations to generate')
    parser.add_argument('--output', type=str, default='car_code_alarm_configs_grpc.json', help='Output file name')
    parser.add_argument('--output-dir', type=str, default='output',
                       help='Output directory (default: output)')
    parser.add_argument('--port', type=int, default=8081, help='gRPC server port (default: 8081)')

    args = parser.parse_args()

    # Create output directory with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, timestamp)
    os.makedirs(output_path, exist_ok=True)

    print(f"Output directory: {output_path}")

    # Generate car code alarm configs
    configs = generate_car_code_alarm_configs(args.count)

    # Save as plain JSON
    json_file = os.path.join(output_path, args.output)
    print(f"Generating test data ({args.count} records)...")
    save_as_json(configs, json_file)

    # Print summary
    print("\nDone! Test data file has been created in", output_path)
    print("")
    print("Files generated:")
    print(f"- {json_file}: Car code alarm configurations")



if __name__ == "__main__":
    main()
