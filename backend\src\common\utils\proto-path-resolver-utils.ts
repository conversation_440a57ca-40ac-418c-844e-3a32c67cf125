import { join } from 'path';
import * as fs from 'fs';
import { Logger } from '@nestjs/common';

/**
 * Utility functions for resolving proto file paths across different environments
 */

const logger = new Logger('ProtoPathResolver');

/**
 * Finds the first existing file from a list of possible paths
 * @param paths Array of possible file paths to check
 * @returns The first path that exists, or the first path if none exist
 */
export const findFirstExistingFile = (paths: string[]): string => {
  for (const path of paths) {
    try {
      if (fs.existsSync(path)) {
        logger.log(`Found proto file at: ${path}`);
        return path;
      }
    } catch (error) {
      // Log the error for debugging purposes
      logger.warn(
        `Error checking path ${path}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
  // If no file is found, return the first path as a fallback
  logger.error(`No proto file found in paths: ${paths.join(', ')}`);
  return paths[0];
};

/**
 * Generates possible paths for a proto file across different environments
 * @param protoFileName Name of the proto file
 * @returns Array of possible paths where the file might be located
 */
export const generatePossiblePaths = (protoFileName: string): string[] => {
  return [
    join(process.cwd(), `src/proto/${protoFileName}`),
    join(process.cwd(), `proto/${protoFileName}`),
    join(__dirname, `../../../proto/${protoFileName}`), // Adjusted for common/utils folder depth
    join(__dirname, `../../proto/${protoFileName}`),
  ];
};

/**
 * Resolves paths for a list of proto files
 * @param protoFiles Array of proto file names
 * @returns Array of resolved paths to the proto files
 */
export const resolveProtoPaths = (protoFiles: string[]): string[] => {
  const protoPaths = protoFiles.map((protoFile) =>
    findFirstExistingFile(generatePossiblePaths(protoFile)),
  );

  logger.log(`Using proto paths: ${protoPaths.join(', ')}`);
  return protoPaths;
};
