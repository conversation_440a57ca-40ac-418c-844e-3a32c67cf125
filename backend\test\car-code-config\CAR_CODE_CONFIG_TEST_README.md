# Car Code Config Test Data

This directory provides scripts to quickly generate sample data for car code config testing. All scripts use shared config from `../shared/test_data_config.json` for consistency.

## Scripts

- **Car Code Config Generator** (`generate_car_code_config_test_data.py`)
  - Outputs a plain JSON array of car code configs.
- **Seed Script** (`seed_car_code_config.js`)
  - Inserts car code configs into the database for integration testing.

## How to Use

```bash
python generate_car_code_config_test_data.py           # 10 configs (default)
python generate_car_code_config_test_data.py --count=20 # 20 configs
```

- Output: `output/<timestamp>/car_code_configs.json`

## Output Example

```bash
output/
  ├── 20250529_153000/
  │   └── car_code_configs.json
  └── ...
```

- Each run creates a new timestamped folder for easy organization and history.

## Prerequisites

- Make sure `../shared/test_data_config.json` exists.

## Shared Test Data Config

- All scripts use `../shared/test_data_config.json` for car codes, site IDs, and assignments.
- To change test data structure, update this file (do not hardcode values elsewhere).

---
**Tip:** Delete old folders in `output/` to keep your workspace clean.

Both the test data generator and seed script use the shared test data configuration from `../shared/test_data_config.json`.

The shared configuration contains:

- **Car codes** with their monitoring periods
- **Site object IDs** and their assigned car codes
- **Site-to-car-code assignments** for consistent testing

Rather than duplicating this information in multiple places, all test components reference the same JSON file. This ensures consistency and simplifies maintenance.

> **Important:** If you need to modify the car codes, site IDs, or their relationships, please update the shared configuration file at `../shared/test_data_config.json`. Do not hardcode these values elsewhere.

## Using with Car Case Test Data

After seeding the car code configurations, you can generate and insert car case test data using the scripts in the `backend/test/car-case` directory.

For example:

```bash
# First seed car code configurations
cd backend/test/car-code-config
node seed_car_code_config.js

# Then generate and insert car case test data
cd ../car-case
python generate_car_case_test_data.py --count=20
```

This will ensure that the car case test data can be successfully inserted because the required car code configurations exist.
