#!/usr/bin/env python3
"""
Mermaid Diagram Converter v2.0

This script converts Mermaid diagram files (.mmd) to PNG images using Playwright.
It requires at least one command-line argument (the input file path).

Changelog:
v2.0 (2025-05-16):
- Added support for saving output to images folder instead of diagrams folder
- Increased default viewport size to 3000x2000 pixels
- Added better error handling and logging
- Added debug mode for troubleshooting
- Added support for custom timeout
- Fixed Mermaid initialization issues
- Added diagram dimension detection and warnings

v1.0 (Initial version):
- Basic conversion of Mermaid diagrams to PNG

Usage:
  python convert_mermaid.py input.mmd                        # Convert specific file with default size
  python convert_mermaid.py input.mmd output.png             # Convert to specific output file
  python convert_mermaid.py input.mmd --width 3000           # Convert with custom width
  python convert_mermaid.py input.mmd --height 2000          # Convert with custom height
  python convert_mermaid.py input.mmd --debug                # Run in debug mode (browser visible)
  python convert_mermaid.py --help                           # Show help message
"""

import sys
import time
import argparse
from pathlib import Path
from typing import Dict, Any, Tuple, Optional

try:
    from playwright.sync_api import sync_playwright, Page
except ImportError:
    print("Error: Playwright not installed. Please install it with:")
    print("pip install playwright")
    print("playwright install chromium")
    sys.exit(1)

# Default configuration
DEFAULT_WIDTH = 3000
DEFAULT_HEIGHT = 2000
DEFAULT_TIMEOUT = 30000  # 30 seconds


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Convert Mermaid diagram files to PNG images",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "input_file", 
        help="Path to the Mermaid diagram file (.mmd)"
    )
    
    parser.add_argument(
        "output_file", 
        nargs="?",
        help="Path to the output PNG file (default: same name as input with .png extension)"
    )
    
    parser.add_argument(
        "--width", 
        type=int, 
        default=DEFAULT_WIDTH,
        help=f"Width of the viewport in pixels (default: {DEFAULT_WIDTH})"
    )
    
    parser.add_argument(
        "--height", 
        type=int, 
        default=DEFAULT_HEIGHT,
        help=f"Height of the viewport in pixels (default: {DEFAULT_HEIGHT})"
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="Run in debug mode (browser visible)"
    )
    
    parser.add_argument(
        "--timeout", 
        type=int, 
        default=DEFAULT_TIMEOUT,
        help=f"Timeout for rendering in milliseconds (default: {DEFAULT_TIMEOUT})"
    )
    
    return parser


def read_mermaid_file(file_path: str) -> str:
    """Read the Mermaid diagram file"""
    try:
        return Path(file_path).read_text(encoding="utf-8")
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        sys.exit(1)


def create_html_template(mmd_code: str) -> str:
    """Create HTML template with the Mermaid diagram"""
    return f"""
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <title>Mermaid Diagram</title>
        <style>
          body {{ margin: 0; padding: 0; background-color: white; }}
          .mermaid-container {{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 20px;
          }}
          .mermaid {{
            width: 100%;
            margin: 0 auto;
            font-size: 16px;
          }}
          .mermaid svg {{
            max-width: 100%;
            height: auto;
          }}
        </style>
      </head>
      <body>
        <div class="mermaid-container">
          <div class="mermaid">{mmd_code}</div>
        </div>
        <script type="module">
          import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
          
          // Configure Mermaid
          mermaid.initialize({{
            "startOnLoad": true,
            "securityLevel": "loose",
            "theme": "default",
            "logLevel": "error",
            "fontSize": 16,
            "fontFamily": "sans-serif",
            "sequence": {{
              "diagramMarginX": 50,
              "diagramMarginY": 10,
              "actorMargin": 100,
              "width": 150,
              "height": 65,
              "boxMargin": 10,
              "boxTextMargin": 5,
              "noteMargin": 10,
              "messageMargin": 35
            }},
            "flowchart": {{
              "diagramPadding": 8,
              "htmlLabels": true,
              "curve": "basis"
            }},
            "er": {{
              "diagramPadding": 50,
              "layoutDirection": "LR",
              "minEntityWidth": 180,
              "minEntityHeight": 100,
              "entityPadding": 25,
              "wrap": true,
              "entityFontsize": 16,
              "relationFontsize": 16
            }}
          }});
        </script>
      </body>
    </html>
    """


def setup_page_handlers(page: Page) -> None:
    """Set up page event handlers for logging"""
    page.on("console", lambda msg: print(f"BROWSER: {msg.text}"))
    page.on("pageerror", lambda err: print(f"PAGE ERROR: {err}"))


def get_diagram_dimensions(page: Page) -> Optional[Dict[str, float]]:
    """Get the dimensions of the rendered diagram"""
    return page.evaluate("""
        () => {
            const svg = document.querySelector('.mermaid svg');
            return svg ? { 
                width: svg.getBoundingClientRect().width, 
                height: svg.getBoundingClientRect().height 
            } : null;
        }
    """)


def render_mermaid_diagram(input_file: str, output_file: str, viewport: Tuple[int, int], 
                          debug: bool = False, timeout: int = DEFAULT_TIMEOUT) -> None:
    """Render a Mermaid diagram to a PNG image"""
    print(f"\nProcessing: {input_file}")
    print(f"Output: {output_file}")
    print(f"Viewport: {viewport[0]}x{viewport[1]}")
    
    # Read the Mermaid diagram file
    mmd_code = read_mermaid_file(input_file)
    
    # Create HTML template
    html_template = create_html_template(mmd_code)
    
    # Render the diagram
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=not debug)
        page = browser.new_page(viewport={"width": viewport[0], "height": viewport[1]})
        
        # Set up page handlers
        setup_page_handlers(page)
        
        try:
            # Set page content
            page.set_content(html_template, wait_until="networkidle")
            print("HTML content loaded")
            
            # Wait for rendering
            time.sleep(2)  # Short initial wait
            
            # Wait for Mermaid to finish rendering
            print(f"Waiting for diagram to render (timeout: {timeout/1000}s)...")
            page.wait_for_selector(".mermaid svg", timeout=timeout)
            print("Diagram rendered successfully")
            
            # Get diagram dimensions
            dimensions = get_diagram_dimensions(page)
            if dimensions:
                print(f"Diagram dimensions: {dimensions['width']}x{dimensions['height']}")
                if dimensions['width'] > viewport[0] or dimensions['height'] > viewport[1]:
                    print(f"WARNING: Diagram is larger than viewport!")
                    print(f"Consider using --width {int(dimensions['width'] + 100)} --height {int(dimensions['height'] + 100)}")
            
            # Take screenshot
            element = page.query_selector(".mermaid-container")
            if element:
                element.screenshot(path=output_file)
                print(f"✅ Screenshot saved to: {output_file}")
            else:
                raise Exception("Could not find mermaid-container element")
                
        except Exception as e:
            print(f"❌ Error rendering diagram: {e}")
            
            # Save debug screenshot
            debug_file = str(output_file).replace('.png', '_debug.png')
            try:
                page.screenshot(path=debug_file)
                print(f"Debug screenshot saved to: {debug_file}")
            except:
                pass
            
            sys.exit(1)
            
        finally:
            browser.close()


def main() -> None:
    """Main function"""
    # Set up argument parser
    parser = setup_argument_parser()
    
    # Parse arguments
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)
        
    args = parser.parse_args()
    
    # Determine input and output file paths
    input_path = Path(args.input_file)
    
    if not input_path.exists():
        print(f"Error: Input file '{input_path}' does not exist")
        sys.exit(1)
    
    if not input_path.is_file():
        print(f"Error: '{input_path}' is not a file")
        sys.exit(1)
    
    if args.output_file:
        output_path = Path(args.output_file)
    else:
        # Default output path: images directory with same filename as input
        input_filename = input_path.name
        images_dir = input_path.parent.parent / 'images'
        output_path = images_dir / input_filename.replace('.mmd', '.png')
    
    # Create output directory if it doesn't exist
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Render the diagram
    render_mermaid_diagram(
        str(input_path),
        str(output_path),
        viewport=(args.width, args.height),
        debug=args.debug,
        timeout=args.timeout
    )
    
    print("\nConversion complete!")


if __name__ == "__main__":
    main()