/**
 * System-wide configuration constants
 *
 * This file stores system configuration values, such as query limits, batch operation limits, sort orders, etc.
 * Update and maintain these values to control application-wide behavior in a single place.
 */

/**
 * Default query and batch limits
 */
export const DEFAULT_QUERY_LIMITS = {
  /**
   * Default maximum number of records to return from car case queries
   */
  CAR_CASES: 1000,

  /**
   * Default maximum number of records to return from history log queries
   * Used for all history APIs (car code config history, car code alarm config history, etc.)
   */
  HISTORY_LOGS: 2000,

  /**
   * Default maximum number of records to return from config queries
   * Used for car code config and car code alarm config APIs
   */
  CONFIG: 500,

  /**
   * Maximum number of car code alarm configs that can be created in a single batch operation
   */
  CAR_CODE_ALARM_CONFIG_BATCH: 250,
};

/**
 * Default sort order for queries
 */
export enum SORT_ORDER {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Controller source identifiers
 * Used to identify the source of requests in logs and error messages
 */
export enum CONTROLLER_SOURCE {
  GRPC = 'gRPC',
  REST = 'REST',
}

/**
 * Database table names
 * Centralized definition of all table names used in the application
 * Used in entity decorators and for custom SQL queries
 */
export const DB_TABLES = {
  CAR_CASES: 'car_cases',
  CAR_CODE_CONFIG: 'car_code_config',
  CAR_CODE_ALARM_CONFIG: 'car_code_alarm_config',
  CAR_CODE_CONFIG_HISTORY: 'car_code_config_history',
  CAR_CODE_ALARM_CONFIG_HISTORY: 'car_code_alarm_config_history',
};

/**
 * Default sort settings
 */
export const DEFAULT_SORT_SETTINGS = {
  /**
   * Default sort order for car case queries
   */
  CAR_CASES: SORT_ORDER.DESC,

  /**
   * Default sort order for history log queries
   * Used for all history APIs (car code config history, car code alarm config history, etc.)
   */
  HISTORY_LOGS: SORT_ORDER.DESC,

  /**
   * Default sort order for config queries
   * Used for car code config and car code alarm config APIs
   */
  CONFIG: SORT_ORDER.ASC,
};

/**
 * Default start date for date filtering (used in car code config and other modules)
 */
export const DEFAULT_START_DATE = new Date('2000-01-01');
