#!/usr/bin/env python3
"""
Generate dummy data for testing the car-case API.

This script generates realistic test data for the car-case API in gRPC format
for testing with grpcurl.

Usage:
    python generate_car_case_test_data.py [OPTIONS]

Options:
    --count     Number of car cases to generate (default: 10)
    --output    Output file name (default: car_cases_grpc.json)
    --output-dir Output directory (default: output)
    --port      gRPC server port (default: 8081)
"""

import argparse
import json
import random
import datetime
import os
import sys
from typing import List, Dict, Any


# Load the shared test data config
def load_test_data_config():
    """Load the shared test data configuration."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shared_dir = os.path.join(os.path.dirname(script_dir), 'shared')
    config_path = os.path.join(shared_dir, 'test_data_config.json')

    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Shared test data config not found at {config_path}")
        print("This file is required for test data generation.")
        print("Please ensure the file exists before running this script.")
        sys.exit(1)

# Global test data config
TEST_DATA_CONFIG = load_test_data_config()

# Probability of omitting recommendation and metadata fields (0.0 to 1.0)
# 0.25 means 25% chance of being omitted
OMIT_FIELD_PROBABILITY = 0.25

# OMIT_FIELD_PROBABILITY is used to determine whether to include optional fields
# 0.25 means 25% chance of being omitted


def generate_car_cases(count: int) -> List[Dict[str, Any]]:
    """Generate multiple car cases.

    If count is less than or equal to the number of available site-car code assignments,
    uses a random subset of the assignments. Otherwise, reuses assignments with different
    other attributes (sub_site_id, device_id, etc.) to reach the desired count.
    """
    result = []

    # Get all available site-car code combinations
    assignments = TEST_DATA_CONFIG['site_car_code_assignments']

    # If count is less than or equal to the number of assignments, select a random subset
    if count <= len(assignments):
        # Randomly select 'count' assignments for generating car cases
        selected_assignments = random.sample(assignments, count)

        for assignment in selected_assignments:
            # Generate a new case with the specific site_object_id and car_code
            site_object_id = assignment['site_object_id']
            car_code = assignment['car_code']
            car_code_lower = car_code.lower()

            # Generate sub_site_id and device_id
            sub_site_id = f"sub_{random.randint(1, 10):03d}"
            device_id = f"dev_{random.randint(1, 50):03d}"

            # Tags are no longer needed

            # Generate a random date within the last 30 days
            days_ago = random.randint(0, 30)
            logging_date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

            # Create the car case object
            case = {
                "site_object_id": site_object_id,
                "sub_site_object_id": sub_site_id,
                "device_id": device_id,
                "additional_device_info": json.dumps({
                    "device_type": random.choice(["sensor", "controller", "gateway"]),
                    "firmware": f"v{random.randint(1,2)}.{random.randint(0,9)}.{random.randint(0,9)}",
                    "manufacturer": random.choice(["AcmeCorp", "DeviceMakers", "IoTInc"]),
                    "serial_number": f"SN{random.randint(100000,999999)}"
                }),
                "car_code": car_code_lower,
                "source": random.choice(["thermal_os", "solar_os", "wind_os"]),
                "title": f"Case: {car_code_lower} detected at {site_object_id}",
                "description": f"Test case for {car_code_lower} issue on {site_object_id} with monitoring period of {TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period']} days",
                "logging_date": logging_date,
                "remarks": f"Test remarks for {car_code_lower} at {site_object_id}"
            }

            # Generate recommendation and metadata as in the original function
            if random.random() >= OMIT_FIELD_PROBABILITY:
                action_values = {
                    "AMD": "Check inverter connections and reset",
                    "APD": "Inspect power distribution system",
                    "APR": "Review recent performance data",
                    "CLD": "Clean panels and check for debris",
                    "DRS": "Diagnose and repair sensor",
                    "HDG": "Verify hardware configuration",
                    "HSH": "Inspect for physical damage",
                    "HSO": "Optimize system settings",
                    "HTI": "Test inverter functionality",
                    "HYM": "Monitor system for 24 hours"
                }

                action_value = action_values.get(car_code, "Perform site inspection")

                case["recommendation"] = json.dumps({
                    "action": action_value
                })

            if random.random() >= OMIT_FIELD_PROBABILITY:
                metadata_details = {
                    "daily_energy_loss": str(random.randint(10, 500)),
                    "monitoring_period": str(TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period'])
                }

                # Add some additional metadata based on car code
                if car_code_lower in ["amd", "apd"]:
                    metadata_details["error_code"] = f"E-{random.randint(100, 999)}"
                elif car_code_lower in ["hsh", "hso"]:
                    metadata_details["damage_type"] = random.choice(["crack", "discoloration", "delamination"])
                elif car_code_lower in ["hti", "hym"]:
                    metadata_details["severity"] = random.choice(["low", "medium", "high"])
                elif car_code_lower in ["cld", "drs"]:
                    metadata_details["affected_area"] = f"{random.randint(1, 100)} sq.m"

                case["metadata"] = json.dumps(metadata_details)

            result.append(case)
    else:
        # First use all available assignments
        for assignment in assignments:
            # Generate a new case with the specific site_object_id and car_code
            site_object_id = assignment['site_object_id']
            car_code = assignment['car_code']
            car_code_lower = car_code.lower()

            # Generate sub_site_id and device_id
            sub_site_id = f"sub_{random.randint(1, 10):03d}"
            device_id = f"dev_{random.randint(1, 50):03d}"

            # Tags are no longer needed

            # Generate a random date within the last 30 days
            days_ago = random.randint(0, 30)
            logging_date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

            # Create the car case object
            case = {
                "site_object_id": site_object_id,
                "sub_site_object_id": sub_site_id,
                "device_id": device_id,
                "car_code": car_code_lower,
                "source": random.choice(["thermal_os", "solar_os", "wind_os"]),
                "title": f"Case: {car_code_lower} detected at {site_object_id}",
                "description": f"Test case for {car_code_lower} issue on {site_object_id} with monitoring period of {TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period']} days",
                "logging_date": logging_date
            }

            # Generate recommendation and metadata as in the original function
            if random.random() >= OMIT_FIELD_PROBABILITY:
                action_values = {
                    "AMD": "Check inverter connections and reset",
                    "APD": "Inspect power distribution system",
                    "APR": "Review recent performance data",
                    "CLD": "Clean panels and check for debris",
                    "DRS": "Diagnose and repair sensor",
                    "HDG": "Verify hardware configuration",
                    "HSH": "Inspect for physical damage",
                    "HSO": "Optimize system settings",
                    "HTI": "Test inverter functionality",
                    "HYM": "Monitor system for 24 hours"
                }

                action_value = action_values.get(car_code, "Perform site inspection")

                case["recommendation"] = json.dumps({
                    "action": action_value
                })

            if random.random() >= OMIT_FIELD_PROBABILITY:
                metadata_details = {
                    "daily_energy_loss": str(random.randint(10, 500)),
                    "monitoring_period": str(TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period'])
                }

                # Add some additional metadata based on car code
                if car_code_lower in ["amd", "apd"]:
                    metadata_details["error_code"] = f"E-{random.randint(100, 999)}"
                elif car_code_lower in ["hsh", "hso"]:
                    metadata_details["damage_type"] = random.choice(["crack", "discoloration", "delamination"])
                elif car_code_lower in ["hti", "hym"]:
                    metadata_details["severity"] = random.choice(["low", "medium", "high"])
                elif car_code_lower in ["cld", "drs"]:
                    metadata_details["affected_area"] = f"{random.randint(1, 100)} sq.m"

                case["metadata"] = json.dumps(metadata_details)

            result.append(case)

        # Then generate additional cases by reusing assignments with different other attributes
        remaining = count - len(assignments)

        while remaining > 0:
            # Cycle through assignments
            for assignment in assignments:
                if remaining <= 0:
                    break

                # Generate a new case with the specific site_object_id and car_code
                site_object_id = assignment['site_object_id']
                car_code = assignment['car_code']
                car_code_lower = car_code.lower()

                # Generate sub_site_id and device_id
                sub_site_id = f"sub_{random.randint(1, 10):03d}"
                device_id = f"dev_{random.randint(1, 50):03d}"

                # Tags are no longer needed

                # Generate a random date within the last 30 days
                days_ago = random.randint(0, 30)
                logging_date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

                # Create the car case object
                case = {
                    "site_object_id": site_object_id,
                    "sub_site_object_id": sub_site_id,
                    "device_id": device_id,
                    "additional_device_info": json.dumps({
                        "device_type": random.choice(["sensor", "controller", "gateway"]),
                        "firmware": f"v{random.randint(1,2)}.{random.randint(0,9)}.{random.randint(0,9)}",
                        "manufacturer": random.choice(["AcmeCorp", "DeviceMakers", "IoTInc"]),
                        "serial_number": f"SN{random.randint(100000,999999)}"
                    }),
                    "car_code": car_code_lower,
                    "source": random.choice(["thermal_os", "solar_os", "wind_os"]),
                    "title": f"Case: {car_code_lower} detected at {site_object_id}",
                    "description": f"Test case for {car_code_lower} issue on {site_object_id} with monitoring period of {TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period']} days",
                    "logging_date": logging_date,
                    "remarks": f"Test remarks for {car_code_lower} at {site_object_id}"
                }

                # Generate recommendation and metadata as in the original function
                if random.random() >= OMIT_FIELD_PROBABILITY:
                    action_values = {
                        "AMD": "Check inverter connections and reset",
                        "APD": "Inspect power distribution system",
                        "APR": "Review recent performance data",
                        "CLD": "Clean panels and check for debris",
                        "DRS": "Diagnose and repair sensor",
                        "HDG": "Verify hardware configuration",
                        "HSH": "Inspect for physical damage",
                        "HSO": "Optimize system settings",
                        "HTI": "Test inverter functionality",
                        "HYM": "Monitor system for 24 hours"
                    }

                    action_value = action_values.get(car_code, "Perform site inspection")

                    case["recommendation"] = json.dumps({
                        "action": action_value
                    })

                if random.random() >= OMIT_FIELD_PROBABILITY:
                    metadata_details = {
                        "daily_energy_loss": str(random.randint(10, 500)),
                        "monitoring_period": str(TEST_DATA_CONFIG['car_codes'][car_code]['monitoring_period'])
                    }

                    # Add some additional metadata based on car code
                    if car_code_lower in ["amd", "apd"]:
                        metadata_details["error_code"] = f"E-{random.randint(100, 999)}"
                    elif car_code_lower in ["hsh", "hso"]:
                        metadata_details["damage_type"] = random.choice(["crack", "discoloration", "delamination"])
                    elif car_code_lower in ["hti", "hym"]:
                        metadata_details["severity"] = random.choice(["low", "medium", "high"])
                    elif car_code_lower in ["cld", "drs"]:
                        metadata_details["affected_area"] = f"{random.randint(1, 100)} sq.m"

                    case["metadata"] = json.dumps(metadata_details)

                result.append(case)
                remaining -= 1

    return result



    """Save car cases in a format suitable for grpcurl."""
    # Format matching the CreateCarCaseDAORequest interface
    grpc_format = {"requests": car_cases}
    with open(output_file, 'w') as f:
        json.dump(grpc_format, f, indent=2)

    # Also create a .txt file with the grpcurl command
    command_file = output_file.replace('.json', '_grpcurl_command.txt')
    with open(command_file, 'w') as f:
        # Use the path to grpcurl.exe from the user's memories
        f.write(f"C:\\Users\\<USER>\\Documents\\tools\\grpcurl\\grpcurl.exe -proto src/proto/car-wrapper-service.proto -d @{output_file} -plaintext localhost:{port} car_wrapper_service.CarWrapperService/CreateCarCase\n")

    print(f"Generated {len(car_cases)} car cases in gRPC format: {output_file}")
    print(f"Generated grpcurl command in: {command_file}")


def main():
    parser = argparse.ArgumentParser(description='Generate dummy data for car-case API testing')
    parser.add_argument('--count', type=int, default=10, help='Number of car cases to generate')
    parser.add_argument('--output', type=str, default='car_cases_grpc.json', help='Output file name')
    parser.add_argument('--output-dir', type=str, default='output',
                        help='Output directory (default: output)')

    # Field omission probability is configured via the OMIT_FIELD_PROBABILITY variable at the top of the file

    args = parser.parse_args()

    # Create output directory with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, timestamp)
    os.makedirs(output_path, exist_ok=True)

    print(f"Output directory: {output_path}")

    # Generate car cases
    car_cases = generate_car_cases(args.count)

    # Generate gRPC format data
    output_file = os.path.join(output_path, args.output)
    print(f"Generating test data ({args.count} records)...")
    with open(output_file, 'w') as f:
        json.dump(car_cases, f, indent=2)
    print(f"\nDone! Test data file has been created in {output_path}")
    print(f"- {output_file}")


if __name__ == "__main__":
    main()
