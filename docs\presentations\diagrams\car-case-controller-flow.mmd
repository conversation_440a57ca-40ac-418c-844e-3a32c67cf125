sequenceDiagram
    title Car Case Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCaseController
    participant CarCaseConsoService
    participant CarCaseService
    participant EntityManager
    participant CarCaseRepository
    participant CarCodeConfigRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCaseService,CarCaseConsoService: Both services are independent and directly injected into the controller.
    Note over CarCaseConsoService: <PERSON><PERSON> consolidated views with direct database access.
    Note over CarCaseService: <PERSON>les car case creation and management.

    %% Main Flows
    
    %% 1. Health Check Flow
    Client->>CarCaseController: PingCarCase()
    CarCaseController-->>Client: PingResponse

    %% 2. Find Car Cases Flow
    Client->>CarCaseController: FindCarCases(request)
    Note over CarCaseController: Normalize request parameters
    CarCaseController->>CarCaseConsoService: findCarCases(request)
    CarCaseConsoService->>CarCaseConsoService: buildFindCarCasesQuery(filters, limit)
    CarCaseConsoService->>CarCaseConsoService: initializeBaseQuery()
    CarCaseConsoService->>CarCaseConsoService: applyFiltersToQuery(query, filters)
    CarCaseConsoService->>CarCaseConsoService: finalizeSortingAndLimit(query, limit, filters)
    CarCaseConsoService->>EntityManager: query(sql, parameters)
    EntityManager->>Database: Execute SQL Query
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    CarCaseController-->>Client: CarCasesResponse

    %% 3. Find Car Cases By IDs Flow
    Client->>CarCaseController: FindCarCasesByIds(request)
    Note over CarCaseController: Extract IDs and normalize sort order
    CarCaseController->>CarCaseConsoService: findCarCasesByIds(ids, sortOrder)
    CarCaseConsoService->>EntityManager: query(sql with placeholders, ids)
    EntityManager->>Database: Execute SQL Query with ID parameters
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    CarCaseController-->>Client: CarCasesResponse

    %% 4. Create Car Case Flow
    Client->>CarCaseController: CreateCarCase(request)
    Note over CarCaseController: Validate request parameters
    CarCaseController->>CarCaseService: createCarCase(requests)
    
    %% Car Case Service Processing
    CarCaseService->>CarCaseService: checkCarCodeConfig(carCaseDAOList)
    CarCaseService->>CarCodeConfigRepository: find({car_code, site_object_id})
    CarCodeConfigRepository->>Database: SELECT * FROM CAR_CODE_CONFIG
    Database-->>CarCodeConfigRepository: Car Code Configs
    CarCodeConfigRepository-->>CarCaseService: Car Code Configs
    
    %% Process Each Car Case
    loop For each car case
        CarCaseService->>CarCaseService: generateTags(carCase)
        CarCaseService->>CarCaseService: determineCase(tags, monitoringWindow, caseIdMap)
        
        %% Check for existing cases
        alt Check in-memory cache
            Note right of CarCaseService: In-memory cache prevents duplicate case IDs for the same incident
            Note right of CarCaseService: If multiple incidents with identical tags are processed in the same batch,
            Note right of CarCaseService: they will be grouped under the same case ID rather than creating separate cases
            CarCaseService->>CarCaseService: checkInMemoryCache(tags, inMemoryCache)
        else Check database
            CarCaseService->>CarCaseService: findMostRecentCaseByTags(tags)
            CarCaseService->>CarCaseRepository: findOne({tags}, {order})
            CarCaseRepository->>Database: SELECT * FROM CAR_CASES WHERE tags = ?
            Database-->>CarCaseRepository: Most Recent Case
            CarCaseRepository-->>CarCaseService: Most Recent Case
            CarCaseService->>CarCaseService: isWithinMonitoringWindow(date, window)
        end
        
        %% Save Car Case
        CarCaseService->>CarCaseService: saveCarCaseDAO(carCase, index, totalCount)
        CarCaseService->>CarCaseRepository: save(carCaseEntity)
        CarCaseRepository->>Database: INSERT INTO CAR_CASES
        Database-->>CarCaseRepository: Saved Entity
        CarCaseRepository-->>CarCaseService: Saved Entity with ID
    end
    
    CarCaseService-->>CarCaseController: ServiceResponse(CarCaseDAO array)
    
    %% Fetch Consolidated Data for Created Cases
    CarCaseController->>CarCaseConsoService: findCarCasesByIds(caseIds)
    CarCaseConsoService->>EntityManager: query(sql with placeholders, ids)
    EntityManager->>Database: Execute SQL Query
    Database-->>EntityManager: Raw Results
    EntityManager-->>CarCaseConsoService: Raw Results
    CarCaseConsoService->>CarCaseConsoService: mapResultsToDTO(results)
    CarCaseConsoService-->>CarCaseController: ServiceResponse(CarCaseConsoDAO array)
    
    CarCaseController-->>Client: CarCasesResponse
