#!/bin/bash
# Shell script to generate presentation with Mermaid support

# Navigate to the presentations directory
cd "$(dirname "$0")"

# Create output directory if it doesn't exist
if [ ! -d "output" ]; then
    echo "Creating output directory..."
    mkdir -p output
fi

# Create output/images directory for copied images
if [ ! -d "output/images" ]; then
    echo "Creating output/images directory..."
    mkdir -p output/images
fi

# Check if Marp CLI is installed
if ! command -v marp &> /dev/null; then
    echo "Marp CLI is not installed. Installing it now..."
    npm install -g @marp-team/marp-cli
fi

# Check if images directory exists, create if not
if [ ! -d "images" ]; then
    echo "Creating images directory..."
    mkdir -p images
fi

# Copy all images to the output directory to ensure they're available
echo "Copying images to output directory..."
cp -r images/* output/images/ 2>/dev/null || echo "No images to copy or directory not found."

# Generate HTML presentation with Mermaid support
echo "Generating HTML presentation..."
marp --html --allow-local-files car-wrapper-features.md -o output/car-wrapper-features.html

# Generate PDF presentation
echo "Generating PDF presentation..."
marp --html --pdf --allow-local-files car-wrapper-features.md -o output/car-wrapper-features.pdf

# Generate PowerPoint presentation
echo "Generating PowerPoint presentation..."
if command -v marp &> /dev/null; then
    marp --html --pptx --allow-local-files car-wrapper-features.md -o output/car-wrapper-features.pptx
    echo "PowerPoint presentation generated successfully."
else
    echo "Failed to generate PowerPoint presentation. Make sure Marp CLI is installed correctly."
fi

echo "Done! Presentation files are in the 'docs/presentations/output' directory."
echo "HTML: docs/presentations/output/car-wrapper-features.html"
echo "PDF: docs/presentations/output/car-wrapper-features.pdf"
echo "PPTX: docs/presentations/output/car-wrapper-features.pptx"
