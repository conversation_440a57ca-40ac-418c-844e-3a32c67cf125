/**
 * Unit tests for the CarCodeConfigController
 *
 * These tests verify that the controller correctly delegates to the service
 * and returns the expected responses. The service is mocked to isolate the controller.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { CarCodeConfigController } from './car-code-config.controller';
import { CarCodeConfigService } from './car-code-config.service';
import {
  CreateCarCodeConfigsRequest,
  FindCarCodeConfigsRequest,
  UpdateCarCodeConfigRequest,
  DeleteCarCodeConfigRequest,
  CarCodeConfigDAO,
} from './interfaces';
import { AclService } from '../client/acl/acl.service';
import { AclGuard } from '../client/acl/acl.guard';

describe('CarCodeConfigController', () => {
  let controller: CarCodeConfigController;
  let service: CarCodeConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CarCodeConfigController],
      providers: [
        {
          provide: CarCodeConfigService,
          useValue: {
            createCarCodeConfig: jest.fn(),
            findCarCodeConfigs: jest.fn(),
            findCarCodeConfigById: jest.fn(),
            updateCarCodeConfig: jest.fn(),
            deleteCarCodeConfig: jest.fn(),
          },
        },
        {
          provide: AclService,
          useValue: {},
        },
        {
          provide: AclGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
      ],
    }).compile();

    controller = module.get<CarCodeConfigController>(CarCodeConfigController);
    service = module.get<CarCodeConfigService>(CarCodeConfigService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /**
   * Test Data
   *
   * These mock objects are used throughout the tests to simulate
   * request objects and service responses.
   */
  const mockCarCodeConfigDAO: CarCodeConfigDAO = {
    car_code: 'TEST-001',
    site_object_id: 'SITE-001',
    monitoring_window: 5,
    enabled: true,
    thresholds: JSON.stringify([
      {
        name: 'temperature',
        unit: 'kWh',
        value: '46.17',
        severity: 'high',
        duration: '30 minutes',
        condition: '>=',
        alert_type: 'email',
        escalation_path: 'operations_team',
      },
      {
        name: 'temperature',
        unit: '%',
        value: '98.65',
        severity: 'critical',
        duration: '15 minutes',
        condition: '>',
        alert_type: 'sms',
        escalation_path: 'engineering_team',
      },
    ]),
    recommendation: JSON.stringify([
      {
        name: 'diagnose and repair sensor',
        man_hour: '4',
        num_of_man: '2',
        equipment_cost: '0',
        priority: 'high',
        required_certifications: ['electrical', 'mechanical'],
        estimated_downtime: '2 hours',
        safety_requirements: ['ppe', 'lockout_tagout'],
        follow_up_actions: ['verify_repair', 'retest_system'],
      },
    ]),
    updated_at: new Date().toISOString(),
    updated_by: 'test-user',
  };

  const mockServiceSuccessResponse = {
    success: true,
    data: [mockCarCodeConfigDAO],
  };

  // Mock response for general success (200 OK)
  const mockControllerSuccessResponse = {
    success: {
      status: 'success',
      code: 200,
      message: [mockCarCodeConfigDAO],
    },
  };

  // Mock response for creation success (201 Created)
  const mockControllerCreatedResponse = {
    success: {
      status: 'success',
      code: 201,
      message: [mockCarCodeConfigDAO],
    },
  };

  describe('pingCarCodeConfig', () => {
    it('should return a ping response', () => {
      const result = controller.pingCarCodeConfig();
      expect(result).toEqual({ message: 'Car Code Config Service is running' });
    });
  });

  describe('createCarCodeConfig', () => {
    it('should call service.createCarCodeConfig with the request data (1 entry)', async () => {
      // Mock service method
      jest
        .spyOn(service, 'createCarCodeConfig')
        .mockResolvedValue(mockServiceSuccessResponse);

      // Create request object
      const request: CreateCarCodeConfigsRequest = {
        requests: [mockCarCodeConfigDAO],
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.createCarCodeConfig(request);

      // Verify the result
      expect(result).toEqual(mockControllerCreatedResponse);

      // Verify service method was called with the correct arguments
      // Use a bound method check to avoid ESLint unbound-method warning
      const createSpy = jest.spyOn(service, 'createCarCodeConfig');
      expect(createSpy).toHaveBeenCalledWith(request.requests);
    });

    it('should call service.createCarCodeConfig with the request data (multiple entries)', async () => {
      // Create multiple mock car code config objects
      const mockCarCodeConfigDAO2: CarCodeConfigDAO = {
        car_code: 'TEST-002',
        site_object_id: 'SITE-002',
        monitoring_window: 10,
        enabled: true,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockCarCodeConfigDAO3: CarCodeConfigDAO = {
        car_code: 'TEST-003',
        site_object_id: 'SITE-003',
        monitoring_window: 15,
        enabled: false,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      // Mock service response for multiple entries
      const mockMultipleServiceResponse = {
        success: true,
        data: [
          mockCarCodeConfigDAO,
          mockCarCodeConfigDAO2,
          mockCarCodeConfigDAO3,
        ],
      };

      // Mock controller response for multiple entries
      const mockMultipleControllerResponse = {
        success: {
          status: 'success',
          code: 201,
          message: [
            mockCarCodeConfigDAO,
            mockCarCodeConfigDAO2,
            mockCarCodeConfigDAO3,
          ],
        },
      };

      // Mock service method
      jest
        .spyOn(service, 'createCarCodeConfig')
        .mockResolvedValue(mockMultipleServiceResponse);

      // Create request object with multiple entries
      const request: CreateCarCodeConfigsRequest = {
        requests: [
          mockCarCodeConfigDAO,
          mockCarCodeConfigDAO2,
          mockCarCodeConfigDAO3,
        ],
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.createCarCodeConfig(request);

      // Verify the result
      expect(result).toEqual(mockMultipleControllerResponse);

      // Verify service method was called with the correct arguments
      const createSpy = jest.spyOn(service, 'createCarCodeConfig');
      expect(createSpy).toHaveBeenCalledWith([
        mockCarCodeConfigDAO,
        mockCarCodeConfigDAO2,
        mockCarCodeConfigDAO3,
      ]);
    });

    it('should format service errors correctly in createCarCodeConfig', async () => {
      // Mock service to return an error
      jest.spyOn(service, 'createCarCodeConfig').mockResolvedValue({
        success: false,
        error: 'Database constraint violation',
        errors: ['Duplicate entry'],
      });

      // Create request
      const request: CreateCarCodeConfigsRequest = {
        requests: [mockCarCodeConfigDAO],
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.createCarCodeConfig(request);

      // Verify error response format
      expect(result).toHaveProperty('error');
      expect(result.error?.status).toContain('ERROR: Bad Request');
      expect(result.error?.code).toBe(400);
      // The controller might transform the error messages, so just check that it's an array
      expect(Array.isArray(result.error?.message)).toBe(true);
      expect(result.error?.error).toBe('Database constraint violation');
    });
  });

  describe('findCarCodeConfigs', () => {
    it('should call service.findCarCodeConfigs with the request data', async () => {
      // Mock service method
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [mockCarCodeConfigDAO],
      });

      // Create request object
      const request: FindCarCodeConfigsRequest = {
        car_code: 'TEST-001',
        site_object_id: 'SITE-001',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.findCarCodeConfigs(request);

      // Verify the result
      expect(result).toEqual(mockControllerSuccessResponse);

      // Verify service method was called with the correct arguments
      // Use a bound method check to avoid ESLint unbound-method warning
      const findSpy = jest.spyOn(service, 'findCarCodeConfigs');
      expect(findSpy).toHaveBeenCalledWith(request);
    });

    it('should verify that service is called with correct sort parameters', async () => {
      // Mock service method
      const serviceSpy = jest
        .spyOn(service, 'findCarCodeConfigs')
        .mockResolvedValue({
          success: true,
          data: [mockCarCodeConfigDAO],
        });

      // Create request object with ASC sort order
      const ascRequest: FindCarCodeConfigsRequest = {
        sort_order: 'ASC',
        user_id: 'test-user',
      };

      // Call the controller method
      await controller.findCarCodeConfigs(ascRequest);

      // Verify service was called with ASC sort order
      expect(serviceSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_order: 'ASC',
        }),
      );

      // Reset the mock
      serviceSpy.mockClear();

      // Create request object with DESC sort order
      const descRequest: FindCarCodeConfigsRequest = {
        sort_order: 'DESC',
        user_id: 'test-user',
      };

      // Call the controller method
      await controller.findCarCodeConfigs(descRequest);

      // Verify service was called with DESC sort order
      expect(serviceSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          sort_order: 'DESC',
        }),
      );
    });

    it('should handle empty request in findCarCodeConfigs', async () => {
      // Mock service method
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [],
      });

      // Call the controller method with empty request object
      // Cast to FindCarCodeConfigsRequest to satisfy TypeScript
      const result = await controller.findCarCodeConfigs({
        user_id: 'test-user',
      } as FindCarCodeConfigsRequest);

      // Verify service was called - the controller passes the request directly
      const findSpy = jest.spyOn(service, 'findCarCodeConfigs');
      expect(findSpy).toHaveBeenCalled();

      // Verify successful response
      expect(result).toHaveProperty('success');
      expect(result.success?.code).toBe(200);
    });

    it('should return car code configs sorted by car_code in the specified order', async () => {
      // Create multiple mock car code config objects with different car_codes
      const mockConfig1: CarCodeConfigDAO = {
        car_code: 'AAA-001',
        site_object_id: 'SITE-001',
        monitoring_window: 5,
        enabled: true,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig2: CarCodeConfigDAO = {
        car_code: 'BBB-002',
        site_object_id: 'SITE-002',
        monitoring_window: 10,
        enabled: true,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig3: CarCodeConfigDAO = {
        car_code: 'CCC-003',
        site_object_id: 'SITE-003',
        monitoring_window: 15,
        enabled: false,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      // Mock service response with configs in ascending order by car_code
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig1, mockConfig2, mockConfig3],
      });

      // Create request object with ASC sort order
      const request: FindCarCodeConfigsRequest = {
        sort_order: 'ASC',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.findCarCodeConfigs(request);

      // Verify the result has configs in the correct order
      expect(result).toHaveProperty('success');
      expect(result.success?.message).toHaveLength(3);
      expect(result.success?.message[0].car_code).toBe('AAA-001');
      expect(result.success?.message[1].car_code).toBe('BBB-002');
      expect(result.success?.message[2].car_code).toBe('CCC-003');

      // Now test with descending order
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig3, mockConfig2, mockConfig1],
      });

      // Create request object with DESC sort order
      const descRequest: FindCarCodeConfigsRequest = {
        sort_order: 'DESC',
        user_id: 'test-user',
      };

      // Call the controller method
      const descResult = await controller.findCarCodeConfigs(descRequest);

      // Verify the result has configs in the correct order
      expect(descResult).toHaveProperty('success');
      expect(descResult.success?.message).toHaveLength(3);
      expect(descResult.success?.message[0].car_code).toBe('CCC-003');
      expect(descResult.success?.message[1].car_code).toBe('BBB-002');
      expect(descResult.success?.message[2].car_code).toBe('AAA-001');
    });

    it('should sort by site_object_id as secondary sort field when car_codes are the same', async () => {
      // Create multiple mock car code config objects with the same car_code but different site_object_ids
      const mockConfig1: CarCodeConfigDAO = {
        car_code: 'AAA-001',
        site_object_id: 'SITE-001',
        monitoring_window: 5,
        enabled: true,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig2: CarCodeConfigDAO = {
        car_code: 'AAA-001',
        site_object_id: 'SITE-002',
        monitoring_window: 10,
        enabled: true,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      const mockConfig3: CarCodeConfigDAO = {
        car_code: 'AAA-001',
        site_object_id: 'SITE-003',
        monitoring_window: 15,
        enabled: false,
        thresholds: JSON.stringify([
          {
            name: 'temperature',
            unit: 'kWh',
            value: '46.17',
            severity: 'high',
            duration: '30 minutes',
            condition: '>=',
            alert_type: 'email',
            escalation_path: 'operations_team',
          },
          {
            name: 'temperature',
            unit: '%',
            value: '98.65',
            severity: 'critical',
            duration: '15 minutes',
            condition: '>',
            alert_type: 'sms',
            escalation_path: 'engineering_team',
          },
        ]),
        recommendation: JSON.stringify([
          {
            name: 'diagnose and repair sensor',
            man_hour: '4',
            num_of_man: '2',
            equipment_cost: '0',
            priority: 'high',
            required_certifications: ['electrical', 'mechanical'],
            estimated_downtime: '2 hours',
            safety_requirements: ['ppe', 'lockout_tagout'],
            follow_up_actions: ['verify_repair', 'retest_system'],
          },
        ]),
        updated_at: new Date().toISOString(),
        updated_by: 'test-user',
      };

      // Mock service response with configs in ascending order by site_object_id (secondary sort)
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig1, mockConfig2, mockConfig3],
      });

      // Create request object with ASC sort order
      const request: FindCarCodeConfigsRequest = {
        sort_order: 'ASC',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.findCarCodeConfigs(request);

      // Verify the result has configs in the correct order by site_object_id
      expect(result).toHaveProperty('success');
      expect(result.success?.message).toHaveLength(3);
      expect(result.success?.message[0].site_object_id).toBe('SITE-001');
      expect(result.success?.message[1].site_object_id).toBe('SITE-002');
      expect(result.success?.message[2].site_object_id).toBe('SITE-003');

      // Now test with descending order
      jest.spyOn(service, 'findCarCodeConfigs').mockResolvedValue({
        success: true,
        data: [mockConfig3, mockConfig2, mockConfig1],
      });

      // Create request object with DESC sort order
      const descRequest: FindCarCodeConfigsRequest = {
        sort_order: 'DESC',
        user_id: 'test-user',
      };

      // Call the controller method
      const descResult = await controller.findCarCodeConfigs(descRequest);

      // Verify the result has configs in the correct order by site_object_id (descending)
      expect(descResult).toHaveProperty('success');
      expect(descResult.success?.message).toHaveLength(3);
      expect(descResult.success?.message[0].site_object_id).toBe('SITE-003');
      expect(descResult.success?.message[1].site_object_id).toBe('SITE-002');
      expect(descResult.success?.message[2].site_object_id).toBe('SITE-001');
    });
  });

  // Note: findCarCodeConfigById method doesn't exist in the controller
  // This functionality is handled by findCarCodeConfigs with specific filters

  describe('updateCarCodeConfig', () => {
    it('should call service.updateCarCodeConfig with the request data and handle success', async () => {
      // Mock service method
      jest
        .spyOn(service, 'updateCarCodeConfig')
        .mockResolvedValue(mockServiceSuccessResponse);

      // Create request object
      const request: UpdateCarCodeConfigRequest = {
        request: {
          car_code: 'TEST-001',
          site_object_id: 'SITE-001',
          monitoring_window: 10,
          enabled: true,
          updated_at: new Date().toISOString(),
          updated_by: 'test-user',
        },
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.updateCarCodeConfig(request);

      // Verify the result
      expect(result).toEqual(mockControllerSuccessResponse);

      // Verify service method was called with the correct arguments
      const updateSpy = jest.spyOn(service, 'updateCarCodeConfig');
      expect(updateSpy).toHaveBeenCalledWith(request.request);
    });

    it('should handle not found errors from the service', async () => {
      // Mock service method to return not found error
      jest.spyOn(service, 'updateCarCodeConfig').mockResolvedValue({
        success: false,
        error: 'Car code configuration not found.',
        errors: ['Car code TEST-001 with site SITE-001 not found'],
      });

      // Create request object
      const request: UpdateCarCodeConfigRequest = {
        request: {
          car_code: 'TEST-001',
          site_object_id: 'SITE-001',
          monitoring_window: 5,
          enabled: true,
          thresholds: JSON.stringify([
            {
              name: 'temperature',
              unit: 'kWh',
              value: '46.17',
              severity: 'high',
              duration: '30 minutes',
              condition: '>=',
              alert_type: 'email',
              escalation_path: 'operations_team',
            },
            {
              name: 'temperature',
              unit: '%',
              value: '98.65',
              severity: 'critical',
              duration: '15 minutes',
              condition: '>',
              alert_type: 'sms',
              escalation_path: 'engineering_team',
            },
          ]),
          recommendation: JSON.stringify([
            {
              name: 'diagnose and repair sensor',
              man_hour: '4',
              num_of_man: '2',
              equipment_cost: '0',
              priority: 'high',
              required_certifications: ['electrical', 'mechanical'],
              estimated_downtime: '2 hours',
              safety_requirements: ['ppe', 'lockout_tagout'],
              follow_up_actions: ['verify_repair', 'retest_system'],
            },
          ]),
          updated_by: 'test-user',
        },
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.updateCarCodeConfig(request);

      // Verify the error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404,
          message: ['Car code TEST-001 with site SITE-001 not found'],
          fields: ['car_code'],
          error: 'Car code configuration not found.',
        },
      });
    });

    it('should handle other errors from the service', async () => {
      // Mock service method to return other error
      jest.spyOn(service, 'updateCarCodeConfig').mockResolvedValue({
        success: false,
        error: 'Error updating car code configuration',
      });

      // Create request object
      const request: UpdateCarCodeConfigRequest = {
        request: {
          car_code: 'TEST-001',
          site_object_id: 'SITE-001',
          monitoring_window: 10,
          enabled: true,
          updated_by: 'test-user',
        },
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.updateCarCodeConfig(request);

      // Verify the error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Bad Request - client-provided data caused the error',
          code: 400,
          message: [],
          fields: [],
          error: 'Error updating car code configuration',
        },
      });
    });

    it('should validate missing updated_by in updateCarCodeConfig', async () => {
      // Create request with missing updated_by
      const request: UpdateCarCodeConfigRequest = {
        request: {
          car_code: 'TEST-001',
          site_object_id: 'SITE-001',
          monitoring_window: 10,
          enabled: true,
          // updated_by is missing
        },
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.updateCarCodeConfig(request);

      // Verify error response
      expect(result).toHaveProperty('error');
      expect(result.error?.code).toBe(422);
      expect(result.error?.fields).toContain('request.updated_by');

      // Verify service method was not called
      const updateSpy = jest.spyOn(service, 'updateCarCodeConfig');
      expect(updateSpy).not.toHaveBeenCalled();
    });
  });

  describe('deleteCarCodeConfig', () => {
    it('should call service.deleteCarCodeConfig with the request data and handle success', async () => {
      // Mock service method
      jest.spyOn(service, 'deleteCarCodeConfig').mockResolvedValue({
        success: true,
        data: [{ message: 'Car code config deleted successfully' }],
      });

      // Create request object
      const request: DeleteCarCodeConfigRequest = {
        car_code: 'TEST-001',
        site_object_id: 'SITE-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.deleteCarCodeConfig(request);

      // Verify the result
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [],
        },
      });

      // Verify service method was called with the correct arguments
      const deleteSpy = jest.spyOn(service, 'deleteCarCodeConfig');
      expect(deleteSpy).toHaveBeenCalledWith(
        request.car_code,
        request.site_object_id,
        'system', // Default value
      );
    });

    it('should handle not found errors from the service', async () => {
      // Mock service method to return not found error
      jest.spyOn(service, 'deleteCarCodeConfig').mockResolvedValue({
        success: false,
        error: 'Car code configuration not found.',
        errors: ['Car code NONEXISTENT with site SITE-001 not found'],
      });

      // Create request object
      const request: DeleteCarCodeConfigRequest = {
        car_code: 'NONEXISTENT',
        site_object_id: 'SITE-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.deleteCarCodeConfig(request);

      // Verify the error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404,
          message: ['Car code NONEXISTENT with site SITE-001 not found'],
          fields: ['car_code', 'site_object_id'],
          error: 'Car code configuration not found.',
        },
      });
    });

    it('should handle other errors from the service', async () => {
      // Mock service method to return other error
      jest.spyOn(service, 'deleteCarCodeConfig').mockResolvedValue({
        success: false,
        error: 'Error deleting car code configuration',
      });

      // Create request object
      const request: DeleteCarCodeConfigRequest = {
        car_code: 'TEST-001',
        site_object_id: 'SITE-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.deleteCarCodeConfig(request);

      // Verify the error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Bad Request - client-provided data caused the error',
          code: 400,
          message: [],
          fields: ['car_code', 'site_object_id'],
          error: 'Error deleting car code configuration',
        },
      });
    });

    it('should validate required fields', async () => {
      // Create request object with missing fields
      const request: DeleteCarCodeConfigRequest = {
        car_code: '',
        site_object_id: 'SITE-001',
        updated_by: 'test-user',
        user_id: 'test-user',
      };

      // Call the controller method
      const result = await controller.deleteCarCodeConfig(request);

      // Verify the error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Unprocessable Entity - semantic errors',
          code: 422,
          message: [],
          fields: ['car_code'],
          error: 'The car_code fields are required.',
        },
      });
    });
  });
});
