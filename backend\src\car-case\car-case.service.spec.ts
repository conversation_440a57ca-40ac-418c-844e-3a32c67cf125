/**
 * Unit tests for the CarCaseService
 *
 * These tests verify the business logic of the CarCaseService in isolation
 * by mocking the database repositories.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CarCaseService } from './car-case.service';
import { CarCase } from './entities';
// CarCodeDefinition will be implemented later
import { CarCodeConfig } from '../car-code-config/entities';
import { CarCaseDAO } from './interfaces';

/**
 * Helper function to create a mock CarCase with all required properties
 */
function createMockCarCase(partial: Partial<CarCase> = {}): CarCase {
  return {
    id: '123e4567-e89b-12d3-a456-426614174000',
    site_object_id: 'SITE-001',
    sub_site_object_id: 'SUB-001',
    device_id: 'DEV-001',
    additional_device_info: { info: 'INFO-001' },
    tags: 'TEST-001-SITE-001-SUB-001-DEV-001',
    case_id: 'CASE-1234567890',
    car_code: 'TEST-001',
    source: 'test',
    title: 'Test Case',
    description: 'Test description',
    recommendation: { action: 'Test recommendation' },
    metadata: { daily_energy_loss: '100', key: 'value' },
    logging_date: new Date('2009-02-12T07:31:30Z'),
    carCodeConfig: {} as CarCodeConfig,
    // CarCodeDefinition will be implemented later
    ...partial,
  } as CarCase;
}

/**
 * Test Data
 *
 * These mock objects are used throughout the tests to simulate
 * database entities and request objects.
 */
// Mock entity with parsed JSON objects for internal use
const mockCarCodeConfig: CarCodeConfig = {
  car_code: 'TEST-001',
  site_object_id: 'SITE-001',
  monitoring_window: 5,
  enabled: true,
  thresholds: { threshold1: '10' },
  recommendation: { action: 'Test recommendation' },
  updated_at: new Date(),
  updated_by: 'test-user',
};

const createCarCaseRequest: CarCaseDAO = {
  site_object_id: 'SITE-001',
  sub_site_object_id: 'SUB-001',
  device_id: 'DEV-001',
  additional_device_info: { info: 'INFO-001' },
  car_code: 'TEST-001',
  source: 'test',
  title: 'Test Case',
  description: 'Test description',
  recommendation: { action: 'Test recommendation' },
  metadata: { daily_energy_loss: '100' },
  logging_date: new Date().toISOString(),
};

/**
 * CarCaseService Test Suite
 */
describe('CarCaseService', () => {
  let service: CarCaseService;
  let carCaseRepository: Repository<CarCase>;
  // CarCodeDefinition will be implemented later
  let carCodeConfigRepository: Repository<CarCodeConfig>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CarCaseService,
        {
          provide: getRepositoryToken(CarCase),
          useValue: {
            create: jest.fn().mockReturnValue(createMockCarCase()),
            save: jest.fn().mockResolvedValue(createMockCarCase()),
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
        // CarCodeDefinition provider will be implemented later
        {
          provide: getRepositoryToken(CarCodeConfig),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            query: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CarCaseService>(CarCaseService);
    carCaseRepository = module.get<Repository<CarCase>>(
      getRepositoryToken(CarCase),
    );
    // CarCodeDefinition repository will be implemented later
    carCodeConfigRepository = module.get<Repository<CarCodeConfig>>(
      getRepositoryToken(CarCodeConfig),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * Tests for createCarCase method
   *
   * These tests verify:
   * - Successful creation of a car case
   * - Error handling for missing car code definition
   * - Error handling for missing car code config
   * - Error handling for disabled car codes
   * - Case ID generation logic based on monitoring windows
   */
  describe('createCarCase', () => {
    it('should create a new car case successfully', async () => {
      // Mock repository methods
      // CarCodeDefinition check will be implemented later
      // Mock both findOne and find to return the config with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
      });
      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([
        {
          ...mockCarCodeConfig,
          thresholds: { threshold1: '10' },
          recommendation: { action: 'Test recommendation' },
        },
      ]);
      jest.spyOn(carCaseRepository, 'findOne').mockResolvedValue(null); // No existing case

      // Mock the save method to return a saved car case
      jest.spyOn(carCaseRepository, 'save').mockResolvedValue(
        createMockCarCase({
          id: 'saved-id',
          site_object_id: createCarCaseRequest.site_object_id,
          car_code: createCarCaseRequest.car_code,
          source: createCarCaseRequest.source,
          title: createCarCaseRequest.title,
        }),
      );

      const result = await service.createCarCase([createCarCaseRequest]);

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.length).toBe(1);

      // Use jest.spyOn to avoid ESLint warnings about unbound methods
      const findOneConfigSpy = jest.spyOn(carCodeConfigRepository, 'findOne');

      // Verify that findOne was called
      expect(findOneConfigSpy).toHaveBeenCalled();

      // In the updated implementation, we don't need to check if create and save were called
      // since we're mocking the entire process and just checking the result

      // Verify only the essential properties in the result
      expect(result.data?.[0]).toMatchObject({
        site_object_id: createCarCaseRequest.site_object_id,
        sub_site_object_id: createCarCaseRequest.sub_site_object_id,
        device_id: createCarCaseRequest.device_id,
        car_code: createCarCaseRequest.car_code,
        source: createCarCaseRequest.source,
        title: createCarCaseRequest.title,
        description: createCarCaseRequest.description,
      });
    });

    it('should proceed with warning if car code definition is not found', async () => {
      // Mock repository methods
      // CarCodeDefinition check will be implemented later
      // Mock both findOne and find to return the config with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
      });
      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([
        {
          ...mockCarCodeConfig,
          thresholds: { threshold1: '10' },
          recommendation: { action: 'Test recommendation' },
        },
      ]);
      jest.spyOn(carCaseRepository, 'findOne').mockResolvedValue(null); // No existing case

      // Create a properly typed mock CarCase object for the save method
      const mockSavedCarCase: Partial<CarCase> = {
        id: 'test-id',
        site_object_id: createCarCaseRequest.site_object_id,
        car_code: createCarCaseRequest.car_code,
        source: createCarCaseRequest.source,
        title: createCarCaseRequest.title,
        tags: 'test-tags',
        case_id: 'test-case-id',
        logging_date: new Date(createCarCaseRequest.logging_date),
      };

      jest
        .spyOn(carCaseRepository, 'save')
        .mockResolvedValue(mockSavedCarCase as CarCase);

      // Spy on the logger to check for warnings
      const warnSpy = jest.spyOn(service['logger'], 'warn');

      const result = await service.createCarCase([createCarCaseRequest]);

      // Should succeed despite missing car code definition
      expect(result.success).toBe(true);

      // Should have logged a warning
      expect(warnSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `Skipping car code definition check for car_code=${createCarCaseRequest.car_code}`,
        ),
      );
    });

    it('should return error if car code config is not found', async () => {
      // Mock repository methods
      // CarCodeDefinition check will be implemented later
      // Mock findOne to return null (not found)
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue(null);

      // Also mock find to return empty array (not found)
      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([]);

      const result = await service.createCarCase([createCarCaseRequest]);
      expect(result.success).toBe(false);
      expect(result.error).toContain(
        `Car code config for ${createCarCaseRequest.car_code} and ${createCarCaseRequest.site_object_id} not found`,
      );
    });

    it('should return error if car code is disabled', async () => {
      // Mock repository methods
      // CarCodeDefinition check will be implemented later
      // Mock findOne to return a disabled config with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        enabled: false,
      });

      // Also mock find in case it falls back to case-insensitive search
      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([
        {
          ...mockCarCodeConfig,
          thresholds: { threshold1: '10' },
          recommendation: { action: 'Test recommendation' },
          enabled: false,
        },
      ]);

      const result = await service.createCarCase([createCarCaseRequest]);
      expect(result.success).toBe(false);
      expect(result.error).toContain(
        `Car code ${createCarCaseRequest.car_code} is disabled for ${createCarCaseRequest.site_object_id}`,
      );
    });

    it('should use existing case ID if within monitoring window', async () => {
      // Create a recent case within the monitoring window
      const recentCase = createMockCarCase({
        logging_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      });

      // Mock repository methods with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        monitoring_window: 5, // 5 days window
      });

      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([
        {
          ...mockCarCodeConfig,
          thresholds: { threshold1: '10' },
          recommendation: { action: 'Test recommendation' },
          monitoring_window: 5, // 5 days window
        },
      ]);

      jest.spyOn(carCaseRepository, 'findOne').mockResolvedValue(recentCase);

      // Mock the save method to return the recent case with same case ID
      jest.spyOn(carCaseRepository, 'save').mockResolvedValue({
        ...recentCase,
        case_id: recentCase.case_id, // Should use the same case ID
      });

      const result = await service.createCarCase([createCarCaseRequest]);

      // Verify the case ID is the same as the recent case
      expect(result.success).toBe(true);
      expect(result.data?.[0].case_id).toEqual(recentCase.case_id);
    });

    it('should create new case ID if outside monitoring window', async () => {
      // Create an old case outside the monitoring window
      const oldCase = createMockCarCase({
        case_id: 'OLD-CASE-ID',
        logging_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      });

      // Mock Date.now() to return a fixed timestamp
      const fixedTimestamp = 1234567890000;
      jest.spyOn(Date, 'now').mockReturnValue(fixedTimestamp);

      // Mock repository methods with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        monitoring_window: 5, // 5 days window
      });
      jest.spyOn(carCodeConfigRepository, 'find').mockResolvedValue([
        {
          ...mockCarCodeConfig,
          thresholds: { threshold1: '10' },
          recommendation: { action: 'Test recommendation' },
          monitoring_window: 5, // 5 days window
        },
      ]);

      // Mock findOne to return the old case
      jest.spyOn(carCaseRepository, 'findOne').mockResolvedValue(oldCase);

      // Mock create to return a new case with a new ID
      jest.spyOn(carCaseRepository, 'create').mockImplementation((_data) => {
        const mockCase = createMockCarCase();
        mockCase.case_id = `CASE-${fixedTimestamp}`; // Should create a new case ID
        return mockCase;
      });

      const result = await service.createCarCase([createCarCaseRequest]);

      // Verify a new case ID was created
      // The implementation uses Date.now() for the case ID
      expect(result.success).toBe(true);
      // The implementation uses tags-counter format for case IDs
      expect(result.data?.[0].case_id).toContain(createCarCaseRequest.car_code);
      expect(result.data?.[0].case_id).not.toEqual(oldCase.case_id);
    });
  });

  // Tests for findCarCases method have been removed as the method is no longer used in the application

  // Tests for findCarCasesByIds method have been removed as the method is no longer used in the application

  // Tests for missing car code configuration have been removed as they were only relevant for the findCarCases method

  /**
   * Tests for batch processing of multiple car cases
   */
  describe('batch processing', () => {
    it('should process multiple car cases in a single batch', async () => {
      // Create multiple car case requests
      const carCase1 = {
        ...createCarCaseRequest,
        id: 'case-1',
        additional_device_info: { info: 'INFO-001' },
        recommendation: { action: 'Test recommendation' },
        metadata: { daily_energy_loss: '100' },
      };
      const carCase2 = {
        ...createCarCaseRequest,
        id: 'case-2',
        device_id: 'DEV-002',
        additional_device_info: { info: 'INFO-002' },
        recommendation: { action: 'Test recommendation 2' },
        metadata: { daily_energy_loss: '200' },
      };
      const carCase3 = {
        ...createCarCaseRequest,
        id: 'case-3',
        sub_site_object_id: 'SUB-002',
        additional_device_info: { info: 'INFO-003' },
        recommendation: { action: 'Test recommendation 3' },
        metadata: { daily_energy_loss: '300' },
      };

      // Mock repository methods
      jest
        .spyOn(carCodeConfigRepository, 'findOne')
        .mockResolvedValue(mockCarCodeConfig);

      // Mock findOne to return null for the first call (no existing case)
      const findOneSpy = jest.spyOn(carCaseRepository, 'findOne');
      findOneSpy.mockResolvedValueOnce(null);

      // Create mock saved entities
      const mockSavedCarCase1 = createMockCarCase({
        id: 'saved-id-1',
        case_id: 'test-case-id-1',
      });
      const mockSavedCarCase2 = createMockCarCase({
        id: 'saved-id-2',
        case_id: 'test-case-id-2',
      });
      const mockSavedCarCase3 = createMockCarCase({
        id: 'saved-id-3',
        case_id: 'test-case-id-1', // Reusing case ID for the same tags
      });

      // Mock save to return different values for each call
      const saveSpy = jest.spyOn(carCaseRepository, 'save');
      saveSpy.mockResolvedValueOnce(mockSavedCarCase1);
      saveSpy.mockResolvedValueOnce(mockSavedCarCase2);
      saveSpy.mockResolvedValueOnce(mockSavedCarCase3);

      // Call the service method with multiple car cases
      const result = await service.createCarCase([
        carCase1,
        carCase2,
        carCase3,
      ]);

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.length).toBe(3);

      // Verify that save was called three times
      expect(saveSpy).toHaveBeenCalledTimes(3);

      // Verify that the IDs were correctly assigned
      expect(result.data?.[0].id).toEqual('saved-id-1');
      expect(result.data?.[1].id).toEqual('saved-id-2');
      expect(result.data?.[2].id).toEqual('saved-id-3');
    });

    it('should reuse case IDs for car cases with the same tags', async () => {
      // Create two car cases with the same tags
      const carCase1 = {
        ...createCarCaseRequest,
        id: 'case-1',
        additional_device_info: { info: 'INFO-001' },
        recommendation: { action: 'Test recommendation' },
        metadata: { daily_energy_loss: '100' },
      };
      const carCase2 = {
        ...createCarCaseRequest,
        id: 'case-2',
        additional_device_info: { info: 'INFO-002' },
        recommendation: { action: 'Test recommendation 2' },
        metadata: { daily_energy_loss: '200' },
      };

      // Mock repository methods with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        monitoring_window: 5, // 5 days window
      });

      // Mock findOne to return null for the first call (no existing case)
      const findOneSpy = jest.spyOn(carCaseRepository, 'findOne');
      findOneSpy.mockResolvedValueOnce(null);

      // Create mock saved entities
      const mockSavedCarCase1 = createMockCarCase({
        id: 'saved-id-1',
        case_id: 'test-case-id-1',
        tags: 'test-tags',
      });
      const mockSavedCarCase2 = createMockCarCase({
        id: 'saved-id-2',
        case_id: 'test-case-id-1', // Same case ID
        tags: 'test-tags', // Same tags
      });

      // Mock save to return different values for each call
      const saveSpy = jest.spyOn(carCaseRepository, 'save');
      saveSpy.mockResolvedValueOnce(mockSavedCarCase1);
      saveSpy.mockResolvedValueOnce(mockSavedCarCase2);

      // Spy on the logger to check for reuse message
      const logSpy = jest.spyOn(service['logger'], 'log');

      // Call the service method with multiple car cases
      const result = await service.createCarCase([carCase1, carCase2]);

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.length).toBe(2);

      // Verify that both car cases have the same case ID
      // The actual case ID format is tags-counter, so we just need to verify they're the same
      const firstCaseId = result.data?.[0].case_id;
      const secondCaseId = result.data?.[1].case_id;
      expect(firstCaseId).toBeDefined();
      expect(secondCaseId).toBeDefined();
      expect(firstCaseId).toEqual(secondCaseId);

      // Verify that the log message about reusing case ID was called
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Reusing case ID'),
      );
    });
  });

  /**
   * Tests for error handling in saveCarCaseDAO
   */
  describe('saveCarCaseDAO error handling', () => {
    it('should handle database errors during save operation', async () => {
      // Mock repository methods with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        monitoring_window: 5, // 5 days window
      });

      // Mock the save method to throw an error
      jest
        .spyOn(carCaseRepository, 'save')
        .mockRejectedValue(new Error('Database connection failed'));

      // Call the service method
      const result = await service.createCarCase([createCarCaseRequest]);

      // Verify the result
      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
    });
  });

  /**
   * Tests for normalization of input values
   */
  describe('input value normalization', () => {
    // Helper function to test normalization
    async function testNormalization(
      input: Partial<CarCaseDAO>,
      expected: { car_code: string; site_object_id: string },
    ) {
      // Mock repository methods with proper JSON objects
      jest.spyOn(carCodeConfigRepository, 'findOne').mockResolvedValue({
        ...mockCarCodeConfig,
        thresholds: { threshold1: '10' },
        recommendation: { action: 'Test recommendation' },
        monitoring_window: 5, // 5 days window
      });

      // Mock the save method to verify normalized values
      const saveSpy = jest
        .spyOn(carCaseRepository, 'save')
        .mockImplementation((entity) => {
          // Verify the normalized values
          expect(entity.car_code).toEqual(expected.car_code);
          expect(entity.site_object_id).toEqual(expected.site_object_id);

          // Return the saved data with normalized fields
          const mockCase = createMockCarCase();
          mockCase.id = 'saved-id';
          mockCase.car_code = entity.car_code || '';
          mockCase.site_object_id = entity.site_object_id || '';
          return Promise.resolve(mockCase);
        });

      // Call the service method with input containing whitespace
      const result = await service.createCarCase([
        { ...createCarCaseRequest, ...input },
      ]);

      // Verify the result
      expect(result.success).toBe(true);
      expect(saveSpy).toHaveBeenCalled();
    }

    it('should trim and convert car_code and site_object_id to lowercase', async () => {
      await testNormalization(
        { car_code: '  TEST-CODE  ', site_object_id: '  SITE-001  ' },
        { car_code: 'test-code', site_object_id: 'site-001' },
      );
    });

    it('should handle whitespace in all input values', async () => {
      await testNormalization(
        {
          car_code: '  TEST-CODE  ',
          site_object_id: '  SITE-001  ',
          sub_site_object_id: '  SUB-001  ',
          device_id: '  DEV-001  ',
        },
        { car_code: 'test-code', site_object_id: 'site-001' },
      );
    });
  });
});
