-- Create schema if not exists
CREATE SCHEMA IF NOT EXISTS carwrapper;

-- CAR_CASES Table
DROP TABLE IF EXISTS carwrapper.car_cases CASCADE;
CREATE TABLE carwrapper.car_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_object_id VARCHAR(255) NOT NULL,
    sub_site_object_id VARCHAR(255),
    device_id VARCHAR(255),
    additional_device_info JSON,
    tags VARCHAR(255) NOT NULL,
    case_id VARCHAR(255) NOT NULL,
    car_code VARCHAR(255) NOT NULL,
    source VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    remarks TEXT,
    recommendation JSON,
    metadata JSON,
    logging_date TIMESTAMP NOT NULL
);

-- CAR_CODE_CONFIG Table
DROP TABLE IF EXISTS carwrapper.car_code_config CASCADE;
CREATE TABLE carwrapper.car_code_config (
    site_object_id VARCHAR(255) NOT NULL,
    car_code VARCHAR(255) NOT NULL,
    monitoring_window INTEGER NOT NULL,
    enabled BOOLEAN NOT NULL,
    thresholds JSON,
    recommendation JSON,
    updated_at TIMESTAMP NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (site_object_id, car_code)
);

-- CAR_CODE_ALARM_CONFIG Table
DROP TABLE IF EXISTS carwrapper.car_code_alarm_config CASCADE;
CREATE TABLE carwrapper.car_code_alarm_config (
    car_code VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(255),
    device_type VARCHAR(255),
    should_raise_alarm BOOLEAN NOT NULL,
    updated_at TIMESTAMP,
    updated_by VARCHAR(255)
);

-- CAR_CODE_ALARM_CONFIG_HISTORY Table
DROP TABLE IF EXISTS carwrapper.car_code_alarm_config_history CASCADE;
CREATE TABLE carwrapper.car_code_alarm_config_history (
    car_code VARCHAR(255) NOT NULL,
    old_value TEXT NOT NULL,
    new_value TEXT NOT NULL,
    transaction_type VARCHAR(32) NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (car_code, updated_at)
);

-- CAR_CODE_CONFIG_HISTORY Table
DROP TABLE IF EXISTS carwrapper.car_code_config_history CASCADE;
CREATE TABLE carwrapper.car_code_config_history (
    car_code VARCHAR(255) NOT NULL,
    site_object_id VARCHAR(255) NOT NULL,
    old_value TEXT NOT NULL,
    new_value TEXT NOT NULL,
    transaction_type VARCHAR(32) NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (car_code, site_object_id, updated_at)
);