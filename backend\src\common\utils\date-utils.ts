import { Between } from 'typeorm';
import { DEFAULT_START_DATE } from '../constants';

/**
 * Returns a string representing which date filter case applies.
 * - 'both': both start and end dates are provided
 * - 'startOnly': only start date is provided
 * - 'endOnly': only end date is provided
 * - 'none': neither start nor end date is provided
 *
 * @param start Optional start date string
 * @param end Optional end date string
 */
export function getDateFilterCase(
  start?: string,
  end?: string,
): 'both' | 'startOnly' | 'endOnly' | 'none' {
  if (start && end) return 'both';
  if (start) return 'startOnly';
  if (end) return 'endOnly';
  return 'none';
}

/**
 * Returns a TypeORM Between clause or undefined for generic date filtering.
 * Throws an error if the date(s) are invalid.
 */
export function buildDateFilter(
  start?: string,
  end?: string,
): ReturnType<typeof Between> | undefined {
  switch (getDateFilterCase(start, end)) {
    case 'both': {
      const startDate = new Date(start!);
      const endDate = new Date(end!);
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Invalid date format. Use ISO 8601 format.');
      }
      return Between(startDate, endDate);
    }
    case 'startOnly': {
      const startDate = new Date(start!);
      if (isNaN(startDate.getTime())) {
        throw new Error('Invalid start date format.');
      }
      return Between(startDate, new Date());
    }
    case 'endOnly': {
      const endDate = new Date(end!);
      if (isNaN(endDate.getTime())) {
        throw new Error('Invalid end date format.');
      }
      return Between(DEFAULT_START_DATE, endDate);
    }
    case 'none':
    default:
      return undefined;
  }
}
