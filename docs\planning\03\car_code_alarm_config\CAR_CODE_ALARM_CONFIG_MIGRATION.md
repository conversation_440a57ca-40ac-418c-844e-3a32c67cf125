# CAR CODE ALARM CONFIG Migration

This document outlines the migration of car code alarm configuration for NZOS-CarWrapperService.

- OLD DESIGN: alarm is raised every time monitoring window is the trigger to raise alarm
- NEW DESIGN: only `first occurrence` or `reoccurrence + within monitoring window` then alarm will be raised

## Table Mapping: CAR_DEFINITION -> CAR_CODE_ALARM_CONFIG

| OLD                      | NEW         | Migrating |
|--------------------------|-------------|-----------|
| car_code                 | car_code    | yes       |
| name                     | name        | yes       |
| category                 | category    | yes       |
| isRecoverable            | -           | no        | 
| isLossless               | -           | no        |
| idle_days_to_auto_close  | -           | no        |
| auto_close_actual_action | -           | no        |
| auto_close_root_cause    | -           | no        |
| auto_close_remarks       | -           | no        |
| auto_close_cat_id        | -           | no        |
| dev_type                 | device_type | yes       |
| updated_at               | updated_at  | yes       |

## Migration Script for `CAR CODE ALARM CONFIG`

```sql
INSERT INTO carwrapper.car_code_alarm_config (
    car_code,
    name,
    description,
    category,
    device_type,
    should_raise_alarm,
    updated_at,
    updated_by
)
SELECT
    cd.car_code,
    cd.name,
    NULL AS description, -- description is not in CAR_DEFINITION, set to NULL
    cd.category,
    cd.dev_type AS device_type, -- map dev_type to device_type
    TRUE AS should_raise_alarm, -- based on new design, default to true for initial migration
    NOW() AS updated_at, -- Set updated_at to the current timestamp as current table does not have this column
    'migration-script' AS updated_by -- Indicate the update was done by the migration script as current table does not have this column
FROM
    carwrapper.car_definition cd;
```
