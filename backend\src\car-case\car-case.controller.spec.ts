/**
 * Unit tests for the CarCaseController
 *
 * These tests verify that the controller correctly delegates to the service
 * and returns the expected responses. The service is mocked to isolate the controller.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { AclService } from '../client/acl/acl.service';
import { AclGuard } from '../client/acl/acl.guard';
import { CarCaseController } from './car-case.controller';
import { CarCaseService } from './car-case.service';
import { CarCaseConsoService } from './car-case-conso.service';
import {
  CreateCarCaseDAORequest,
  FindCarCasesRequest,
  FindCarCasesByIdsRequest,
  CarCaseDAO,
} from './interfaces';
import { CarCaseConsoDAO } from './interfaces/car-case-conso.interface';

/**
 * Test Data
 *
 * Mock response objects used throughout the tests.
 */
const mockCarCaseData: CarCaseDAO = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  site_object_id: 'SITE-001',
  sub_site_object_id: 'SUB-001',
  device_id: 'DEV-001',
  additional_device_info: { info: 'INFO-001' },
  tags: 'TEST-001-SITE-001-SUB-001-DEV-001',
  case_id: 'CASE-1234567890',
  car_code: 'TEST-001',
  source: 'test',
  title: 'Test Case',
  description: 'Test description',
  recommendation: { action: 'Test recommendation' },
  metadata: { daily_energy_loss: '100' },
  logging_date: new Date().toISOString(),
};

const mockCarCaseConsoData: CarCaseConsoDAO = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  conso_id: ['123e4567-e89b-12d3-a456-426614174000'],
  site_object_id: 'SITE-001',
  sub_site_object_id: 'SUB-001',
  device_id: 'DEV-001',
  additional_device_info: { info: 'INFO-001' },
  tags: 'TEST-001-SITE-001-SUB-001-DEV-001',
  case_id: 'CASE-1234567890',
  car_code: 'TEST-001',
  source: 'test',
  title: 'Test Case',
  description: 'Test description',
  recommendation: { action: 'Test recommendation' },
  metadata: { daily_energy_loss: '100' },
  logging_date: new Date().toISOString(),
  occurrence_count: 1,
  first_occurrence: new Date().toISOString(),
  most_recent_occurrence: new Date().toISOString(),
  status: 'open',
};

// Expected response formats are created inline in the tests

/**
 * CarCaseController Test Suite
 */
describe('CarCaseController', () => {
  let controller: CarCaseController;
  let service: CarCaseService;
  let consoService: CarCaseConsoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CarCaseController],
      providers: [
        {
          provide: AclService,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: AclGuard,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: CarCaseService,
          useValue: {
            createCarCase: jest
              .fn()
              .mockResolvedValue({ success: true, data: [mockCarCaseData] }),
          },
        },
        {
          provide: CarCaseConsoService,
          useValue: {
            findCarCases: jest.fn().mockResolvedValue({
              success: true,
              data: [mockCarCaseConsoData],
            }),
            findCarCasesByIds: jest.fn().mockResolvedValue({
              success: true,
              data: [mockCarCaseConsoData],
            }),
            convertToCarCaseConsoDAO: jest
              .fn()
              .mockReturnValue(mockCarCaseConsoData),
          },
        },
      ],
    }).compile();

    controller = module.get<CarCaseController>(CarCaseController);
    service = module.get<CarCaseService>(CarCaseService);
    consoService = module.get<CarCaseConsoService>(CarCaseConsoService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /**
   * Tests for pingCarCase method
   *
   * Verifies that the controller returns a valid ping response.
   */
  describe('pingCarCase', () => {
    it('should return a ping response', () => {
      const result = controller.pingCarCase();
      expect(result).toEqual({ message: 'Car Case Service is running' });
    });
  });

  /**
   * Tests for createCarCase method
   *
   * Verifies that the controller correctly delegates to the service
   * and returns the service's response.
   */
  describe('createCarCase', () => {
    it('should allow append one new car case', async () => {
      const singleRequest: CarCaseDAO = {
        site_object_id: 'SITE-001',
        sub_site_object_id: 'SUB-001',
        device_id: 'DEV-001',
        additional_device_info: { info: 'INFO-001' },
        car_code: 'TEST-001',
        source: 'test',
        title: 'Test Case',
        description: 'Test description',
        recommendation: { action: 'Test recommendation' },
        metadata: { daily_energy_loss: '100', key: 'value' },
        logging_date: new Date().toISOString(),
      };

      // Wrap the single request in a CreateCarCasesRequest
      const request: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [singleRequest],
      };

      // Mock the consoService.findCarCasesByIds to return the expected consolidated data
      jest.spyOn(consoService, 'findCarCasesByIds').mockResolvedValue({
        success: true,
        data: [mockCarCaseConsoData],
      });

      const result = await controller.createCarCase(request);

      const createCarCaseSpy = jest.spyOn(service, 'createCarCase');
      expect(createCarCaseSpy).toHaveBeenCalledWith([singleRequest]);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 201,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should handle an array of car cases (batch insert)', async () => {
      // Create a second mock car case data with different values
      const mockCarCaseData2: CarCaseDAO = {
        id: '223e4567-e89b-12d3-a456-426614174001',
        site_object_id: 'SITE-002',
        sub_site_object_id: 'SUB-002',
        device_id: 'DEV-002',
        additional_device_info: { info: 'INFO-002' },
        tags: 'TEST-002-SITE-002-SUB-002-DEV-002',
        case_id: 'CASE-9876543210',
        car_code: 'TEST-002',
        source: 'test2',
        title: 'Test Case 2',
        description: 'Test description 2',
        recommendation: { action: 'Test recommendation 2' },
        metadata: { daily_energy_loss: '200' },
        logging_date: new Date().toISOString(),
      };

      // Create a second mock consolidated car case data
      const mockCarCaseConsoData2: CarCaseConsoDAO = {
        id: '223e4567-e89b-12d3-a456-426614174001',
        conso_id: ['223e4567-e89b-12d3-a456-426614174001'],
        site_object_id: 'SITE-002',
        sub_site_object_id: 'SUB-002',
        device_id: 'DEV-002',
        additional_device_info: { info: 'INFO-002' },
        tags: 'TEST-002-SITE-002-SUB-002-DEV-002',
        case_id: 'CASE-9876543210',
        car_code: 'TEST-002',
        source: 'test2',
        title: 'Test Case 2',
        description: 'Test description 2',
        recommendation: { action: 'Test recommendation 2' },
        metadata: { daily_energy_loss: '200' },
        logging_date: new Date().toISOString(),
        occurrence_count: 1,
        first_occurrence: new Date().toISOString(),
        most_recent_occurrence: new Date().toISOString(),
        status: 'open',
      };

      // Create a batch request with multiple car cases
      const batchRequest: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [
          {
            site_object_id: 'SITE-001',
            sub_site_object_id: 'SUB-001',
            device_id: 'DEV-001',
            additional_device_info: { info: 'INFO-001' },
            car_code: 'TEST-001',
            source: 'test',
            title: 'Test Case',
            description: 'Test description',
            recommendation: { action: 'Test recommendation' },
            metadata: { daily_energy_loss: '100', key: 'value' },
            logging_date: new Date().toISOString(),
          },
          {
            site_object_id: 'SITE-002',
            sub_site_object_id: 'SUB-002',
            device_id: 'DEV-002',
            additional_device_info: { info: 'INFO-002' },
            car_code: 'TEST-002',
            source: 'test2',
            title: 'Test Case 2',
            description: 'Test description 2',
            recommendation: { action: 'Test recommendation 2' },
            metadata: { daily_energy_loss: '200' },
            logging_date: new Date().toISOString(),
          },
        ],
      };

      // Mock the service to process the batch request and return both car cases
      jest.spyOn(service, 'createCarCase').mockImplementationOnce(() =>
        Promise.resolve({
          success: true,
          data: [mockCarCaseData, mockCarCaseData2],
        }),
      );

      // Mock the consoService to return the consolidated data
      jest.spyOn(consoService, 'findCarCasesByIds').mockResolvedValue({
        success: true,
        data: [mockCarCaseConsoData, mockCarCaseConsoData2],
      });

      // Process the batch request
      const result = await controller.createCarCase(batchRequest);

      // Verify the result is a single response with both car cases
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 201,
          message: [mockCarCaseConsoData, mockCarCaseConsoData2],
        },
      });

      // Verify the service was called with the batch of requests
      const createCarCaseSpy = jest.spyOn(service, 'createCarCase');
      expect(createCarCaseSpy).toHaveBeenCalledWith(batchRequest.requests);
      expect(createCarCaseSpy).toHaveBeenCalledTimes(1);
    });

    it('should reject the entire batch if any request is invalid', async () => {
      // Create a batch request with one valid and one invalid request
      const batchRequest: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [
          {
            site_object_id: 'SITE-001',
            sub_site_object_id: 'SUB-001',
            device_id: 'DEV-001',
            additional_device_info: { info: 'INFO-001' },
            car_code: 'TEST-001',
            source: 'test',
            title: 'Test Case',
            description: 'Test description',
            recommendation: { action: 'Test recommendation' },
            metadata: { daily_energy_loss: '100', key: 'value' },
            logging_date: new Date().toISOString(),
          },
          {
            // Missing required fields: site_object_id, title
            site_object_id: '', // Empty string to trigger validation error
            car_code: 'TEST-002',
            source: 'test2',
            title: '', // Empty string to trigger validation error
            description: 'Test description 2',
            recommendation: { action: 'Test recommendation 2' },
            metadata: { daily_energy_loss: '200' },
            logging_date: new Date().toISOString(),
          } as CarCaseDAO,
        ],
      };

      // Process the batch request
      const result = await controller.createCarCase(batchRequest);

      // Verify the result is an error response with missing fields
      expect(result).toEqual({
        error: {
          status: 'ERROR: Unprocessable Entity - semantic errors',
          code: 422, // Unprocessable Entity
          message: [],
          fields: ['site_object_id'],
          error:
            'Validation failed for car case 2/2: Missing required fields: site_object_id',
        },
      });

      // Verify the service was not called at all
      const createCarCaseSpy = jest.spyOn(service, 'createCarCase');
      expect(createCarCaseSpy).not.toHaveBeenCalled();
    });

    it('should return 404 Not Found if no requests are provided', async () => {
      // Create an empty batch request
      const emptyRequest: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [],
      };

      // Process the empty request
      const result = await controller.createCarCase(emptyRequest);

      // Verify the result is an error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Not Found - no resources to process',
          code: 404, // Not Found
          message: [],
          fields: [],
          error: 'No car case requests provided in the batch request',
        },
      });

      // Verify the service was not called
      const createCarCaseSpy = jest.spyOn(service, 'createCarCase');
      expect(createCarCaseSpy).not.toHaveBeenCalled();
    });
  });

  /**
   * Tests for findCarCases method
   *
   * Verifies that the controller correctly delegates to the service
   * with the provided filters and returns the service's response.
   */
  describe('findCarCases', () => {
    it('should find car cases with filters', async () => {
      const request: FindCarCasesRequest = {
        site_object_id: 'SITE-001',
        car_code: 'TEST-001',
        start_date: '2023-01-01T00:00:00Z',
        end_date: '2023-12-31T23:59:59Z',
      };

      const result = await controller.findCarCases(request);

      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should handle empty request object', async () => {
      const result = await controller.findCarCases({});

      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith({});
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should handle null or undefined request', async () => {
      const result = await controller.findCarCases({});

      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith({});
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });
  });

  /**
   * Tests for findCarCasesByIds method
   *
   * Verifies that the controller correctly delegates to the service
   * with the provided IDs and returns the service's response.
   */
  describe('findCarCasesByIds', () => {
    it('should find car cases by IDs', async () => {
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['123e4567-e89b-12d3-a456-426614174000'],
        limit: 2,
        offset: 0,
      };

      const result = await controller.findCarCasesByIds(request);

      const findCarCasesByIdsSpy = jest.spyOn(
        consoService,
        'findCarCasesByIds',
      );
      expect(findCarCasesByIdsSpy).toHaveBeenCalledWith(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should find car cases by IDs with ASC sort order', async () => {
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['123e4567-e89b-12d3-a456-426614174000'],
        sort_order: 'ASC',
      };

      const result = await controller.findCarCasesByIds(request);

      const findCarCasesByIdsSpy = jest.spyOn(
        consoService,
        'findCarCasesByIds',
      );
      expect(findCarCasesByIdsSpy).toHaveBeenCalledWith(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should find car cases by IDs with DESC sort order', async () => {
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['123e4567-e89b-12d3-a456-426614174000'],
        sort_order: 'DESC',
      };

      const result = await controller.findCarCasesByIds(request);

      const findCarCasesByIdsSpy = jest.spyOn(
        consoService,
        'findCarCasesByIds',
      );
      expect(findCarCasesByIdsSpy).toHaveBeenCalledWith(request);
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });

    it('should handle case-insensitive sort order', async () => {
      // Use a valid sort order and test the normalization in the controller implementation
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['123e4567-e89b-12d3-a456-426614174000'],
        sort_order: 'ASC', // Use valid uppercase value
      };

      // Mock the service to simulate case-insensitive handling
      const spy = jest
        .spyOn(consoService, 'findCarCasesByIds')
        .mockImplementation((req) => {
          expect(req.ids).toEqual(request.ids);
          expect(req.sort_order).toEqual('ASC');
          return Promise.resolve({
            success: true,
            data: [mockCarCaseConsoData],
          });
        });

      const result = await controller.findCarCasesByIds(request);

      // Verify the service was called
      expect(spy).toHaveBeenCalledWith(request);

      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [mockCarCaseConsoData],
        },
      });
    });
  });

  /**
   * Tests for input validation
   */
  describe('input validation', () => {
    it('should validate required fields in createCarCase', async () => {
      // Create a request with missing required fields
      const invalidRequest: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [
          {
            // Missing site_object_id, car_code, source, and logging_date
            title: 'Test Case',
            description: 'Test description',
          } as CarCaseDAO,
        ],
      };

      // Process the invalid request
      const result = await controller.createCarCase(invalidRequest);

      // Verify the result is an error response with all missing fields listed
      expect(result).toEqual({
        error: {
          status: 'ERROR: Unprocessable Entity - semantic errors',
          code: 422,
          message: [],
          fields: ['site_object_id', 'car_code', 'source', 'logging_date'],
          error: expect.stringContaining('Missing required fields') as string,
        },
      });

      // Verify the service was not called
      const createCarCaseSpy = jest.spyOn(service, 'createCarCase');
      expect(createCarCaseSpy).not.toHaveBeenCalled();
    });

    it('should validate date formats in findCarCases', async () => {
      // Mock the service to return an error for invalid date format
      jest.spyOn(consoService, 'findCarCases').mockResolvedValueOnce({
        success: false,
        error: 'Invalid date format: start_date',
        data: [],
      });

      // Create a request with invalid date format
      const invalidRequest: FindCarCasesRequest = {
        start_date: 'invalid-date',
        end_date: '2023-12-31',
      };

      // Process the invalid request
      const result = await controller.findCarCases(invalidRequest);

      // Verify the result is an error response
      expect(result).toEqual({
        error: {
          status: 'ERROR: Bad Request - error finding car cases',
          code: 400,
          message: [JSON.stringify(invalidRequest)],
          fields: [],
          error: 'Invalid date format: start_date',
        },
      });
    });
  });

  /**
   * Tests for error response formatting
   */
  describe('error response formatting', () => {
    it('should format service errors correctly', async () => {
      // Mock the service to return an error
      jest.spyOn(service, 'createCarCase').mockResolvedValueOnce({
        success: false,
        error: 'Service error message',
      });

      // Create a valid request
      const request: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [
          {
            site_object_id: 'SITE-001',
            car_code: 'TEST-001',
            source: 'test',
            logging_date: new Date().toISOString(),
            title: 'Test Case',
          } as CarCaseDAO,
        ],
      };

      // Process the request
      const result = await controller.createCarCase(request);

      // Verify the result is a properly formatted error response
      expect(result).toHaveProperty('error');
      expect(result.error).toHaveProperty('status');
      expect(result.error).toHaveProperty('code');
      expect(result.error).toHaveProperty('message');
      expect(result.error).toHaveProperty('fields');
      expect(result.error).toHaveProperty('error');
      expect(result.error?.error).toEqual('Service error message');
    });

    it('should include field names in validation error responses', async () => {
      // Create a request with missing required fields
      const invalidRequest: CreateCarCaseDAORequest = {
        user_id: 'test-user',
        requests: [
          {
            // Missing car_code and source
            site_object_id: 'SITE-001',
            logging_date: new Date().toISOString(),
            title: 'Test Case',
          } as CarCaseDAO,
        ],
      };

      // Process the invalid request
      const result = await controller.createCarCase(invalidRequest);

      // Verify the result includes the field names in the fields array
      expect(result).toHaveProperty('error');
      expect(result.error?.fields).toContain('car_code');
      expect(result.error?.fields).toContain('source');
    });
  });

  /**
   * Tests for sort order normalization
   */
  describe('sort order normalization', () => {
    it('should normalize sort order to uppercase', async () => {
      // Create a request with lowercase sort order
      const request: FindCarCasesRequest = {
        sort_order: 'asc' as 'ASC' | 'DESC', // Lowercase
      };

      // Process the request
      await controller.findCarCases(request);

      // Verify the service was called with normalized sort order
      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith(
        expect.objectContaining({ sort_order: 'ASC' }),
      );
    });

    it('should handle invalid sort order values', async () => {
      // Create a request with invalid sort order
      const request: FindCarCasesRequest = {
        sort_order: 'INVALID' as unknown as 'ASC' | 'DESC',
      };

      // Process the request
      await controller.findCarCases(request);

      // Verify the service was called with the normalized sort order (default is DESC)
      // The normalization function in the controller converts invalid values to the default
      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith(
        expect.objectContaining({ sort_order: 'DESC' }),
      );
    });
  });

  /**
   * Tests for empty result sets
   */
  describe('empty result sets', () => {
    it('should handle empty result sets from findCarCases', async () => {
      // Mock the service to return an empty result set
      jest.spyOn(consoService, 'findCarCases').mockResolvedValueOnce({
        success: true,
        data: [],
      });

      // Process a request
      const result = await controller.findCarCases({});

      // Verify the result is a success response with an empty message array
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [],
        },
      });
    });

    it('should handle empty result sets from findCarCasesByIds', async () => {
      // Mock the service to return an empty result set
      jest.spyOn(consoService, 'findCarCasesByIds').mockResolvedValueOnce({
        success: true,
        data: [],
      });

      // Process a request for a single ID
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['non-existent-id'],
      };

      // Process the request
      const result = await controller.findCarCasesByIds(request);

      // Verify the result is a 404 error response for a single ID
      expect(result).toEqual({
        error: {
          status: 'ERROR: Not Found - car case not found',
          code: 404,
          message: [JSON.stringify({ ids: ['non-existent-id'] })],
          fields: [],
          error: 'Car case with ID non-existent-id not found',
        },
      });
    });

    it('should return success with empty array for multiple IDs not found', async () => {
      // Mock the service to return an empty result set
      jest.spyOn(consoService, 'findCarCasesByIds').mockResolvedValueOnce({
        success: true,
        data: [],
      });

      // Process a request for multiple IDs
      const request: FindCarCasesByIdsRequest = {
        user_id: 'test-user',
        ids: ['id1', 'id2'],
      };

      // Process the request
      const result = await controller.findCarCasesByIds(request);

      // Verify the result is a success response with an empty message array
      // (not a 404 error, since we're looking for multiple IDs)
      expect(result).toEqual({
        success: {
          status: 'success',
          code: 200,
          message: [],
        },
      });
    });
  });

  /**
   * Tests for handling of null/undefined requests
   */
  describe('handling of null/undefined requests', () => {
    it('should handle null request in findCarCases', async () => {
      // Process a null request
      const result = await controller.findCarCases(
        null as unknown as FindCarCasesRequest,
      );

      // Verify the service was called with an empty object
      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith({});

      // Verify the result is a success response
      expect(result).toHaveProperty('success');
    });

    it('should handle undefined request in findCarCases', async () => {
      // Process an undefined request
      const result = await controller.findCarCases({});

      // Verify the service was called with an empty object
      const findCarCasesSpy = jest.spyOn(consoService, 'findCarCases');
      expect(findCarCasesSpy).toHaveBeenCalledWith({});

      // Verify the result is a success response
      expect(result).toHaveProperty('success');
    });

    it('should reject undefined request in findCarCasesByIds', async () => {
      // Process an undefined request
      // This should not happen in practice due to gRPC validation,
      // but we test it for completeness

      // Mock the controller's implementation to handle undefined request
      // We need to do this because the controller tries to access request.ids.length
      // which would throw an error with undefined
      const mockImplementation = jest.spyOn(controller, 'findCarCasesByIds');
      mockImplementation.mockImplementationOnce(() => {
        return Promise.resolve({
          error: {
            status: 'ERROR: Bad Request - invalid request',
            code: 400,
            message: [],
            fields: [],
            error: 'Invalid request: request is undefined',
          },
        });
      });

      const result = await controller.findCarCasesByIds(
        undefined as unknown as FindCarCasesByIdsRequest,
      );

      // Verify the result is an error response
      expect(result).toHaveProperty('error');
      expect(result.error?.code).toEqual(400);
    });
  });
});
