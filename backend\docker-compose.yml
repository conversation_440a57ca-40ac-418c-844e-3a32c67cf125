# Docker Compose file for production environment
#
# This file defines the production services for the Car Wrapper Service:
# - PostgreSQL database for data storage
# - NestJS gRPC microservice for the application
#
# Container naming convention:
# - Production containers have a '_prod' suffix (e.g., nestjs_carwrapperservice_prod)
# - Development containers have no suffix (e.g., nestjs_carwrapperservice)
# This helps distinguish between production and development containers when both are running.
services:
  # PostgreSQL database for storing car wrapper data
  postgresdb:
    image: postgres:latest
    container_name: nestjs_postgresdb
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: user
      POSTGRES_PASSWORD: root_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/db/init.sql:/docker-entrypoint-initdb.d/01-init.sql
    networks:
      - backend_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # NestJS gRPC microservice for handling car wrapper operations
  car-wrapper-service-backend-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nestjs_carwrapperservice_prod
    ports:
      - "8081:8081"
    # Removed volume mounts to prevent overwriting the built files
    volumes:
      - ./logs:/usr/src/app/logs
    # Use the .env.docker file for environment variables
    # This ensures all variables are in one place and reduces duplication
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - DB_HOST=postgresdb  # Must point to the service name in docker-compose
      - DOCKER_ENVIRONMENT=true
      - ENV=production
    networks:
      - backend_network
    depends_on:
      postgresdb:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
    name: nestjs_postgres_data

networks:
  backend_network:
    name: nestjs_backend_network
    driver: bridge