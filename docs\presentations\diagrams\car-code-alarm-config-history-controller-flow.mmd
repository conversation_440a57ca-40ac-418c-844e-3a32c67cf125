sequenceDiagram
    title Car Code Alarm Config History Controller Flow Diagram
    
    %% Actors and Components
    actor Client
    participant CarCodeAlarmConfigHistoryController
    participant CarCodeAlarmConfigHistoryService
    participant CarCodeAlarmConfigHistoryRepository
    participant Database
    
    %% Note on Architecture
    Note over CarCodeAlarmConfigHistoryController,CarCodeAlarmConfigHistoryService: History controllers are read-only and provide access to audit logs
    Note over CarCodeAlarmConfigHistoryService: Provides access to historical records of all configuration changes
    
    %% Main Flow - Find History Records
    
    Client->>CarCodeAlarmConfigHistoryController: FindCarCodeAlarmConfigHistory(request)
    Note over CarCodeAlarmConfigHistoryController: Validate and normalize request parameters
    
    %% Normalize sort order
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: normalizeSortOrder(request.sort_order)
    
    %% Prepare query parameters
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: Prepare queryParams object
    Note over CarCodeAlarmConfigHistoryController: Extract car_code, start_date, end_date, sort_order, limit
    
    %% Call service method
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryService: findCarCodeAlarmConfigsHistory(queryParams)
    
    %% Build query conditions
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: Build where conditions
    Note over CarCodeAlarmConfigHistoryService: Apply car_code filter if provided
    
    %% Apply date filtering
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: applyDateFiltering(start_date, end_date)
    Note over CarCodeAlarmConfigHistoryService: Create date range filter if dates provided
    
    %% Execute query
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryRepository: find(where, order, take)
    CarCodeAlarmConfigHistoryRepository->>Database: SELECT * FROM CAR_CODE_ALARM_CONFIG_HISTORY
    Note over Database: Filter by car_code, date range
    Note over Database: Order by updated_at
    Note over Database: Limit results
    
    %% Return results
    Database-->>CarCodeAlarmConfigHistoryRepository: History Records
    CarCodeAlarmConfigHistoryRepository-->>CarCodeAlarmConfigHistoryService: History Records
    
    %% Map to DAO objects
    CarCodeAlarmConfigHistoryService->>CarCodeAlarmConfigHistoryService: mapCarCodeAlarmConfigHistoryToDAO(historyRecords)
    Note over CarCodeAlarmConfigHistoryService: Convert database entities to DAO objects
    Note over CarCodeAlarmConfigHistoryService: Ensure updated_at is formatted as ISO string
    
    %% Return service response
    CarCodeAlarmConfigHistoryService-->>CarCodeAlarmConfigHistoryController: ServiceResponse(CarCodeAlarmConfigHistoryDAO array)
    
    %% Convert to controller response
    CarCodeAlarmConfigHistoryController->>CarCodeAlarmConfigHistoryController: Convert to CarCodeAlarmConfigHistoryResponse
    
    %% Return response to client
    CarCodeAlarmConfigHistoryController-->>Client: CarCodeAlarmConfigHistoryResponse
